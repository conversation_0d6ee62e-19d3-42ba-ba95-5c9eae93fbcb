{"": {"const-values": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master.d", "diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/NeuroLoopModels-master.swiftdeps"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopModels/Affirmation.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/Affirmation.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/Affirmation.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/Affirmation.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopModels/DTOs.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/DTOs.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/DTOs.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/DTOs.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopModels/RepetitionCycle.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/RepetitionCycle.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/RepetitionCycle.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopModels.build/Objects-normal/arm64/RepetitionCycle.o"}}