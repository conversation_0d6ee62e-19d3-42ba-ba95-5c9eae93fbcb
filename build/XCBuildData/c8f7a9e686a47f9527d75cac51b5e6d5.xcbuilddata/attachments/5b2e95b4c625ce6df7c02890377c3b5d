/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation/AffirmationProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation/AffirmationRepositoryProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationConstants.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationError.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationSortOption.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationStatistics.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AudioFileManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/DataExportServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/HomeViewModelProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Journal/JournalRepositoryProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/LibraryViewModelProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/NotificationServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/PurchaseManagerProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/RepetitionServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioRecordingError.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioRecordingServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/PremiumServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/ServiceErrors.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/SpeechRecognitionError.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/StreakCalendarDay.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/StreakServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/SyncServiceProtocol.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/ThemeManaging.swift
