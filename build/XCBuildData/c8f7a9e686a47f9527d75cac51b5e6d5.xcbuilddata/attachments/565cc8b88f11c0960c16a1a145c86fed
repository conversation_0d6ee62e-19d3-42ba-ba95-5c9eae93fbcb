{"": {"const-values": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master.d", "diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuroLoopUI-master.swiftdeps"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AffirmationProgressIndicator.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationProgressIndicator.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationProgressIndicator.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationProgressIndicator.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AffirmationTextView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationTextView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationTextView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationTextView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AudioWaveformView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveformView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveformView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveformView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/ActionButtonStyle.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ActionButtonStyle.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ActionButtonStyle.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ActionButtonStyle.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/FilterChip.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FilterChip.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FilterChip.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FilterChip.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/PlayButton.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PlayButton.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PlayButton.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PlayButton.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/QuickActionButton.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/QuickActionButton.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/QuickActionButton.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/QuickActionButton.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/RecordingButton.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RecordingButton.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RecordingButton.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RecordingButton.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Card/CardModifier.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CardModifier.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CardModifier.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CardModifier.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AddAffirmationCarouselCard.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationCarouselCard.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationCarouselCard.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationCarouselCard.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AffirmationCard.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCard.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCard.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCard.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AffirmationCarouselCard.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCarouselCard.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCarouselCard.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationCarouselCard.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/BlueCardBackground.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueCardBackground.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueCardBackground.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueCardBackground.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/EnhancedAffirmationCard.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/EnhancedAffirmationCard.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/EnhancedAffirmationCard.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/ProgressCard.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressCard.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressCard.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressCard.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/Statistic.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Statistic.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Statistic.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Statistic.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/ContextualTipView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ContextualTipView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ContextualTipView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ContextualTipView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Counters/RepetitionCounterView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionCounterView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionCounterView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionCounterView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/ConfettiView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ConfettiView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ConfettiView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ConfettiView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/ParticleEffectView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ParticleEffectView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ParticleEffectView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ParticleEffectView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/RippleEffectView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RippleEffectView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RippleEffectView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RippleEffectView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/ErrorView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ErrorView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ErrorView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ErrorView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/LoadingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LoadingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LoadingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LoadingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/PremiumFeedbackView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PremiumFeedbackView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PremiumFeedbackView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PremiumFeedbackView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/HighlightedAffirmationText.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HighlightedAffirmationText.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HighlightedAffirmationText.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HighlightedAffirmationText.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Indicators/Indicators.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Indicators.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Indicators.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/Indicators.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Input/SearchBar.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SearchBar.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SearchBar.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SearchBar.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Pickers/CategoryPicker.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CategoryPicker.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CategoryPicker.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/CategoryPicker.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Pickers/ThemePickerView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePickerView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePickerView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePickerView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/ProgressBar.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressBar.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressBar.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressBar.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/ProgressIndicator.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressIndicator.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressIndicator.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressIndicator.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/StreakCalendar.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendar.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendar.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendar.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Settings/ThemePicker.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePicker.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePicker.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemePicker.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Visualizations/AudioWaveform.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveform.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveform.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioWaveform.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Visualizations/VoiceWaveVisualization.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/VoiceWaveVisualization.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/VoiceWaveVisualization.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/VoiceWaveVisualization.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+ActionButtonStyle.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+ActionButtonStyle.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+ActionButtonStyle.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+ActionButtonStyle.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+BackgroundComponents.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BackgroundComponents.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BackgroundComponents.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BackgroundComponents.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+BlueTheme.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BlueTheme.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BlueTheme.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/View+BlueTheme.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/AffirmationStub.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStub.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStub.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStub.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockAudioService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockAudioService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockAudioService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockAudioService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockJournalRepository.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockJournalRepository.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockJournalRepository.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockJournalRepository.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockRepetitionService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockRepetitionService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockRepetitionService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MockRepetitionService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/AlertItem.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItem.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItem.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItem.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/AlertItemModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItemModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItemModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AlertItemModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/StreakCalendarDay.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendarDay.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendarDay.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakCalendarDay.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/BlueThemeModifiers.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueThemeModifiers.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueThemeModifiers.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/BlueThemeModifiers.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/GlassmorphicCardModifier.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GlassmorphicCardModifier.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GlassmorphicCardModifier.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GlassmorphicCardModifier.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/ThemeTitleModifier.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeTitleModifier.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeTitleModifier.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeTitleModifier.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAffirmationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAffirmationsView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationsView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationsView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationsView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAudioRecordingService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAudioRecordingService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAudioRecordingService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAudioRecordingService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewHomeView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewMockTypes.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMockTypes.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMockTypes.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMockTypes.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewNotificationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewNotificationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewNotificationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewNotificationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewRepetitionService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewRepetitionService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewRepetitionService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewRepetitionService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewServiceFactory.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewServiceFactory.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewServiceFactory.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewServiceFactory.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewSettingsView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewStreakService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewStreakService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewStreakService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewStreakService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/PreviewMocks.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMocks.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMocks.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMocks.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Utilities/AsyncPreview.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AsyncPreview.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AsyncPreview.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AsyncPreview.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AchievementSharingViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AddAffirmationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AffirmationDetailViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/FixedSpeakAffirmationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FixedSpeakAffirmationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FixedSpeakAffirmationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/FixedSpeakAffirmationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/GuidedSessionViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Journal/JournalViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/MainTabViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/MilestoneJourneyViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAddAffirmationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationDetailViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationListViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationRepository.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationRepository.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationRepository.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationRepository.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationSystem.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationSystem.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationSystem.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationSystem.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewExportOptionsViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewExportOptionsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewExportOptionsViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewExportOptionsViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewHapticManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHapticManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHapticManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHapticManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewHomeViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewHomeViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewMainTabViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewPremiumFeaturesViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewPremiumFeaturesViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewPremiumFeaturesViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewPremiumFeaturesViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewSettingsViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSettingsViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewSyncOptionsViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSyncOptionsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSyncOptionsViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewSyncOptionsViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewThemeManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewThemeManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewThemeManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewThemeManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewUserDefaults.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewUserDefaults.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewUserDefaults.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewUserDefaults.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/ProgressDashboardViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/AddAffirmationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AddAffirmationView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/AffirmationPickerView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationPickerView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationPickerView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationPickerView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/SpeakAffirmationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/SpeakAffirmationViewTemp.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewTemp.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewTemp.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SpeakAffirmationViewTemp.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/AffirmationDetailView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationDetailView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Audio/AudioPlaybackView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioPlaybackView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioPlaybackView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioPlaybackView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Audio/AudioRecordingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AudioRecordingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Diagnostic/DiagnosticLauncher.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DiagnosticLauncher.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DiagnosticLauncher.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DiagnosticLauncher.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Diagnostic/RepetitionDiagnosticView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionDiagnosticView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionDiagnosticView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionDiagnosticView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Help/HelpView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HelpView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HelpView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HelpView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Home/HomeView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/HomeView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalCalendarView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalCalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalCalendarView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalCalendarView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalEntryRow.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalEntryRow.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalEntryRow.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalEntryRow.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/JournalView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Library/LibraryView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/LibraryView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Loading/ServiceInitializationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ServiceInitializationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ServiceInitializationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ServiceInitializationView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Navigation/MainTabView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MainTabView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Navigation/NavigationCoordinator.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NavigationCoordinator.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NavigationCoordinator.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NavigationCoordinator.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Premium/SubscriptionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAddAffirmationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAddAffirmationView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAffirmationDetailView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationDetailView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAffirmationListView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewAffirmationListView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewApp.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewApp.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewApp.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewApp.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewMainTabView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewMainTabView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewOnboardingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewOnboardingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewOnboardingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PreviewOnboardingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Progress/MilestoneJourneyView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MilestoneJourneyView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Progress/ProgressDashboardView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ProgressDashboardView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/AffirmationStepView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStepView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStepView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AffirmationStepView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/GuidedSessionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/GuidedSessionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/NeuralPathwayRestorationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NeuralPathwayRestorationView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/RepetitionTrackingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionTrackingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RepetitionProgressCardView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionProgressCardView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionProgressCardView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RepetitionProgressCardView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RootView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RootView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/RootView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AboutSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AboutView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AboutView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AccountSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AccountSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AccountSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AccountSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/DataSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DataSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DataSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/DataSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/NotificationSettingsView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationSettingsView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationSettingsView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationSettingsView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/NotificationsSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationsSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationsSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/NotificationsSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/PrivacyPolicyView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PrivacyPolicyView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PrivacyPolicyView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/PrivacyPolicyView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsThemePickerView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsThemePickerView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsThemePickerView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsThemePickerView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SettingsView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SubscriptionSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SubscriptionSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/TermsOfServiceView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/TermsOfServiceView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/TermsOfServiceView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/TermsOfServiceView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSectionView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSectionView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSectionView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSectionView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSelectorView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSelectorView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSelectorView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSelectorView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSettingsView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSettingsView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSettingsView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/ThemeSettingsView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/OnboardingPageView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingPageView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingPageView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingPageView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/OnboardingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/OnboardingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/SkeletonView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SkeletonView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SkeletonView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/SkeletonView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Sharing/AchievementSharingView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/AchievementSharingView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/MonthlyCalendarView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MonthlyCalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MonthlyCalendarView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/MonthlyCalendarView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/StreakTimelineView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakTimelineView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakTimelineView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakTimelineView.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/StreakVisualizationView.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationView.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationView.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopUI.build/Objects-normal/arm64/StreakVisualizationView.o"}}