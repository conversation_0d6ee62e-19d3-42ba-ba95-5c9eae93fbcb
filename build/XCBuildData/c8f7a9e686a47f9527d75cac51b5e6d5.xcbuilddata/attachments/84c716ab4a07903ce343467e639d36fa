/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AffirmationProgressIndicator.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AffirmationTextView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/AudioWaveformView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/ActionButtonStyle.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/FilterChip.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/PlayButton.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/QuickActionButton.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Buttons/RecordingButton.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Card/CardModifier.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AddAffirmationCarouselCard.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AffirmationCard.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/AffirmationCarouselCard.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/BlueCardBackground.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/ProgressCard.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/Statistic.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/ContextualTipView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Counters/RepetitionCounterView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/ConfettiView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/ParticleEffectView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Effects/RippleEffectView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/ErrorView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/LoadingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Feedback/PremiumFeedbackView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/HighlightedAffirmationText.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Indicators/Indicators.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Input/SearchBar.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Pickers/CategoryPicker.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Pickers/ThemePickerView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/ProgressBar.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/ProgressIndicator.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Progress/StreakCalendar.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Settings/ThemePicker.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Visualizations/AudioWaveform.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Visualizations/VoiceWaveVisualization.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+ActionButtonStyle.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+BackgroundComponents.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Extensions/View+BlueTheme.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/AffirmationStub.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockAudioService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockJournalRepository.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Mocks/MockRepetitionService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/AlertItem.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/AlertItemModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Models/StreakCalendarDay.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/BlueThemeModifiers.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/GlassmorphicCardModifier.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Modifiers/ThemeTitleModifier.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAffirmationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAffirmationsView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewAudioRecordingService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewHomeView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewMockTypes.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewNotificationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewRepetitionService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewServiceFactory.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewSettingsView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Preview/PreviewStreakService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/PreviewMocks.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Utilities/AsyncPreview.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AchievementSharingViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AddAffirmationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AffirmationDetailViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/FixedSpeakAffirmationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/GuidedSessionViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Journal/JournalViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/MainTabViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/MilestoneJourneyViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAddAffirmationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationDetailViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationListViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationRepository.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewAffirmationSystem.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewExportOptionsViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewHapticManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewHomeViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewMainTabViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewPremiumFeaturesViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewSettingsViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewSyncOptionsViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewThemeManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/Preview/PreviewUserDefaults.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/ProgressDashboardViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/AddAffirmationView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/AffirmationPickerView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/SpeakAffirmationView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Affirmation/SpeakAffirmationViewTemp.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/AffirmationDetailView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Audio/AudioPlaybackView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Audio/AudioRecordingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Diagnostic/DiagnosticLauncher.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Diagnostic/RepetitionDiagnosticView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Help/HelpView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Home/HomeView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalCalendarView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalEntryRow.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Journal/JournalView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Library/LibraryView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Loading/ServiceInitializationView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Navigation/MainTabView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Navigation/NavigationCoordinator.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Premium/SubscriptionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAddAffirmationView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAffirmationDetailView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewAffirmationListView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewApp.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewMainTabView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Preview/PreviewOnboardingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Progress/MilestoneJourneyView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Progress/ProgressDashboardView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/AffirmationStepView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/GuidedSessionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/NeuralPathwayRestorationView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Repetition/RepetitionTrackingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RepetitionProgressCardView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AboutSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AboutView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/AccountSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/DataSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/NotificationSettingsView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/NotificationsSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/PrivacyPolicyView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsThemePickerView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SubscriptionSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/TermsOfServiceView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSectionView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSelectorView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/ThemeSettingsView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/OnboardingPageView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/OnboardingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Shared/SkeletonView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Sharing/AchievementSharingView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/MonthlyCalendarView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/StreakTimelineView.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Streak/StreakVisualizationView.swift
