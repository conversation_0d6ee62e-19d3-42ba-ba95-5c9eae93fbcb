{"": {"const-values": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master.d", "diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NeuroLoopCore-master.swiftdeps"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Color+FallbackGradient.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Color+FallbackGradient.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Color+FallbackGradient.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Color+FallbackGradient.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Examples/ViewStateExample.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateExample.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateExample.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateExample.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Extensions/TaskPriorityExtension.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskPriorityExtension.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskPriorityExtension.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskPriorityExtension.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Migration/DataMigrationManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataMigrationManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataMigrationManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataMigrationManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/NotificationViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockAffirmationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockAffirmationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockAffirmationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockAffirmationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockDataExportService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockDataExportService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockDataExportService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockDataExportService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockPurchaseManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockPurchaseManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockPurchaseManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockPurchaseManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockSyncService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockSyncService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockSyncService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewMockSyncService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewRepetitionService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewRepetitionService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewRepetitionService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewRepetitionService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewStreakService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewStreakService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewStreakService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PreviewStreakService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/AffirmationDTORepository.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationDTORepository.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationDTORepository.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationDTORepository.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/InMemoryAffirmationRepository.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/InMemoryAffirmationRepository.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/InMemoryAffirmationRepository.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/InMemoryAffirmationRepository.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/SwiftDataAffirmationRepository.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SwiftDataAffirmationRepository.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SwiftDataAffirmationRepository.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SwiftDataAffirmationRepository.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AccessibilityService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AccessibilityService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AccessibilityService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AccessibilityService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AffirmationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AffirmationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AppStoreConfigurationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AppStoreConfigurationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AppStoreConfigurationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AppStoreConfigurationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AsyncServiceFactory.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AsyncServiceFactory.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AsyncServiceFactory.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AsyncServiceFactory.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AudioRecordingService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioRecordingService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioRecordingService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioRecordingService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AudioSessionManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioSessionManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioSessionManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AudioSessionManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/BackgroundSyncManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BackgroundSyncManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BackgroundSyncManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BackgroundSyncManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/BatchProcessor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BatchProcessor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BatchProcessor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/BatchProcessor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CachePerformanceMonitor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CachePerformanceMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CachePerformanceMonitor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CachePerformanceMonitor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CacheWarmingService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CacheWarmingService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CacheWarmingService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CacheWarmingService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ChangeTracker.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ChangeTracker.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ChangeTracker.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ChangeTracker.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CloudKitDataExportService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitDataExportService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitDataExportService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitDataExportService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CloudKitSyncService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitSyncService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitSyncService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CloudKitSyncService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CompressionManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CompressionManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CompressionManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/CompressionManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ConflictResolver.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ConflictResolver.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ConflictResolver.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ConflictResolver.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DataExportService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataExportService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataExportService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DataExportService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DeltaProcessor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DeltaProcessor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DeltaProcessor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DeltaProcessor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DiskCache.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DiskCache.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DiskCache.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/DiskCache.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/EnhancedOptimisticUpdateManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/EnhancedOptimisticUpdateManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/EnhancedOptimisticUpdateManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/EnhancedOptimisticUpdateManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/FinalTestingService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/FinalTestingService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/FinalTestingService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/FinalTestingService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/MemoryStorageService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MemoryStorageService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MemoryStorageService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MemoryStorageService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/MultiLevelCacheManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MultiLevelCacheManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MultiLevelCacheManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/MultiLevelCacheManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/NotificationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NotificationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateMonitor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateMonitor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateMonitor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateOrchestrator.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateOrchestrator.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateOrchestrator.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/OptimisticUpdateOrchestrator.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PerformanceOptimizationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PerformanceOptimizationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PerformanceOptimizationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PerformanceOptimizationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PremiumFeatureUnlocker.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumFeatureUnlocker.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumFeatureUnlocker.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumFeatureUnlocker.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PremiumService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PremiumService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PurchaseManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PurchaseManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PurchaseManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/PurchaseManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ReactiveDataManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveDataManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveDataManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveDataManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/RepetitionService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/RetryManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RetryManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RetryManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RetryManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ServiceFactory.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactory.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactory.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactory.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ServiceFactoryExtension.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactoryExtension.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactoryExtension.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ServiceFactoryExtension.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SimpleReactiveDataService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SimpleReactiveDataService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SimpleReactiveDataService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SimpleReactiveDataService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SmartCacheInvalidationService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SmartCacheInvalidationService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SmartCacheInvalidationService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SmartCacheInvalidationService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SpeechRecognitionManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SpeechRecognitionManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SpeechRecognitionManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SpeechRecognitionManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StateAnalytics.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StateAnalytics.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StateAnalytics.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StateAnalytics.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StatePersistenceManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StatePersistenceManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StatePersistenceManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StatePersistenceManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StoreKitPurchaseManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StoreKitPurchaseManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StoreKitPurchaseManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StoreKitPurchaseManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StreakService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SyncChange.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncChange.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncChange.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncChange.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SyncConflictResolver.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncConflictResolver.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncConflictResolver.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/SyncConflictResolver.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/UserPreferencesService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/UserPreferencesService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/UserPreferencesService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/UserPreferencesService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ViewStateManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ViewStateObserver.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateObserver.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateObserver.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ViewStateObserver.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/StreakManagerImpl.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManagerImpl.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManagerImpl.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManagerImpl.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/StreakManaging.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManaging.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManaging.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/StreakManaging.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/Theme.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Theme.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Theme.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/Theme.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManaging.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManaging.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManaging.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeManaging.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemePersistenceService.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemePersistenceService.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemePersistenceService.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemePersistenceService.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeTypes.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeTypes.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeTypes.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ThemeTypes.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/AlertManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AlertManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AlertManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/AlertManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/ErrorHandler.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ErrorHandler.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ErrorHandler.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ErrorHandler.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/LRUCache.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LRUCache.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LRUCache.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LRUCache.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/LockManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LockManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LockManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/LockManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/NetworkMonitor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NetworkMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NetworkMonitor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/NetworkMonitor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/TaskManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/TaskManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/ViewModels/ReactiveViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/ReactiveViewModel.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/ViewModels/RepetitionTrackingViewModel.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionTrackingViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionTrackingViewModel.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopCore.build/Objects-normal/arm64/RepetitionTrackingViewModel.o"}}