{"": {"const-values": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master.d", "diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/NeuroLoopTypes-master.swiftdeps"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/AccessibilityManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AccessibilityManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AccessibilityManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AccessibilityManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Affirmation/AffirmationCategory.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AffirmationCategory.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AffirmationCategory.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AffirmationCategory.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/AppLaunchTestable.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AppLaunchTestable.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AppLaunchTestable.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/AppLaunchTestable.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/CodableColorExtension.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/CodableColorExtension.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/CodableColorExtension.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/CodableColorExtension.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Color+Platform.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Color+Platform.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Color+Platform.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Color+Platform.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/GradientButtonModifier.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/GradientButtonModifier.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/GradientButtonModifier.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/GradientButtonModifier.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Haptics/HapticEnvironmentKey.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticEnvironmentKey.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticEnvironmentKey.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticEnvironmentKey.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Haptics/HapticGenerating.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticGenerating.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticGenerating.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticGenerating.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Haptics/HapticManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/HapticManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Journal/JournalEntry.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/JournalEntry.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/JournalEntry.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/JournalEntry.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Navigation/MainTab.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/MainTab.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/MainTab.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/MainTab.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/OnboardingPage.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/OnboardingPage.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/OnboardingPage.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/OnboardingPage.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/PerformanceMonitor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PerformanceMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PerformanceMonitor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PerformanceMonitor.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Premium/PremiumFeature.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PremiumFeature.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PremiumFeature.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/PremiumFeature.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/StreakTypes.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/StreakTypes.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/StreakTypes.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/StreakTypes.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Theme.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Theme.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Theme.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/Theme.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Theme/ThemeColor+iOS.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+iOS.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+iOS.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+iOS.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Theme/ThemeColor+macOS.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+macOS.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+macOS.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor+macOS.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopTypes/Theme/ThemeColor.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopTypes.build/Objects-normal/arm64/ThemeColor.o"}}