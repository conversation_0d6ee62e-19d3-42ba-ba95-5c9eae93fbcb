/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Color+FallbackGradient.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Examples/ViewStateExample.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Extensions/TaskPriorityExtension.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Migration/DataMigrationManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/NotificationViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockAffirmationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockDataExportService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockPurchaseManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewMockSyncService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewRepetitionService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Preview/PreviewStreakService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/AffirmationDTORepository.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/InMemoryAffirmationRepository.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Repositories/SwiftDataAffirmationRepository.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AccessibilityService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AffirmationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AppStoreConfigurationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AsyncServiceFactory.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AudioRecordingService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/AudioSessionManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/BackgroundSyncManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/BatchProcessor.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CachePerformanceMonitor.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CacheWarmingService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ChangeTracker.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CloudKitDataExportService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CloudKitSyncService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/CompressionManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ConflictResolver.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DataExportService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DeltaProcessor.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/DiskCache.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/EnhancedOptimisticUpdateManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/FinalTestingService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/MemoryStorageService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/MultiLevelCacheManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/NotificationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateMonitor.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/OptimisticUpdateOrchestrator.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PerformanceOptimizationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PremiumFeatureUnlocker.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PremiumService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PurchaseManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ReactiveDataManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/RepetitionService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/RetryManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ServiceFactory.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ServiceFactoryExtension.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SimpleReactiveDataService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SmartCacheInvalidationService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SpeechRecognitionManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StateAnalytics.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StatePersistenceManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StoreKitPurchaseManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/StreakService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SyncChange.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/SyncConflictResolver.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/UserPreferencesService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ViewStateManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/ViewStateObserver.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/StreakManagerImpl.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/StreakManaging.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/Theme.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManaging.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemePersistenceService.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeTypes.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/AlertManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/ErrorHandler.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/LRUCache.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/LockManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/NetworkMonitor.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Utilities/TaskManager.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/ViewModels/ReactiveViewModel.swift
/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/ViewModels/RepetitionTrackingViewModel.swift
