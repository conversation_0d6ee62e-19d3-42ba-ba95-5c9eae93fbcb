{"": {"const-values": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master.d", "diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NeuroLoopInterfaces-master.swiftdeps"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/Affirmation.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/Affirmation.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/Affirmation.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation/AffirmationProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Affirmation/AffirmationRepositoryProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationRepositoryProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationRepositoryProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationRepositoryProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationConstants.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationConstants.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationConstants.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationConstants.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationError.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationError.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationError.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationError.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationSortOption.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationSortOption.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationSortOption.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationSortOption.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AffirmationStatistics.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationStatistics.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationStatistics.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AffirmationStatistics.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/AudioFileManager.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioFileManager.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioFileManager.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioFileManager.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/DataExportServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/DataExportServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/DataExportServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/DataExportServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/HomeViewModelProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/HomeViewModelProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/HomeViewModelProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/HomeViewModelProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Journal/JournalRepositoryProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/JournalRepositoryProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/JournalRepositoryProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/JournalRepositoryProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/LibraryViewModelProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/LibraryViewModelProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/LibraryViewModelProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/LibraryViewModelProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/NotificationServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NotificationServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NotificationServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/NotificationServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/PurchaseManagerProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PurchaseManagerProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PurchaseManagerProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PurchaseManagerProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/RepetitionServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/RepetitionServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/RepetitionServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/RepetitionServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioRecordingError.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingError.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingError.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingError.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioRecordingServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioRecordingServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/AudioServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/AudioServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/PremiumServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PremiumServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PremiumServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/PremiumServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/ServiceErrors.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ServiceErrors.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ServiceErrors.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ServiceErrors.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/Services/SpeechRecognitionError.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SpeechRecognitionError.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SpeechRecognitionError.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SpeechRecognitionError.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/StreakCalendarDay.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakCalendarDay.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakCalendarDay.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakCalendarDay.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/StreakServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/StreakServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/SyncServiceProtocol.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SyncServiceProtocol.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SyncServiceProtocol.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/SyncServiceProtocol.o"}, "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopInterfaces/ThemeManaging.swift": {"index-unit-output-path": "/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ThemeManaging.o", "llvm-bc": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ThemeManaging.bc", "object": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/build/NeuroLoopApp2025_Fresh.build/Release-iphoneos/NeuroLoopInterfaces.build/Objects-normal/arm64/ThemeManaging.o"}}