import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI
import SwiftUI

#if DEBUG
    private class PreviewAffirmationRepository: NeuroLoopInterfaces.AffirmationRepositoryProtocol,
        @unchecked Sendable
    {
        public init() {}
        public func fetchAffirmations() async throws -> [any NeuroLoopInterfaces
            .AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                    isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
            ]
        }
        public func fetchAffirmation(id: UUID) async throws -> (
            any NeuroLoopInterfaces.AffirmationProtocol
        )? {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
        }
        public func createAffirmation(
            text: String, category: AffirmationCategory, recordingURL: URL?
        ) async throws -> any NeuroLoopInterfaces.AffirmationProtocol {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: text, category: category, recordingURL: recordingURL, isFavorite: false,
                todayProgress: 0.0, cycleProgress: 0.0)
        }
        public func updateAffirmation(_ affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}
        public func deleteAffirmation(id: UUID) async throws {}
        public func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any NeuroLoopInterfaces.AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Preview Affirmation", category: category, recordingURL: nil,
                    isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
            ]
        }
        public func fetchFavoriteAffirmations() async throws -> [any NeuroLoopInterfaces
            .AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Favorite Preview Affirmation", category: .confidence, recordingURL: nil,
                    isFavorite: true, todayProgress: 1.0, cycleProgress: 0.8)
            ]
        }
        public func fetchCurrentAffirmation() async throws -> (
            any NeuroLoopInterfaces.AffirmationProtocol
        )? {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
        }
        public func recordRepetition(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}
        public func startCycle(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}

        // Missing protocol methods
        public func saveAffirmation(_ affirmation: any NeuroLoopInterfaces.AffirmationProtocol) async throws {}
        public func updateProgress(for affirmationId: UUID, repetitions: Int, date: Date) async throws {}
        public func getStatistics(for affirmationId: UUID) async throws -> AffirmationStatistics { AffirmationStatistics() }
        public func searchAffirmations(query: String) async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
        public func getAffirmations(sortedBy option: AffirmationSortOption) async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
        public func updateFavoriteStatus(for affirmationId: UUID, isFavorite: Bool) async throws {}
        public func getDailyProgress(for affirmationId: UUID, date: Date) async throws -> Int { 0 }
        public func updateEnergyLevel(for affirmationId: UUID, level: Double) async throws {}
        public func recordMood(for affirmationId: UUID, rating: Int, notes: String?) async throws {}
        public func getAffirmationsWithActiveCycles() async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
        public func completeCycle(for affirmationId: UUID) async throws {}
        public func startNewCycle(for affirmationId: UUID) async throws {}
        public func getStreakInfo(for affirmationId: UUID) async throws -> (currentStreak: Int, longestStreak: Int) { (0, 0) }
        public func updateLongestStreak(for affirmationId: UUID, streak: Int) async throws {}
    }
#endif

public struct AffirmationsView: View {
    @StateObject private var viewModel: AffirmationViewModel

    @State private var isLoading: Bool = true

    public init(viewModel: AffirmationViewModel? = nil) {
        if let viewModel = viewModel {
            _viewModel = StateObject(wrappedValue: viewModel)
        } else {
            // Use a mock implementation for preview/testing
            let affirmationService = AffirmationService(repository: PreviewAffirmationRepository())
            let repetitionService = RepetitionService(affirmationService: affirmationService)
            let streakService = StreakService(repository: PreviewAffirmationRepository())
            #if os(iOS)
                let audioRecordingService = AudioRecordingService(
                    audioFileManager: AudioFileManager.shared)
            #else
                let audioRecordingService = AudioRecordingService()
            #endif
            let viewModel = AffirmationViewModel(
                affirmationService: affirmationService,
                repetitionService: repetitionService,
                streakService: streakService,
                audioRecordingService: audioRecordingService,
                hapticManager: HapticManager.shared,
                themeManager: ThemeManager.shared
            )
            _viewModel = StateObject(wrappedValue: viewModel)
        }
    }

    public var body: some View {
        VStack(spacing: 20) {
            if isLoading || viewModel.isLoading {
                AffirmationsSkeletonView()
            } else {
                Text("Daily Affirmations")
                    .font(.largeTitle)
                    .bold()
                    .foregroundColor(viewModel.theme.primaryTextColor.asColor)
                    .accessibilityAddTraits(.isHeader)

                // Carousel of affirmation cards
                if viewModel.hasAffirmations {
                    TabView(selection: $viewModel.carouselIndex) {
                        ForEach(Array(viewModel.affirmations.enumerated()), id: \.element.id) {
                            idx, affirmation in
                            SimpleAffirmationCard(
                                affirmation: affirmation,
                                theme: viewModel.theme,
                                onTap: {
                                    Task {
                                        await viewModel.setCurrentAffirmation(affirmation)
                                    }
                                }
                            )
                            .tag(idx)
                        }
                    // Blank card for new affirmation
                    VStack {
                        Button(action: { viewModel.showingAddAffirmationSheet = true }) {
                            VStack(spacing: 12) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 48))
                                    .foregroundColor(viewModel.theme.accentColor.asColor)
                                Text("Add New Affirmation")
                                    .font(.headline)
                                    .foregroundColor(viewModel.theme.accentColor.asColor)
                            }
                            .padding()
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(viewModel.theme.cardBackgroundColor.asColor.opacity(0.2))
                            )
                        }
                    }
                    .tag(viewModel.affirmations.count)
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                .frame(height: 320)
                .onChange(of: viewModel.carouselIndex) { oldValue, newValue in
                    print("[DEBUG] Carousel index changed from \(oldValue) to \(newValue)")
                    if newValue < viewModel.affirmations.count {
                        let selectedAffirmation = viewModel.affirmations[newValue]
                        print("[DEBUG] Setting current affirmation to: \(selectedAffirmation.text)")
                        Task {
                            await viewModel.setCurrentAffirmation(selectedAffirmation)
                        }
                    }
                }
                .sheet(isPresented: $viewModel.showingAddAffirmationSheet) {
                    AddAffirmationSheet(viewModel: viewModel)
                }
                }

                if let currentAffirmation = viewModel.currentAffirmation {
                    Text("Day \(viewModel.currentCycleDay) of \(viewModel.totalCycleDays)")
                        .font(.headline)
                        .foregroundColor(viewModel.theme.secondaryTextColor.asColor)
                        .padding(.top, 5)
                        .accessibilityLabel(
                            "Day \(viewModel.currentCycleDay) of \(viewModel.totalCycleDays)")

                    ZStack {
                        // Background for contrast
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                viewModel.theme.accentColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            viewModel.theme.accentColor.asColor
                                        ]), startPoint: .top, endPoint: .bottom)
                            )
                            .frame(width: 220, height: 220)

                        RepetitionCounterView(
                            currentCount: viewModel.currentRepetitions,
                            totalCount: viewModel.totalRepetitions,
                            onTap: {
                                Task {
                                    await viewModel.recordRepetition()
                                }
                            }
                        )
                        .disabled(!viewModel.canRecordRepetition)
                        .opacity(viewModel.canRecordRepetition ? 1.0 : 0.6)
                    }
                    .padding(.vertical, 20)
                    .background(
                        viewModel.theme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [viewModel.theme.accentColor.asColor]),
                                startPoint: .top, endPoint: .bottom)
                    )

                    if viewModel.isCurrentCycleComplete {
                        Text("Cycle Complete! 🎉")
                            .font(.headline)
                            .foregroundColor(.green)
                            .padding()
                            .accessibilityLabel("Cycle Complete!")
                    } else if viewModel.hasTodayQuotaMet {
                        Text("Today's Goal Met! 🎯")
                            .font(.headline)
                            .foregroundColor(.green)
                            .padding()
                            .accessibilityLabel("Today's Goal Met!")
                    }

                    Toggle(isOn: $viewModel.isAutoRepeatEnabled) {
                        Text("Auto-Repeat Mode")
                            .foregroundColor(viewModel.theme.primaryTextColor.asColor)
                    }
                    .padding(.horizontal, 40)
                    .accessibilityHint(
                        "When enabled, repetitions will be automatically counted at regular intervals"
                    )

                    // Manual repetition button
                    Button(action: {
                        print("🔍 Manual repetition increment")
                        Task {
                            await viewModel.recordRepetition()
                        }
                    }) {
                        Text("Record Repetition")
                            .font(.caption)
                            .padding(8)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                    .padding(.top, 10)
                } else {
                    Text("No active affirmation")
                        .font(.title2)
                        .foregroundColor(viewModel.theme.primaryTextColor.asColor.opacity(0.7))
                        .padding()

                    Button("Create Your First Affirmation") {
                        // This would navigate to affirmation creation screen
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        viewModel.theme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [viewModel.theme.accentColor.asColor]),
                                startPoint: .top, endPoint: .bottom)
                    )
                    .cornerRadius(10)
                    .accessibilityHint("Tap to create your first affirmation")
                }

                Spacer()
            }
        }
        .padding()
        .onAppear {
            Task {
                await viewModel.loadAffirmations()
                viewModel.syncCarouselWithCurrentAffirmation()
                isLoading = false
            }
        }
    }
}

// MARK: - Affirmations Skeleton View

private struct AffirmationsSkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 40)
                .redacted(reason: .placeholder)
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.15))
                .frame(height: 60)
                .redacted(reason: .placeholder)
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.15))
                .frame(height: 220)
                .redacted(reason: .placeholder)
        }
        .padding()
        .accessibilityHidden(true)
    }
}

private struct AddAffirmationSheet: View {
    @ObservedObject var viewModel: AffirmationViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var text: String = ""
    @State private var selectedCategory: AffirmationCategory = .confidence
    @State private var isSaving = false

    var body: some View {
        NavigationView {
            Form {
                Section(
                    header: Text("Affirmation Text").foregroundColor(
                        viewModel.theme.primaryTextColor.asColor)
                ) {
                    TextField("Enter your affirmation", text: $text)
                        .foregroundColor(viewModel.theme.primaryTextColor.asColor)
                }
                Section(
                    header: Text("Category").foregroundColor(
                        viewModel.theme.primaryTextColor.asColor)
                ) {
                    Picker("Category", selection: $selectedCategory) {
                        ForEach(AffirmationCategory.allCases, id: \.self) { category in
                            Text(category.displayName).tag(category)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            .navigationTitle("New Affirmation")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        isSaving = true
                        Task {
                            await viewModel.createAffirmation(
                                text: text, category: selectedCategory)
                            isSaving = false
                            dismiss()
                        }
                    }
                    .disabled(
                        text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSaving)
                }
            }
        }
    }
}

// MARK: - Simple Affirmation Card

private struct SimpleAffirmationCard: View {
    let affirmation: any AffirmationProtocol
    let theme: NeuroLoopTypes.Theme
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            // Affirmation text with quotation marks
            VStack(spacing: 8) {
                Text("\u{201C}")
                    .font(.system(size: 40, weight: .light, design: .serif))
                    .foregroundColor(theme.accentColor.asColor.opacity(0.6))

                Text(affirmation.text)
                    .font(.title2)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(theme.primaryTextColor.asColor)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)

                Text("\u{201D}")
                    .font(.system(size: 40, weight: .light, design: .serif))
                    .foregroundColor(theme.accentColor.asColor.opacity(0.6))
            }
            .padding(.horizontal, 20)

            // Category badge
            Text(affirmation.category.displayName)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(theme.primaryTextColor.asColor)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(theme.accentColor.asColor.opacity(0.2))
                )

            // Action button
            Button(action: onTap) {
                HStack {
                    Image(systemName: "play.circle.fill")
                        .font(.title3)
                    Text("Start Session")
                        .fontWeight(.semibold)
                }
                .foregroundColor(theme.primaryTextColor.asColor)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(theme.accentColor.asColor)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
        .frame(height: 400) // Fixed height for consistent carousel
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(theme.cardBackgroundColor.asColor)
                .shadow(
                    color: theme.shadowColor.asColor.opacity(0.1),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        .padding(.horizontal, 16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Affirmation: \(affirmation.text)")
        .accessibilityHint("Swipe left or right to see other affirmations, or tap to start session")
    }
}

#Preview {
    AffirmationsView()
}
