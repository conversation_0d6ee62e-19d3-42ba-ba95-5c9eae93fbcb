import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI

@MainActor
public class MockServiceFactory {
    public static let shared = MockServiceFactory()

    private init() {}

    public func getAffirmationService() -> AffirmationServiceProtocol {
        // Use local MockAffirmationRepository for testing
        let repository = MockAffirmationRepository()
        return NeuroLoopCore.AffirmationService(repository: repository)
    }

    public func getRepetitionService() -> RepetitionServiceProtocol {
        let affirmationService = getAffirmationService()
        return NeuroLoopCore.RepetitionService(
            affirmationService: affirmationService as! NeuroLoopCore.AffirmationService)
    }

    public func getStreakService() -> StreakServiceProtocol {
        // Use local MockAffirmationRepository for testing
        let repository =
            MockAffirmationRepository()
            as any NeuroLoopInterfaces.AffirmationRepositoryProtocol & Sendable
        return NeuroLoopCore.StreakService(repository: repository)
    }

    public func getAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            // On iOS, provide the AudioFileManager parameter
            return NeuroLoopCore.AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            // On macOS, use the parameterless initializer
            return NeuroLoopCore.AudioRecordingService()
        #endif
    }

    @available(iOS 17.0, macOS 14.0, *)
    public func getPurchaseManager() -> PurchaseManagerSendable {
        return MockPurchaseManager()
    }

    @available(iOS 17.0, macOS 14.0, *)
    public func getDataExportService() -> DataExportServiceProtocol {
        return MockDataExportService()
    }

    public func getSyncService() -> SyncServiceProtocol {
        return MockSyncService()
    }
}

// MARK: - Local Mock Implementations

public class MockAffirmationRepository: NeuroLoopInterfaces.AffirmationRepositoryProtocol,
    @unchecked Sendable
{
    public init() {}

    public func fetchAffirmations() async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] {
        return []
    }
    public func fetchAffirmation(id: UUID) async throws -> (
        any NeuroLoopInterfaces.AffirmationProtocol
    )? { return nil }
    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any NeuroLoopInterfaces.AffirmationProtocol
    {
        return await NeuroLoopInterfaces.AffirmationStub(
            text: text, category: category, recordingURL: recordingURL)
    }
    public func updateAffirmation(_ affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
        async throws
    {}
    public func deleteAffirmation(id: UUID) async throws {}
    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any NeuroLoopInterfaces.AffirmationProtocol]
    { return [] }
    public func fetchFavoriteAffirmations() async throws -> [any NeuroLoopInterfaces
        .AffirmationProtocol]
    { return [] }
    public func fetchCurrentAffirmation() async throws -> (
        any NeuroLoopInterfaces.AffirmationProtocol
    )? { return nil }
    public func recordRepetition(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
        async throws
    {}
    public func startCycle(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
        async throws
    {}

    // Missing protocol methods
    public func saveAffirmation(_ affirmation: any NeuroLoopInterfaces.AffirmationProtocol) async throws {}
    public func updateProgress(for affirmationId: UUID, repetitions: Int, date: Date) async throws {}
    public func getStatistics(for affirmationId: UUID) async throws -> AffirmationStatistics { AffirmationStatistics() }
    public func searchAffirmations(query: String) async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
    public func getAffirmations(sortedBy option: AffirmationSortOption) async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
    public func updateFavoriteStatus(for affirmationId: UUID, isFavorite: Bool) async throws {}
    public func getDailyProgress(for affirmationId: UUID, date: Date) async throws -> Int { 0 }
    public func updateEnergyLevel(for affirmationId: UUID, level: Double) async throws {}
    public func recordMood(for affirmationId: UUID, rating: Int, notes: String?) async throws {}
    public func getAffirmationsWithActiveCycles() async throws -> [any NeuroLoopInterfaces.AffirmationProtocol] { [] }
    public func completeCycle(for affirmationId: UUID) async throws {}
    public func startNewCycle(for affirmationId: UUID) async throws {}
    public func getStreakInfo(for affirmationId: UUID) async throws -> (currentStreak: Int, longestStreak: Int) { (0, 0) }
    public func updateLongestStreak(for affirmationId: UUID, streak: Int) async throws {}
}

public struct AffirmationStub: AffirmationProtocol, Identifiable, Equatable, Codable {
    public let id: UUID
    public var text: String
    public var category: AffirmationCategory
    public var recordingURL: URL?
    public var createdAt: Date
    public var updatedAt: Date
    public var currentCycleDay: Int
    public var cycleStartDate: Date?
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var dailyProgress: [Date: Int]
    public var lastRepetitionDate: Date?
    public var energyLevel: Double
    public var moodRating: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var isCurrentCycleComplete: Bool
    public var todayProgress: Double
    public var cycleProgress: Double
    public var hasTodayQuotaMet: Bool
    public var canPerformRepetition: Bool { true }
    public var hasRecording: Bool { recordingURL != nil }
    public var longestStreak: Int

    public func recordRepetition() throws {}
    public func updateEnergyLevel(_ level: Double) {}
    public func recordMood(_ rating: Int, notes: String?) {}

    public func startAffirmations() {}
    public func stopAffirmations() {}
    public func addAffirmation(_ text: String) async throws {}
    public func removeAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
    public func getAllAffirmations() async -> [any AffirmationProtocol] { return [] }

    public init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        currentCycleDay: Int = 1,
        cycleStartDate: Date? = nil,
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        dailyProgress: [Date: Int] = [:],
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.0,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        isCurrentCycleComplete: Bool = false,
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0,
        hasTodayQuotaMet: Bool = false,
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.dailyProgress = dailyProgress
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.isCurrentCycleComplete = isCurrentCycleComplete
        self.todayProgress = todayProgress
        self.cycleProgress = cycleProgress
        self.hasTodayQuotaMet = hasTodayQuotaMet
        self.longestStreak = longestStreak
    }

    public static nonisolated func == (lhs: AffirmationStub, rhs: AffirmationStub) -> Bool {
        lhs.id == rhs.id
    }
}

private class MockSyncService: SyncServiceProtocol, @unchecked Sendable {
    func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
    func loadAffirmations() async throws -> [any AffirmationProtocol] { return [] }
    func deleteAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
}

@available(iOS 17.0, macOS 14.0, *)
private final class MockPurchaseManager: PurchaseManagerSendable, @unchecked Sendable {
    func purchasePremium() async throws {}
    func restorePurchases() async throws {}
}

@available(iOS 17.0, macOS 14.0, *)
private class MockDataExportService: DataExportServiceProtocol {
    func exportData() async throws -> URL { throw NSError(domain: "Mock", code: -1) }
    func importData(from url: URL) async throws { throw NSError(domain: "Mock", code: -1) }
    func deleteAllData() async throws { throw NSError(domain: "Mock", code: -1) }
}

@available(iOS 17.0, macOS 14.0, *)
private class MockPerformanceMonitor: PerformanceMonitorProtocol, @unchecked Sendable {
    func trackAffirmationShown(_ affirmation: any AffirmationProtocol) async {}
    func getMetrics() async -> [String: Double] { return [:] }
    func resetMetrics() async {}
}
