import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI
import os.log

@MainActor
public class AffirmationViewModel: ObservableObject {
    // MARK: - Logging
    private let logger = Logger(
        subsystem: Bundle.main.bundleIdentifier ?? "com.neuroloop.app",
        category: "AffirmationViewModel"
    )

    // MARK: - Published Properties

    @Published private(set) var affirmations: [any NeuroLoopInterfaces.AffirmationProtocol] = []
    @Published private(set) var currentAffirmation: (any NeuroLoopInterfaces.AffirmationProtocol)?
    @Published private(set) var isLoading = false
    @Published var error: Error?
    @Published var errorMessage: String?
    @Published var isAutoRepeatEnabled = false
    @Published private(set) var statistics: AffirmationStatistics?
    @Published private(set) var streakStatistics: StreakStatistics?
    @Published public var mostRecentAffirmation: (any NeuroLoopInterfaces.AffirmationProtocol)?
    @Published public var mostRecentAffirmationTimestamp: Date?
    @Published private(set) var recentAffirmations: [any NeuroLoopInterfaces.AffirmationProtocol] =
        []
    @Published private(set) var recentActivities: [Activity] = []
    @Published var carouselIndex: Int = 0
    @Published var showingAddAffirmationSheet: Bool = false

    // MARK: - Cache Properties
    private var affirmationCache: [UUID: any AffirmationProtocol] = [:]
    private var progressCache: [UUID: ProgressInfo] = [:]
    private var streakCache: [UUID: StreakInfo] = [:]
    private var lastCacheUpdate: Date?
    private let cacheValidityDuration: TimeInterval = 300  // 5 minutes

    // MARK: - Private Properties

    let affirmationService: AffirmationServiceProtocol
    let repetitionService: RepetitionServiceProtocol
    let streakService: StreakServiceProtocol
    let audioRecordingService: AudioRecordingServiceProtocol
    private let hapticManager: NeuroLoopTypes.HapticGenerating
    private let themeManager: NeuroLoopInterfaces.ThemeManaging
    private var cancellables = Set<AnyCancellable>()
    private let mostRecentAffirmationKey = "mostRecentAffirmationID"
    private let mostRecentAffirmationTimestampKey = "mostRecentAffirmationTimestamp"
    private let maxRecentActivities = 5

    // MARK: - Initialization

    @MainActor
    public init(
        affirmationService: AffirmationServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        streakService: StreakServiceProtocol,
        audioRecordingService: AudioRecordingServiceProtocol,
        hapticManager: NeuroLoopTypes.HapticGenerating,
        themeManager: NeuroLoopInterfaces.ThemeManaging
    ) {
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        self.streakService = streakService
        self.audioRecordingService = audioRecordingService
        self.hapticManager = hapticManager
        self.themeManager = themeManager
        print(
            "[DEBUG] AffirmationViewModel init: affirmations initialized with count = \(affirmations.count)"
        )
        Task {
            await loadAffirmations()
            await loadStatistics()
        }
    }

    // MARK: - Error Handling

    private func handleError(_ error: Error) {
        self.error = error
        self.errorMessage = error.localizedDescription

        // Enhanced error logging
        logger.error("❌ Error in AffirmationViewModel: \(error.localizedDescription)")
        if let nsError = error as NSError? {
            logger.error(
                """
                Error details:
                - Domain: \(nsError.domain)
                - Code: \(nsError.code)
                - Description: \(nsError.localizedDescription)
                - User Info: \(nsError.userInfo)
                """)
        }

        // Provide user-friendly error messages
        switch error {
        case let affirmationError as AffirmationError:
            switch affirmationError {
            case .notFound:
                self.errorMessage = "The requested affirmation could not be found."
                logger.error("Affirmation not found")
            case .invalidData:
                self.errorMessage = "There was a problem with the data. Please try again."
                logger.error("Invalid data error occurred")
            case .cannotPerformRepetition:
                self.errorMessage = "You've reached the maximum number of repetitions for today."
                logger.error("Too many repetitions attempted")
            case .alreadyInUse:
                self.errorMessage = "This affirmation is already in use."
                logger.error("Affirmation already in use")
            case .noActiveCycle:
                self.errorMessage = "No active cycle found for this affirmation."
                logger.error("No active cycle")
            case .maximumCyclesReached:
                self.errorMessage = "Maximum number of cycles reached for this affirmation."
                logger.error("Maximum cycles reached")
            case .maximumRepetitionsReached:
                self.errorMessage = "You've reached the maximum number of repetitions for today."
                logger.error("Maximum repetitions reached")
            case .cannotDelete:
                self.errorMessage = "This affirmation cannot be deleted."
                logger.error("Cannot delete affirmation")
            case .unknown:
                self.errorMessage = "An unexpected error occurred. Please try again."
                logger.error("Unknown error occurred")
            }
        default:
            self.errorMessage = "An unexpected error occurred. Please try again."
            logger.error("Unexpected error: \(error.localizedDescription)")
        }

        // Clear error after 5 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
            self?.error = nil
            self?.errorMessage = nil
        }
    }

    // MARK: - Public Methods

    /// Load all affirmations from the repository with caching
    public func loadAffirmations() async {
        logger.info("Starting to load affirmations")
        isLoading = true
        error = nil
        errorMessage = nil

        // Check if cache is still valid
        if let lastUpdate = lastCacheUpdate,
            Date().timeIntervalSince(lastUpdate) < cacheValidityDuration,
            !affirmations.isEmpty
        {
            logger.info("Using cached affirmations")
            isLoading = false
            return
        }

        do {
            let fetchedAffirmations = try await affirmationService.fetchAffirmations()
            logger.info("Successfully fetched \(fetchedAffirmations.count) affirmations")

            // Update cache
            affirmationCache = Dictionary(
                uniqueKeysWithValues: fetchedAffirmations.map { ($0.id, $0) })
            affirmations = fetchedAffirmations
            currentAffirmation = try await affirmationService.fetchCurrentAffirmation()

            // Update recent affirmations (last 5 non-current affirmations)
            recentAffirmations = Array(
                affirmations
                    .filter { $0.id != currentAffirmation?.id }
                    .sorted { $0.createdAt > $1.createdAt }
                    .prefix(5))

            await loadMostRecentAffirmation()
            lastCacheUpdate = Date()
            logger.info("Successfully updated affirmation cache")
        } catch {
            logger.error("Failed to load affirmations: \(error.localizedDescription)")
            await handleError(error)
        }
        print("[DEBUG] loadAffirmations: affirmations now has count = \(affirmations.count)")
        isLoading = false
    }

    /// Load statistics
    public func loadStatistics() async {
        do {
            statistics = try await affirmationService.getStatistics()
            streakStatistics = try await streakService.getStreakStatistics()
        } catch {
            await handleError(error)
        }
    }

    /// Create a new affirmation
    public func createAffirmation(
        text: String, category: AffirmationCategory, recordingURL: URL? = nil
    ) async {
        isLoading = true
        error = nil
        errorMessage = nil
        var finalRecordingURL: URL? = recordingURL
        if let tempURL = recordingURL {
            do {
                let permanentURL = try moveAudioToPermanentLocation(tempURL)
                finalRecordingURL = permanentURL
            } catch {
                await handleError(error)
                isLoading = false
                return
            }
        }
        do {
            let createdAffirmation = try await affirmationService.createAffirmation(
                text: text,
                category: category,
                recordingURL: finalRecordingURL
            )
            await loadAffirmations()
            if currentAffirmation == nil {
                currentAffirmation = createdAffirmation
                try await startCycle(for: createdAffirmation)
            }
            updateActivitiesForNewAffirmation(createdAffirmation)
            await hapticManager.playSuccess()
            await loadStatistics()
        } catch {
            if let url = finalRecordingURL {
                try? FileManager.default.removeItem(at: url)
            }
            await handleError(error)
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// Move audio file to a permanent location associated with the affirmation
    private func moveAudioToPermanentLocation(_ tempURL: URL) throws -> URL {
        let fileManager = FileManager.default
        let directory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let permanentURL = directory.appendingPathComponent(UUID().uuidString + ".m4a")
        if fileManager.fileExists(atPath: permanentURL.path) {
            try fileManager.removeItem(at: permanentURL)
        }
        try fileManager.moveItem(at: tempURL, to: permanentURL)
        return permanentURL
    }

    /// Delete an affirmation
    public func deleteAffirmation(_ affirmation: any AffirmationProtocol) async {
        isLoading = true
        error = nil
        errorMessage = nil
        do {
            try await affirmationService.deleteAffirmation(id: affirmation.id)
            await loadAffirmations()
            await loadStatistics()
            await hapticManager.playSuccess()
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// Toggle favorite status for an affirmation
    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async {
        let affirmationNL = affirmation as! any NeuroLoopInterfaces.AffirmationProtocol
        do {
            let updatedAffirmation = try await affirmationService.toggleFavorite(affirmationNL)
            if let index = affirmations.firstIndex(where: { $0.id == affirmationNL.id }) {
                affirmations[index] = updatedAffirmation
            }
            if currentAffirmation?.id == affirmationNL.id {
                currentAffirmation = updatedAffirmation
            }
            await hapticManager.playSelection()
            await loadStatistics()
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
    }

    /// Record a repetition for the current affirmation
    public func recordRepetition() async {
        guard let affirmation = currentAffirmation else {
            logger.error("Attempted to record repetition with no current affirmation")
            return
        }

        logger.info("Recording repetition for affirmation: \(affirmation.id)")
        do {
            let beforeRepetitionCount = affirmation.currentRepetitions
            logger.debug("Before repetition count: \(beforeRepetitionCount)")

            let result = try await repetitionService.recordRepetition(for: affirmation)

            if result.success {
                let afterRepetitionCount = result.updatedAffirmation.currentRepetitions
                logger.info(
                    """
                    Repetition recorded successfully:
                    - Before count: \(beforeRepetitionCount)
                    - After count: \(afterRepetitionCount)
                    - Difference: \(afterRepetitionCount - beforeRepetitionCount)
                    """)

                currentAffirmation = result.updatedAffirmation
                if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                    affirmations[index] = result.updatedAffirmation
                }

                updateMostRecentAffirmation(result.updatedAffirmation)
                updateActivitiesForRepetition(result.updatedAffirmation)

                if result.isCycleComplete {
                    logger.info("Cycle completed for affirmation: \(affirmation.id)")
                    await hapticManager.playCelebrationPattern()
                } else if result.isQuotaMet {
                    logger.info("Daily quota met for affirmation: \(affirmation.id)")
                    await hapticManager.playAffirmationCompletionPattern()
                } else {
                    await hapticManager.mediumImpact()
                }

                await loadStatistics()
            } else if let error = result.error {
                logger.error("Failed to record repetition: \(error.localizedDescription)")
                await handleError(error)
                await hapticManager.playError()
            }
        } catch {
            logger.error("Error recording repetition: \(error.localizedDescription)")
            await handleError(error)
            await hapticManager.playError()
        }
    }

    /// Start a new affirmation session
    public func startSession() async {
        isLoading = true
        error = nil
        errorMessage = nil
        do {
            // Load the most recent affirmation if none is current
            if currentAffirmation == nil {
                if let recent = mostRecentAffirmation {
                    currentAffirmation = recent
                } else {
                    // If no recent affirmation, fetch the first available one
                    let fetchedAffirmations = try await affirmationService.fetchAffirmations()
                    if let firstAffirmation = fetchedAffirmations.first {
                        currentAffirmation = firstAffirmation
                    }
                }
            }

            // Start a cycle for the current affirmation if needed
            if let affirmation = currentAffirmation {
                try await startCycle(for: affirmation)
            }

            // Load affirmations and statistics
            await loadAffirmations()
            await loadStatistics()

            await hapticManager.playSuccess()
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
        isLoading = false
    }

    /// View progress statistics
    public func viewProgress() async {
        await loadStatistics()
    }

    /// Start a new cycle for an affirmation
    public func startCycle(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
        async throws
    {
        let result = try await repetitionService.startCycle(for: affirmation)
        if result.success {
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = result.updatedAffirmation
            }
            currentAffirmation = result.updatedAffirmation
            updateActivitiesForCycleStart(result.updatedAffirmation)
            await hapticManager.playSuccess()
            await loadStatistics()
        } else if let error = result.error {
            await handleError(error)
        }
    }

    /// Set the current affirmation
    public func setCurrentAffirmation(_ affirmation: any AffirmationProtocol) async {
        // If affirmation is already any NeuroLoopInterfaces.AffirmationProtocol, use it directly; otherwise, set currentAffirmation to nil (or handle as needed)
        if let casted = affirmation as? any NeuroLoopInterfaces.AffirmationProtocol {
            // Check if we're actually changing the affirmation
            let isChangingAffirmation = currentAffirmation?.id != casted.id

            if isChangingAffirmation {
                print(
                    "AffirmationViewModel: Changing current affirmation from \(currentAffirmation?.id.uuidString ?? "nil") to \(casted.id.uuidString)"
                )

                // Update the current affirmation
                currentAffirmation = casted

                // Update tracking and activities
                updateMostRecentAffirmation(casted)
                updateActivitiesForCurrentAffirmation(casted)

                // Notify observers that the current affirmation has changed
                NotificationCenter.default.post(
                    name: Notification.Name("CurrentAffirmationChanged"),
                    object: nil,
                    userInfo: ["affirmationId": casted.id]
                )

                // Start a cycle if needed
                if let hasActiveCycle =
                    (casted as? (any NeuroLoopInterfaces.AffirmationProtocol & AnyObject))?
                    .hasActiveCycle,
                    !hasActiveCycle
                {
                    do {
                        try await startCycle(for: casted)
                    } catch {
                        await handleError(error)
                        await hapticManager.playError()
                    }
                }
            } else {
                print(
                    "AffirmationViewModel: Current affirmation unchanged: \(casted.id.uuidString)")
            }
        } else {
            currentAffirmation = nil

            // Notify observers that the current affirmation has been cleared
            NotificationCenter.default.post(
                name: Notification.Name("CurrentAffirmationChanged"),
                object: nil,
                userInfo: ["affirmationId": UUID()]
            )
        }
    }

    /// Start audio recording
    public func startRecording() async {
        do {
            try await audioRecordingService.startRecording()
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
    }

    /// Stop recording audio and return the file URL
    public func stopRecording() async -> URL? {
        do {
            return try await audioRecordingService.stopRecording()
        } catch {
            await handleError(error)
            await hapticManager.playError()
            return nil
        }
    }

    /// Start audio playback
    public func startPlayback() async {
        do {
            try await audioRecordingService.startPlayback()
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
    }

    /// Validate streaks
    public func validateStreaks() async {
        do {
            let result = try await streakService.validateStreaks()
            if result.success {
                await loadAffirmations()
                await loadStatistics()
                if result.brokenStreaks > 0 {
                    await hapticManager.playError()
                }
            } else if let error = result.error {
                await handleError(error)
                await hapticManager.playError()
            }
        } catch {
            await handleError(error)
            await hapticManager.playError()
        }
    }

    /// Get streak calendar data for the current affirmation
    public func getStreakCalendarData(numberOfDays: Int = 30) async -> [NeuroLoopInterfaces
        .StreakCalendarDay]
    {
        guard let affirmation = currentAffirmation else { return [] }
        return await streakService.getStreakCalendarData(
            for: affirmation, numberOfDays: numberOfDays)
    }

    /// Check if the current streak is at risk
    public func isCurrentStreakAtRisk() async -> Bool {
        guard let affirmation = currentAffirmation else { return false }
        return await streakService.isStreakAtRisk(for: affirmation)
    }

    // MARK: - Most Recent Affirmation Tracking
    private func updateMostRecentAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        mostRecentAffirmation = affirmation
        mostRecentAffirmationTimestamp = Date()
        let defaults = UserDefaults.standard
        defaults.set(affirmation.id.uuidString, forKey: mostRecentAffirmationKey)
        defaults.set(Date(), forKey: mostRecentAffirmationTimestampKey)
    }

    private func loadMostRecentAffirmation() async {
        let defaults = UserDefaults.standard
        guard let idString = defaults.string(forKey: mostRecentAffirmationKey),
            let id = UUID(uuidString: idString)
        else {
            mostRecentAffirmation = nil
            mostRecentAffirmationTimestamp = nil
            return
        }
        mostRecentAffirmationTimestamp =
            defaults.object(forKey: mostRecentAffirmationTimestampKey) as? Date
        if let affirmation = affirmations.first(where: { $0.id == id }) {
            mostRecentAffirmation = affirmation
        } else {
            // Try to fetch from service if not in memory
            if let fetched = try? await affirmationService.fetchAffirmation(id: id) {
                mostRecentAffirmation = fetched
            } else {
                // Affirmation was deleted
                mostRecentAffirmation = nil
                defaults.removeObject(forKey: mostRecentAffirmationKey)
                defaults.removeObject(forKey: mostRecentAffirmationTimestampKey)
            }
        }
    }

    // MARK: - Activity Management

    private func addActivity(title: String, subtitle: String, icon: String) {
        let activity = Activity(
            title: title,
            subtitle: subtitle,
            icon: icon,
            time: formatTimeAgo(Date())
        )
        recentActivities.insert(activity, at: 0)
        if recentActivities.count > maxRecentActivities {
            recentActivities.removeLast()
        }
    }

    private func formatTimeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    // MARK: - Activity Updates

    private func updateActivitiesForRepetition(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "Repetition Recorded"
        let subtitle = affirmation.text
        let icon = "checkmark.circle.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForNewAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "New Affirmation Created"
        let subtitle = affirmation.text
        let icon = "plus.circle.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForCycleStart(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "New Cycle Started"
        let subtitle = affirmation.text
        let icon = "arrow.triangle.2.circlepath"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    private func updateActivitiesForCurrentAffirmation(
        _ affirmation: any NeuroLoopInterfaces.AffirmationProtocol
    ) {
        let title = "Current Affirmation Changed"
        let subtitle = affirmation.text
        let icon = "star.fill"
        addActivity(title: title, subtitle: subtitle, icon: icon)
    }

    // MARK: - Computed Properties

    public var theme: Theme {
        themeManager.currentTheme
    }

    public var hasAffirmations: Bool {
        let result = !affirmations.isEmpty
        print(
            "[DEBUG] hasAffirmations accessed: \(result), affirmations count = \(affirmations.count)"
        )
        return result
    }

    public var canRecordRepetition: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.canPerformRepetition(for: affirmation)
    }

    public var currentProgress: Double {
        guard let affirmation = currentAffirmation else { return 0.0 }
        return repetitionService.getProgress(for: affirmation).todayProgress
    }

    public var currentCycleProgress: Double {
        guard let affirmation = currentAffirmation else { return 0.0 }
        return repetitionService.getProgress(for: affirmation).cycleProgress
    }

    public var currentRepetitions: Int {
        guard let affirmation = currentAffirmation else { return 0 }
        return repetitionService.getProgress(for: affirmation).currentRepetitions
    }

    public var totalRepetitions: Int {
        AffirmationConstants.DAILY_REPETITIONS
    }

    public var currentCycleDay: Int {
        guard let affirmation = currentAffirmation else { return 1 }
        return repetitionService.getProgress(for: affirmation).currentDay
    }

    public var totalCycleDays: Int {
        AffirmationConstants.CYCLE_DAYS
    }

    public var isCurrentCycleComplete: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.getProgress(for: affirmation).isCycleComplete
    }

    public var hasTodayQuotaMet: Bool {
        guard let affirmation = currentAffirmation else { return false }
        return repetitionService.getProgress(for: affirmation).hasTodayQuotaMet
    }

    public var timeUntilNextRepetition: TimeInterval? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.timeUntilNextRepetition(for: affirmation)
    }

    public var streakInfo: StreakInfo? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.getStreakInfo(for: affirmation)
    }

    public var progressInfo: ProgressInfo? {
        guard let affirmation = currentAffirmation else { return nil }
        return repetitionService.getProgress(for: affirmation)
    }

    // Add cached progress info getter
    public func getCachedProgressInfo(for affirmation: any AffirmationProtocol) -> ProgressInfo? {
        if let cached = progressCache[affirmation.id] {
            return cached
        }
        let progress = repetitionService.getProgress(for: affirmation)
        progressCache[affirmation.id] = progress
        return progress
    }

    // Add cached streak info getter
    public func getCachedStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo? {
        if let cached = streakCache[affirmation.id] {
            return cached
        }
        let streak = repetitionService.getStreakInfo(for: affirmation)
        streakCache[affirmation.id] = streak
        return streak
    }

    // Clear cache when needed
    private func clearCache() {
        affirmationCache.removeAll()
        progressCache.removeAll()
        streakCache.removeAll()
        lastCacheUpdate = nil
    }

    // MARK: - Carousel Management

    /// Syncs the carousel index with the current affirmation
    public func syncCarouselWithCurrentAffirmation() {
        guard let currentAffirmation = currentAffirmation else { return }

        if let index = affirmations.firstIndex(where: { $0.id == currentAffirmation.id }) {
            DispatchQueue.main.async {
                self.carouselIndex = index
            }
        }
    }

    /// Updates the carousel index and sets the corresponding affirmation as current
    @MainActor
    public func updateCarouselIndex(_ index: Int) {
        carouselIndex = index
        if index < affirmations.count {
            let selectedAffirmation = affirmations[index]
            Task {
                await setCurrentAffirmation(selectedAffirmation)
            }
        }
    }
}
