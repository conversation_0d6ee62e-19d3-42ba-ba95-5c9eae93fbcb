import NeuroLoopCore
import NeuroLoopModels
import Neuro<PERSON><PERSON>Shared  // Needed for HapticManager
import NeuroLoopTypes
import NeuroLoopUI
import SwiftUI
import NeuroLoopInterfaces
import Foundation
#if os(iOS)
import UIKit
#else
import AppKit
#endif

// MARK: - Simple Diagnostic View
@available(iOS 17.0, macOS 14.0, *)
struct SimpleDiagnosticView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(spacing: 20) {
            Text("NeuroLoop Diagnostic Tools")
                .font(.largeTitle)
                .bold()

            Text("This is a simplified diagnostic view to avoid protocol conformance issues.")
                .multilineTextAlignment(.center)
                .padding()

            Text("App Version: 1.0.0")
                .font(.headline)

            Text("Build: \(Date().formatted(date: .numeric, time: .standard))")
                .font(.subheadline)
                .foregroundColor(.secondary)

            // System Info
            systemInfoCard()

            Button("Return to Normal App") {
                // Create a temporary file to store the user's preference
                let fileManager = FileManager.default
                let tempDirectory = fileManager.temporaryDirectory
                let prefsFile = tempDirectory.appendingPathComponent("neuroloop_prefs.txt")

                do {
                    // Write "false" to the file to indicate diagnostic mode should be off
                    try "false".write(to: prefsFile, atomically: true, encoding: .utf8)
                    print("Preference saved: Diagnostic mode disabled")

                    // Restart the app (this is a hack, but it works for testing)
                    exit(0)
                } catch {
                    print("Error saving preference: \(error)")
                }
            }
            .padding()
            .background(themeManager.currentTheme.accentColor.asColor)
            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            .cornerRadius(8)

            Spacer()
        }
        .padding()
    }

    private func systemInfoCard() -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("System Information")
                .font(.headline)

            Divider()

            Group {
                #if os(iOS)
                infoRow(label: "iOS Version", value: UIDevice.current.systemVersion)
                infoRow(label: "Device Model", value: UIDevice.current.model)
                infoRow(label: "Device Name", value: UIDevice.current.name)
                #else
                infoRow(label: "OS Version", value: ProcessInfo.processInfo.operatingSystemVersionString)
                infoRow(label: "Device Name", value: Host.current().localizedName ?? "Unknown")
                #endif
                infoRow(label: "App Version", value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
                infoRow(label: "Build Number", value: Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown")
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }

    private func infoRow(label: String, value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.subheadline)
                .bold()

            Spacer()

            Text(value)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

@MainActor
class StartupPerformanceLog {
    static let shared = StartupPerformanceLog()
    private var events: [(String, TimeInterval)] = []
    private var startTime: Date?
    private init() {}
    func mark(_ label: String) {
        let now = Date()
        if startTime == nil { startTime = now }
        let elapsed = now.timeIntervalSince(startTime ?? now)
        events.append((label, elapsed))
        print("[Startup] \(label): \(elapsed)s")
    }
    func printSummary() {
        print("--- Startup Performance Summary ---")
        for (label, elapsed) in events {
            print("[Startup] \(label): \(elapsed)s")
        }
        if let total = events.last?.1 {
            print("[Startup] Total to interactive: \(total)s")
        }
        print("-----------------------------------")
    }
}

@MainActor
class StartupCoordinator {
    static let shared = StartupCoordinator()
    private init() {}

    func start() {
        StartupPerformanceLog.shared.mark("StartupCoordinator.start")
        // Initialize critical services synchronously
        do {
            _ = try ServiceFactory.shared.getServices()
            StartupPerformanceLog.shared.mark("Critical services initialized")
        } catch {
            print("[Startup] Error initializing critical services: \(error)")
        }
    }
}

@main
struct NeuroLoopApp: App {
    // Feature flag to easily switch between production and preview modes
    // Set to true for production mode, false for preview mode
    private let useProductionMode = true

    // DIAGNOSTIC MODE: Set to true to use diagnostic mode, false for normal app
    // Change this to true if you need to troubleshoot repetition counter issues
    private var useDiagnosticMode: Bool = false

    @StateObject private var navigationCoordinator = NeuroLoopShared.NavigationCoordinator()
    @StateObject private var themeManager = NeuroLoopCore.ThemeManager.shared
    @StateObject private var hapticManager = NeuroLoopTypes.HapticManager.shared

    init() {
        StartupPerformanceLog.shared.mark("App init")
        StartupCoordinator.shared.start()

        // Check if there's a preference file to disable diagnostic mode
        let fileManager = FileManager.default
        let tempDirectory = fileManager.temporaryDirectory
        let prefsFile = tempDirectory.appendingPathComponent("neuroloop_prefs.txt")

        if fileManager.fileExists(atPath: prefsFile.path) {
            do {
                let contents = try String(contentsOf: prefsFile, encoding: .utf8)
                if contents.trimmingCharacters(in: .whitespacesAndNewlines) == "false" {
                    useDiagnosticMode = false
                    print("Diagnostic mode disabled based on preference file")

                    // Delete the file so it doesn't persist
                    try fileManager.removeItem(at: prefsFile)
                }
            } catch {
                print("Error reading preference file: \(error)")
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            if #available(iOS 17.0, macOS 14.0, *) {
                if useDiagnosticMode {
                    // DIAGNOSTIC MODE
                    SimpleDiagnosticView()
                        .environmentObject(navigationCoordinator)
                        .environmentObject(themeManager)
                        .environmentObject(hapticManager)
                        .onAppear {
                            StartupPerformanceLog.shared.mark("SimpleDiagnosticView appeared")
                            print("DIAGNOSTIC MODE: Running simplified diagnostic view")
                        }
                } else if useProductionMode {
                    // PRODUCTION MODE
                    RootView()
                        .environmentObject(navigationCoordinator)
                        .environmentObject(themeManager)
                        .environmentObject(hapticManager)
                        .onAppear {
                            StartupPerformanceLog.shared.mark("RootView appeared")
                        }
                } else {
                    // PREVIEW MODE
                    MainTabView.preview()
                        .environmentObject(navigationCoordinator)
                        .environmentObject(themeManager)
                        .environmentObject(hapticManager)
                        .onAppear {
                            StartupPerformanceLog.shared.mark("MainTabView appeared")
                        }
                }
            } else {
                Text("This app requires iOS 17.0 or later for diagnostic mode")
                    .foregroundColor(.red)
                    .padding()
            }
        }
    }
}
