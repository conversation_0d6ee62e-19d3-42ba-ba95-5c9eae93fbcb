import Combine
// Modified with availability attribute
import Foundation

#if canImport(UIKit) && canImport(CoreHaptics)
    import UIKit
    import CoreHaptics
#endif

/// Manages haptic feedback
@MainActor
public final class HapticManager: HapticGenerating, ObservableObject {
    /// Shared instance of the haptic manager
    public static let shared = HapticManager()

    #if canImport(UIKit) && canImport(CoreHaptics)
        // UIKit feedback generators
        private lazy var lightImpactGenerator = UIImpactFeedbackGenerator(style: .light)
        private lazy var mediumImpactGenerator = UIImpactFeedbackGenerator(style: .medium)
        private lazy var heavyImpactGenerator = UIImpactFeedbackGenerator(style: .heavy)
        private lazy var selectionGenerator = UISelectionFeedbackGenerator()
        private lazy var notificationGenerator = UINotificationFeedbackGenerator()

        // CoreHaptics engine for more complex patterns
        private var engine: CHHapticEngine?
    #endif

    @Published public private(set) var isEnabled = true
    private var supportsHaptics: Bool = false

    private init() {
        #if canImport(UIKit) && canImport(CoreHaptics)
            // Check if device supports haptics
            supportsHaptics = CHHapticEngine.capabilitiesForHardware().supportsHaptics
            setupHapticEngine()
        #endif
    }

    /// Initialize the haptic manager
    public func initialize() {
        #if canImport(UIKit) && canImport(CoreHaptics)
            // Only prepare generators if haptics are supported and available
            guard supportsHaptics else { return }

            // Prepare all generators
            lightImpactGenerator.prepare()
            mediumImpactGenerator.prepare()
            heavyImpactGenerator.prepare()
            selectionGenerator.prepare()
            notificationGenerator.prepare()
        #endif
    }

    /// Set up the CoreHaptics engine
    private func setupHapticEngine() {
        #if canImport(UIKit) && canImport(CoreHaptics)
            guard supportsHaptics else { return }
            do {
                engine = try CHHapticEngine()
                try engine?.start()
                engine?.resetHandler = { [weak self] in
                    guard let self = self else { return }
                    do {
                        try self.engine?.start()
                    } catch {
                        print("Failed to restart haptic engine: \(error)")
                    }
                }
                engine?.stoppedHandler = { reason in
                    print("Haptic engine stopped: \(reason)")
                }
            } catch {
                print("Failed to create haptic engine: \(error)")
            }
        #endif
    }

    /// Enable or disable haptic feedback
    /// - Parameter enabled: Whether haptic feedback is enabled
    public func setEnabled(_ enabled: Bool) {
        isEnabled = enabled
    }

    /// Get whether haptic feedback is enabled
    /// - Returns: Whether haptic feedback is enabled
    public func isHapticsEnabled() -> Bool {
        return isEnabled
    }

    /// Generate a light impact haptic feedback
    public func lightImpact() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            lightImpactGenerator.impactOccurred()
        #else
            print("Light impact haptic")
        #endif
    }

    /// Generate a medium impact haptic feedback
    public func mediumImpact() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            mediumImpactGenerator.impactOccurred()
        #else
            print("Medium impact haptic")
        #endif
    }

    /// Generate a heavy impact haptic feedback
    public func heavyImpact() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            heavyImpactGenerator.impactOccurred()
        #else
            print("Heavy impact haptic")
        #endif
    }

    /// Generate a success haptic feedback
    public func playSuccess() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            notificationGenerator.notificationOccurred(.success)
        #else
            print("Success haptic")
        #endif
    }

    /// Generate an error haptic feedback
    public func playError() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            notificationGenerator.notificationOccurred(.error)
        #else
            print("Error haptic")
        #endif
    }

    /// Generate a warning haptic feedback
    public func playWarning() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            notificationGenerator.notificationOccurred(.warning)
        #else
            print("Warning haptic")
        #endif
    }

    /// Generate a selection haptic feedback
    public func playSelection() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            selectionGenerator.selectionChanged()
        #else
            print("Selection haptic")
        #endif
    }

    /// Play a custom pattern for affirmation completion
    public func playAffirmationCompletionPattern() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            if supportsHaptics, let engine = engine {
                do {
                    let intensity = CHHapticEventParameter(
                        parameterID: .hapticIntensity, value: 0.7)
                    let sharpness = CHHapticEventParameter(
                        parameterID: .hapticSharpness, value: 0.5)
                    let event1 = CHHapticEvent(
                        eventType: .hapticTransient, parameters: [intensity, sharpness],
                        relativeTime: 0)
                    let event2 = CHHapticEvent(
                        eventType: .hapticTransient,
                        parameters: [
                            CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.5),
                            CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.3),
                        ],
                        relativeTime: 0.1
                    )
                    let pattern = try CHHapticPattern(events: [event1, event2], parameters: [])
                    let player = try engine.makePlayer(with: pattern)
                    try player.start(atTime: 0)
                } catch {
                    print("Failed to play haptic pattern: \(error)")
                    await playSuccess()
                }
            } else {
                await playSuccess()
            }
        #else
            print("Affirmation completion haptic pattern")
        #endif
    }

    /// Play a celebration pattern for milestone achievements
    public func playCelebrationPattern() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            if supportsHaptics, let engine = engine {
                do {
                    var events: [CHHapticEvent] = []
                    events.append(
                        CHHapticEvent(
                            eventType: .hapticTransient,
                            parameters: [
                                CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                                CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.7),
                            ],
                            relativeTime: 0
                        ))
                    for i in 0..<3 {
                        let intensity = 0.8 - (Float(i) * 0.2)
                        events.append(
                            CHHapticEvent(
                                eventType: .hapticTransient,
                                parameters: [
                                    CHHapticEventParameter(
                                        parameterID: .hapticIntensity, value: intensity),
                                    CHHapticEventParameter(
                                        parameterID: .hapticSharpness, value: 0.5),
                                ],
                                relativeTime: TimeInterval(i + 1) * 0.15
                            ))
                    }
                    let pattern = try CHHapticPattern(events: events, parameters: [])
                    let player = try engine.makePlayer(with: pattern)
                    try player.start(atTime: 0)
                } catch {
                    print("Failed to play celebration pattern: \(error)")
                    await playSuccess()
                }
            } else {
                await playSuccess()
            }
        #else
            print("Celebration haptic pattern")
        #endif
    }

    /// Play a streak completion pattern for completing a streak
    /// This is a more elaborate pattern than the regular celebration pattern
    public func playStreakCompletionPattern() async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            if supportsHaptics, let engine = engine {
                do {
                    var events: [CHHapticEvent] = []
                    events.append(
                        CHHapticEvent(
                            eventType: .hapticTransient,
                            parameters: [
                                CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                                CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.8),
                            ],
                            relativeTime: 0
                        ))
                    for i in 0..<2 {
                        events.append(
                            CHHapticEvent(
                                eventType: .hapticTransient,
                                parameters: [
                                    CHHapticEventParameter(
                                        parameterID: .hapticIntensity, value: 0.8),
                                    CHHapticEventParameter(
                                        parameterID: .hapticSharpness, value: 0.5),
                                ],
                                relativeTime: 0.3 + Double(i) * 0.6
                            ))
                        events.append(
                            CHHapticEvent(
                                eventType: .hapticTransient,
                                parameters: [
                                    CHHapticEventParameter(
                                        parameterID: .hapticIntensity, value: 0.5),
                                    CHHapticEventParameter(
                                        parameterID: .hapticSharpness, value: 0.3),
                                ],
                                relativeTime: 0.4 + Double(i) * 0.6
                            ))
                    }
                    events.append(
                        CHHapticEvent(
                            eventType: .hapticContinuous,
                            parameters: [
                                CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.3),
                                CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.3),
                            ],
                            relativeTime: 1.5,
                            duration: 0.6
                        ))
                    events.append(
                        CHHapticEvent(
                            eventType: .hapticTransient,
                            parameters: [
                                CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                                CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0),
                            ],
                            relativeTime: 2.1
                        ))
                    let pattern = try CHHapticPattern(events: events, parameters: [])
                    let player = try engine.makePlayer(with: pattern)
                    try player.start(atTime: 0)
                } catch {
                    print("Failed to play streak completion pattern: \(error)")
                    await playCelebrationPattern()
                }
            } else {
                await playCelebrationPattern()
            }
        #else
            print("Streak completion haptic pattern")
        #endif
    }

    /// Play an impact haptic feedback with the specified style
    public func playImpact(style: ImpactStyle) async {
        guard isEnabled else { return }
        #if canImport(UIKit) && canImport(CoreHaptics)
            switch style {
            case .light:
                lightImpactGenerator.impactOccurred()
            case .medium:
                mediumImpactGenerator.impactOccurred()
            case .heavy:
                heavyImpactGenerator.impactOccurred()
            case .soft:
                let generator = UIImpactFeedbackGenerator(style: .soft)
                generator.impactOccurred()
            case .rigid:
                let generator = UIImpactFeedbackGenerator(style: .rigid)
                generator.impactOccurred()
            }
        #else
            print("Impact haptic: \(style)")
        #endif
    }
}
