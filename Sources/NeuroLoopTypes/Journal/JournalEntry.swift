import Foundation
import SwiftData

/// Represents a daily journal entry with mood tracking and thoughts
@Model
public class JournalEntry {
    public var id: UUID
    public var date: Date
    public var mood: MoodLevel
    public var thoughts: String
    public var affirmationSessionIds: [UUID] // Connected affirmation sessions
    public var createdAt: Date
    public var updatedAt: Date
    
    public init(
        id: UUID = UUID(),
        date: Date = Date(),
        mood: MoodLevel = .neutral,
        thoughts: String = "",
        affirmationSessionIds: [UUID] = [],
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.date = date
        self.mood = mood
        self.thoughts = thoughts
        self.affirmationSessionIds = affirmationSessionIds
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

/// Represents mood levels with associated colors and emojis
public enum MoodLevel: Int, CaseIterable, Codable {
    case veryLow = 1
    case low = 2
    case neutral = 3
    case high = 4
    case veryHigh = 5
    
    public var emoji: String {
        switch self {
        case .veryLow: return "😢"
        case .low: return "😕"
        case .neutral: return "😐"
        case .high: return "😊"
        case .veryHigh: return "😄"
        }
    }
    
    public var title: String {
        switch self {
        case .veryLow: return "Very Low"
        case .low: return "Low"
        case .neutral: return "Neutral"
        case .high: return "Good"
        case .veryHigh: return "Excellent"
        }
    }
    
    public var description: String {
        switch self {
        case .veryLow: return "Feeling down or struggling"
        case .low: return "Below average mood"
        case .neutral: return "Balanced and calm"
        case .high: return "Positive and upbeat"
        case .veryHigh: return "Fantastic and energized"
        }
    }
    
    public var color: String {
        switch self {
        case .veryLow: return "red"
        case .low: return "orange"
        case .neutral: return "yellow"
        case .high: return "green"
        case .veryHigh: return "blue"
        }
    }
}

/// Protocol for journal entry operations
public protocol JournalEntryProtocol {
    var id: UUID { get }
    var date: Date { get }
    var mood: MoodLevel { get }
    var thoughts: String { get }
    var affirmationSessionIds: [UUID] { get }
    var createdAt: Date { get }
    var updatedAt: Date { get }
}

extension JournalEntry: JournalEntryProtocol {}

/// Represents mood statistics over time
public struct MoodStatistics {
    public let averageMood: Double
    public let moodTrend: MoodTrend
    public let totalEntries: Int
    public let streakDays: Int
    public let moodDistribution: [MoodLevel: Int]
    
    public init(
        averageMood: Double,
        moodTrend: MoodTrend,
        totalEntries: Int,
        streakDays: Int,
        moodDistribution: [MoodLevel: Int]
    ) {
        self.averageMood = averageMood
        self.moodTrend = moodTrend
        self.totalEntries = totalEntries
        self.streakDays = streakDays
        self.moodDistribution = moodDistribution
    }
}

public enum MoodTrend {
    case improving
    case stable
    case declining
    
    public var emoji: String {
        switch self {
        case .improving: return "📈"
        case .stable: return "➡️"
        case .declining: return "📉"
        }
    }
    
    public var title: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Needs Attention"
        }
    }
}
