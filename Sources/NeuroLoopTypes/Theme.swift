import SwiftUI

/// Theme for the app
public struct Theme: Identifiable, <PERSON><PERSON><PERSON>, Codable {
    public let id: UUID
    public let name: String
    public let isBuiltIn: Bool
    public let primaryTextColor: CodableColor
    public let secondaryTextColor: CodableColor
    public let backgroundColor: ThemeFill
    public let cardBackgroundColor: ThemeFill
    public let accentColor: ThemeFill
    public let shadowColor: CodableColor
    public let errorColor: CodableColor
    public let successColor: CodableColor
    public let warningColor: CodableColor
    public let borderColor: CodableColor
    public let disabledColor: CodableColor

    // Computed properties for UI convenience
    public var description: String {
        isBuiltIn ? "Built-in theme" : "Custom theme"
    }

    public var backgroundGradient: SwiftUI.LinearGradient {
        backgroundColor.asGradient ?? SwiftUI.LinearGradient(
            gradient: SwiftUI.Gradient(colors: [backgroundColor.asColor, backgroundColor.asColor]),
            startPoint: .top,
            endPoint: .bottom
        )
    }

    /// Creates a new theme with the specified colors
    /// - Parameters:
    ///   - id: Unique identifier for the theme
    ///   - name: Name of the theme
    ///   - isBuiltIn: Whether the theme is built-in
    ///   - primaryTextColor: Primary text color
    ///   - secondaryTextColor: Secondary text color
    ///   - backgroundColor: Background color
    ///   - cardBackgroundColor: Card background color
    ///   - accentColor: Accent color
    ///   - shadowColor: Shadow color
    ///   - errorColor: Error color
    ///   - successColor: Success color
    ///   - warningColor: Warning color
    ///   - borderColor: Border color
    ///   - disabledColor: Disabled color
    public init(
        id: UUID = UUID(),
        name: String,
        isBuiltIn: Bool = false,
        primaryTextColor: CodableColor,
        secondaryTextColor: CodableColor,
        backgroundColor: ThemeFill,
        cardBackgroundColor: ThemeFill,
        accentColor: ThemeFill,
        shadowColor: CodableColor,
        errorColor: CodableColor,
        successColor: CodableColor,
        warningColor: CodableColor,
        borderColor: CodableColor,
        disabledColor: CodableColor
    ) {
        self.id = id
        self.name = name
        self.isBuiltIn = isBuiltIn
        self.primaryTextColor = primaryTextColor
        self.secondaryTextColor = secondaryTextColor
        self.backgroundColor = backgroundColor
        self.cardBackgroundColor = cardBackgroundColor
        self.accentColor = accentColor
        self.shadowColor = shadowColor
        self.errorColor = errorColor
        self.successColor = successColor
        self.warningColor = warningColor
        self.borderColor = borderColor
        self.disabledColor = disabledColor
    }

    // Custom Codable implementation for Color properties
    enum CodingKeys: String, CodingKey {
        case id, name, isBuiltIn,
        primaryTextColor, secondaryTextColor, backgroundColor, cardBackgroundColor, accentColor, shadowColor, errorColor, successColor, warningColor, borderColor, disabledColor
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        isBuiltIn = try container.decode(Bool.self, forKey: .isBuiltIn)
        primaryTextColor = try container.decode(CodableColor.self, forKey: .primaryTextColor)
        secondaryTextColor = try container.decode(CodableColor.self, forKey: .secondaryTextColor)
        backgroundColor = try container.decode(ThemeFill.self, forKey: .backgroundColor)
        cardBackgroundColor = try container.decode(ThemeFill.self, forKey: .cardBackgroundColor)
        accentColor = try container.decode(ThemeFill.self, forKey: .accentColor)
        shadowColor = try container.decode(CodableColor.self, forKey: .shadowColor)
        errorColor = try container.decode(CodableColor.self, forKey: .errorColor)
        successColor = try container.decode(CodableColor.self, forKey: .successColor)
        warningColor = try container.decode(CodableColor.self, forKey: .warningColor)
        borderColor = try container.decode(CodableColor.self, forKey: .borderColor)
        disabledColor = try container.decode(CodableColor.self, forKey: .disabledColor)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(isBuiltIn, forKey: .isBuiltIn)
        try container.encode(primaryTextColor, forKey: .primaryTextColor)
        try container.encode(secondaryTextColor, forKey: .secondaryTextColor)
        try container.encode(backgroundColor, forKey: .backgroundColor)
        try container.encode(cardBackgroundColor, forKey: .cardBackgroundColor)
        try container.encode(accentColor, forKey: .accentColor)
        try container.encode(shadowColor, forKey: .shadowColor)
        try container.encode(errorColor, forKey: .errorColor)
        try container.encode(successColor, forKey: .successColor)
        try container.encode(warningColor, forKey: .warningColor)
        try container.encode(borderColor, forKey: .borderColor)
        try container.encode(disabledColor, forKey: .disabledColor)
    }

    /// Default light theme
    @MainActor public static let light: Theme = Theme(
        name: "Light",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color(red: 0.1, green: 0.1, blue: 0.15)),
        secondaryTextColor: CodableColor(Color(red: 0.4, green: 0.4, blue: 0.45)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.95, green: 0.97, blue: 1.0),
                Color(red: 0.9, green: 0.92, blue: 0.95)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color.white.opacity(0.95),
                Color.white.opacity(0.9)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.2, green: 0.4, blue: 0.8),
                Color(red: 0.3, green: 0.5, blue: 0.9)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.15)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.gray.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Default dark theme
    @MainActor public static let dark: Theme = Theme(
        name: "Dark",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.gray.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.1, green: 0.1, blue: 0.15),
                Color(red: 0.05, green: 0.05, blue: 0.1)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.2, green: 0.2, blue: 0.25),
                Color(red: 0.15, green: 0.15, blue: 0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.3, green: 0.5, blue: 0.9),
                Color(red: 0.4, green: 0.6, blue: 1.0)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.4)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.gray.opacity(0.3)),
        disabledColor: CodableColor(Color.gray.opacity(0.6))
    )

    /// Blue theme with shadowed cards (Default - Perfect Theme)
    @MainActor public static let blue: Theme = Theme(
        name: "Ocean Blue",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.1, green: 0.4, blue: 0.8),
                Color(red: 0.0, green: 0.2, blue: 0.6)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.0, green: 0.5, blue: 1.0),
                Color(red: 0.0, green: 0.3, blue: 0.8)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.3, green: 0.6, blue: 1.0),
                Color(red: 0.2, green: 0.5, blue: 0.9)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Royal Purple theme - Light and elegant
    @MainActor public static let purple: Theme = Theme(
        name: "Royal Purple",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.4, green: 0.1, blue: 0.8),
                Color(red: 0.2, green: 0.0, blue: 0.6)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.5, green: 0.0, blue: 1.0),
                Color(red: 0.3, green: 0.0, blue: 0.8)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.6, green: 0.3, blue: 1.0),
                Color(red: 0.5, green: 0.2, blue: 0.9)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Forest Green theme - Natural and calming
    @MainActor public static let green: Theme = Theme(
        name: "Forest Green",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.1, green: 0.6, blue: 0.3),
                Color(red: 0.0, green: 0.4, blue: 0.2)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.0, green: 0.8, blue: 0.4),
                Color(red: 0.0, green: 0.6, blue: 0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.3, green: 0.9, blue: 0.5),
                Color(red: 0.2, green: 0.7, blue: 0.4)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Sunset Orange theme - Warm and energizing
    @MainActor public static let orange: Theme = Theme(
        name: "Sunset Orange",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.9, green: 0.4, blue: 0.1),
                Color(red: 0.7, green: 0.2, blue: 0.0)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 1.0, green: 0.5, blue: 0.0),
                Color(red: 0.8, green: 0.3, blue: 0.0)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 1.0, green: 0.6, blue: 0.3),
                Color(red: 0.9, green: 0.5, blue: 0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Rose Gold theme - Elegant and sophisticated
    @MainActor public static let roseGold: Theme = Theme(
        name: "Rose Gold",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.8, green: 0.4, blue: 0.5),
                Color(red: 0.6, green: 0.2, blue: 0.3)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.9, green: 0.5, blue: 0.6),
                Color(red: 0.7, green: 0.3, blue: 0.4)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 1.0, green: 0.6, blue: 0.7),
                Color(red: 0.8, green: 0.4, blue: 0.5)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )

    /// Teal Breeze theme - Fresh and modern
    @MainActor public static let teal: Theme = Theme(
        name: "Teal Breeze",
        isBuiltIn: true,
        primaryTextColor: CodableColor(Color.white),
        secondaryTextColor: CodableColor(Color.white.opacity(0.8)),
        backgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.0, green: 0.6, blue: 0.6),
                Color(red: 0.0, green: 0.4, blue: 0.4)
            ],
            startPoint: .top,
            endPoint: .bottom
        )),
        cardBackgroundColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.0, green: 0.8, blue: 0.8),
                Color(red: 0.0, green: 0.6, blue: 0.6)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        accentColor: ThemeFill.gradient(ThemeGradient(
            colors: [
                Color(red: 0.3, green: 0.9, blue: 0.9),
                Color(red: 0.2, green: 0.7, blue: 0.7)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )),
        shadowColor: CodableColor(Color.black.opacity(0.3)),
        errorColor: CodableColor(Color(red: 0.9, green: 0.2, blue: 0.2)),
        successColor: CodableColor(Color(red: 0.2, green: 0.8, blue: 0.2)),
        warningColor: CodableColor(Color(red: 0.9, green: 0.7, blue: 0.2)),
        borderColor: CodableColor(Color.white.opacity(0.2)),
        disabledColor: CodableColor(Color.gray.opacity(0.5))
    )
}

// Custom gradient type for theme
public struct ThemeGradient: Hashable, Codable {
    public let colors: [Color]
    public let startPoint: UnitPoint
    public let endPoint: UnitPoint

    public init(colors: [Color], startPoint: UnitPoint, endPoint: UnitPoint) {
        self.colors = colors
        self.startPoint = startPoint
        self.endPoint = endPoint
    }

    private enum CodingKeys: String, CodingKey {
        case colors
        case startPointX
        case startPointY
        case endPointX
        case endPointY
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let codableColors = try container.decode([CodableColor].self, forKey: .colors)
        self.colors = codableColors.map { $0.color }

        let startX = try container.decode(Double.self, forKey: .startPointX)
        let startY = try container.decode(Double.self, forKey: .startPointY)
        self.startPoint = UnitPoint(x: startX, y: startY)

        let endX = try container.decode(Double.self, forKey: .endPointX)
        let endY = try container.decode(Double.self, forKey: .endPointY)
        self.endPoint = UnitPoint(x: endX, y: endY)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        let codableColors = colors.map { CodableColor($0) }
        try container.encode(codableColors, forKey: .colors)
        try container.encode(startPoint.x, forKey: .startPointX)
        try container.encode(startPoint.y, forKey: .startPointY)
        try container.encode(endPoint.x, forKey: .endPointX)
        try container.encode(endPoint.y, forKey: .endPointY)
    }
}

/// Represents either a solid color or a gradient
public enum ThemeFill: Hashable, Codable {
    case color(Color)
    case gradient(ThemeGradient)

    public var asColor: Color {
        switch self {
        case .color(let color):
            return color
        case .gradient(let gradient):
            return gradient.colors.first ?? .clear
        }
    }

    public var asGradient: LinearGradient? {
        switch self {
        case .color:
            return nil
        case .gradient(let gradient):
            return LinearGradient(
                gradient: Gradient(colors: gradient.colors),
                startPoint: gradient.startPoint,
                endPoint: gradient.endPoint
            )
        }
    }

    private enum CodingKeys: String, CodingKey {
        case type
        case color
        case gradient
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)

        switch type {
        case "color":
            let color = try container.decode(CodableColor.self, forKey: .color)
            self = .color(color.color)
        case "gradient":
            let gradient = try container.decode(ThemeGradient.self, forKey: .gradient)
            self = .gradient(gradient)
        default:
            throw DecodingError.dataCorruptedError(
                forKey: .type,
                in: container,
                debugDescription: "Invalid type: \(type)"
            )
        }
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        case .color(let color):
            try container.encode("color", forKey: .type)
            try container.encode(CodableColor(color), forKey: .color)
        case .gradient(let gradient):
            try container.encode("gradient", forKey: .type)
            try container.encode(gradient, forKey: .gradient)
        }
    }
}

// Codable wrapper for Color
public struct CodableColor: Hashable, Codable {
    public let color: Color

    public init(_ color: Color) {
        self.color = color
    }

    private struct ColorComponents: Codable {
        let red: Double
        let green: Double
        let blue: Double
        let opacity: Double
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let components = try container.decode(ColorComponents.self)
        self.color = Color(red: components.red, green: components.green, blue: components.blue, opacity: components.opacity)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        #if os(iOS)
        let uiColor = UIColor(self.color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        let components = ColorComponents(red: Double(red), green: Double(green), blue: Double(blue), opacity: Double(alpha))
        #elseif os(macOS)
        let nsColor = NSColor(self.color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        nsColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        let components = ColorComponents(red: Double(red), green: Double(green), blue: Double(blue), opacity: Double(alpha))
        #endif
        try container.encode(components)
    }
}

// Helper extensions for Color <-> Hex
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    func toHex() -> String {
        #if canImport(UIKit)
        typealias NativeColor = UIColor
        #elseif canImport(AppKit)
        typealias NativeColor = NSColor
        #endif
        let native = NativeColor(self)
        var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
        native.getRed(&r, green: &g, blue: &b, alpha: &a)
        let rgba = (Int(a * 255) << 24) | (Int(r * 255) << 16) | (Int(g * 255) << 8) | Int(b * 255)
        return String(format: "%08X", rgba)
    }
}

// Helper extension for CodableColor
extension CodableColor {
    public var background: AnyView {
        return AnyView(color)
    }

    public var foreground: Color {
        return color
    }
}

// MARK: - Accessibility Color Helpers

extension Color {
    /// Returns true if the color is considered dark (luminance < 0.5)
    public var isDarkColor: Bool {
        #if canImport(UIKit)
        let uiColor = UIColor(self)
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        #elseif canImport(AppKit)
        let nsColor = NSColor(self)
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        nsColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        #endif
        // Standard luminance formula
        let luminance = 0.299 * Double(red) + 0.587 * Double(green) + 0.114 * Double(blue)
        return luminance < 0.5
    }
}
