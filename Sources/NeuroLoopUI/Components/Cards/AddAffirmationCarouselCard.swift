import SwiftUI
import NeuroLoopCore
import Neur<PERSON><PERSON>oopShared
import NeuroLoopTypes

/// A card component for adding new affirmations in the carousel
public struct AddAffirmationCarouselCard: View {
    let theme: NeuroLoopTypes.Theme
    let onTap: () -> Void

    public init(
        theme: NeuroLoopTypes.Theme,
        onTap: @escaping () -> Void
    ) {
        self.theme = theme
        self.onTap = onTap
    }

    public var body: some View {
        VStack(spacing: 20) {
            // Plus icon
            Image(systemName: "plus.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(theme.accentColor.asColor)

            // Title
            Text("Add New Affirmation")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(theme.primaryTextColor.asColor)
                .multilineTextAlignment(.center)

            // Subtitle
            Text("Create a personalized affirmation to add to your daily practice")
                .font(.body)
                .foregroundColor(theme.secondaryTextColor.asColor)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)

            // Action button
            Button(action: onTap) {
                HStack {
                    Image(systemName: "plus")
                        .font(.title3)
                    Text("Create Affirmation")
                        .fontWeight(.semibold)
                }
                .foregroundColor(theme.primaryTextColor.asColor)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(theme.accentColor.asColor)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
        .frame(height: 400) // Fixed height to match AffirmationCarouselCard
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(theme.cardBackgroundColor.asColor.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(theme.accentColor.asColor.opacity(0.3), lineWidth: 2)
                        .strokeBorder(style: StrokeStyle(lineWidth: 2, dash: [8, 4]))
                )
        )
        .padding(.horizontal, 16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Add new affirmation")
        .accessibilityHint("Tap to create a new affirmation for your daily practice")
    }
}

// MARK: - Preview
#if DEBUG
struct AddAffirmationCarouselCard_Previews: PreviewProvider {
    static var previews: some View {
        AddAffirmationCarouselCard(
            theme: .blue,
            onTap: {}
        )
        .previewLayout(.sizeThatFits)
        .padding()
    }
}
#endif
