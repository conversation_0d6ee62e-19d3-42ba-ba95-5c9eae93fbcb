import SwiftUI
import NeuroLoopCore
import NeuroLoopTypes

/// A reusable themed card background with shadow
public struct BlueCardBackground: View {
    @EnvironmentObject private var themeManager: ThemeManager
    private let cornerRadius: CGFloat

    public init(cornerRadius: CGFloat = 16) {
        self.cornerRadius = cornerRadius
    }

    public var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
                themeManager.currentTheme.cardBackgroundColor.asGradient ??
                LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.cardBackgroundColor.asColor,
                        themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .shadow(color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15, x: 0, y: 8)
    }
}

/// A reusable themed background gradient
public struct BlueBackgroundGradient: View {
    @EnvironmentObject private var themeManager: ThemeManager

    public init() {}

    public var body: some View {
        (themeManager.currentTheme.backgroundColor.asGradient ??
         LinearGradient(
            gradient: Gradient(colors: [
                themeManager.currentTheme.backgroundColor.asColor,
                themeManager.currentTheme.backgroundColor.asColor.opacity(0.8)
            ]),
            startPoint: .top,
            endPoint: .bottom
         ))
        .ignoresSafeArea()
    }
}

/// A container view that applies the blue card styling to its content
public struct BlueCard<Content: View>: View {
    private let content: Content
    private let cornerRadius: CGFloat

    public init(cornerRadius: CGFloat = 16, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.cornerRadius = cornerRadius
    }

    public var body: some View {
        content
            .padding()
            .background(BlueCardBackground(cornerRadius: cornerRadius))
    }
}

#if DEBUG
struct BlueCardBackground_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            BlueBackgroundGradient()

            VStack(spacing: 20) {
                BlueCard {
                    Text("Blue Card")
                        .font(.headline)
                        .foregroundColor(.white)
                }

                Text("Regular Text")
                    .font(.body)
                    .foregroundColor(.white)
                    .padding()
                    .background(BlueCardBackground())
            }
            .padding()
        }
    }
}
#endif
