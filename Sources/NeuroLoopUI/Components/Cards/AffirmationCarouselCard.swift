import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopShared
import NeuroLoopTypes

/// A card component specifically designed for the affirmation carousel
public struct AffirmationCarouselCard: View {
    let affirmation: any AffirmationProtocol
    let theme: NeuroLoopTypes.Theme
    let onTap: () -> Void

    public init(
        affirmation: any AffirmationProtocol,
        theme: NeuroLoopTypes.Theme,
        onTap: @escaping () -> Void
    ) {
        self.affirmation = affirmation
        self.theme = theme
        self.onTap = onTap
    }

    public var body: some View {
        VStack(spacing: 16) {
            // Affirmation text with quotation marks
            VStack(spacing: 8) {
                Text("\u{201C}")
                    .font(.system(size: 40, weight: .light, design: .serif))
                    .foregroundColor(theme.accentColor.asColor.opacity(0.6))

                Text(affirmation.text)
                    .font(.title2)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(theme.primaryTextColor.asColor)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)

                Text("\u{201D}")
                    .font(.system(size: 40, weight: .light, design: .serif))
                    .foregroundColor(theme.accentColor.asColor.opacity(0.6))
            }
            .padding(.horizontal, 20)

            // Category badge
            Text(affirmation.category.displayName)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(theme.primaryTextColor.asColor)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(theme.accentColor.asColor.opacity(0.2))
                )

            // Action button
            Button(action: onTap) {
                HStack {
                    Image(systemName: "play.circle.fill")
                        .font(.title3)
                    Text("Start Session")
                        .fontWeight(.semibold)
                }
                .foregroundColor(theme.primaryTextColor.asColor)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(theme.accentColor.asColor)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
        .frame(height: 400) // Fixed height for consistent carousel
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(theme.cardBackgroundColor.asColor)
                .shadow(
                    color: theme.shadowColor.asColor.opacity(0.1),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        .padding(.horizontal, 16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Affirmation: \(affirmation.text)")
        .accessibilityHint("Swipe left or right to see other affirmations, or tap to start session")
    }
}

// MARK: - Category Extension
private extension AffirmationCategory {
    var displayName: String {
        switch self {
        case .confidence:
            return "Confidence"
        case .mindfulness:
            return "Mindfulness"
        case .gratitude:
            return "Gratitude"
        case .health:
            return "Health"
        case .success:
            return "Success"
        case .relationships:
            return "Relationships"
        case .selfLove:
            return "Self Love"
        case .personalGrowth:
            return "Personal Growth"
        case .meditation:
            return "Meditation"
        case .abundance:
            return "Abundance"
        case .personal:
            return "Personal"
        case .custom:
            return "Custom"
        }
    }
}

// MARK: - Preview
#if DEBUG
struct AffirmationCarouselCard_Previews: PreviewProvider {
    static var previews: some View {
        AffirmationCarouselCard(
            affirmation: PreviewAffirmation(
                text: "I am confident, capable, and worthy of success in all areas of my life.",
                category: .confidence
            ),
            theme: .blue,
            onTap: {}
        )
        .previewLayout(.sizeThatFits)
        .padding()
    }
}

private struct PreviewAffirmation: AffirmationProtocol {
    let id = UUID()
    let text: String
    let category: AffirmationCategory
    let isActive = true
    let createdAt = Date()
    let updatedAt = Date()

    // Required protocol properties
    let isFavorite = false
    let hasRecording = false
    let todayProgress: Double = 0.5
    let cycleProgress: Double = 0.3
    let recordingURL: URL? = nil
    let completedCycles = 0
    let currentRepetitions = 5
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double = 0.8
    let moodRating: Int? = 4
    let notes: String? = nil
    let playCount = 10
    let hasActiveCycle = true
    let currentCycleDay = 3
    let cycleStartDate: Date? = Date()
    let dailyProgress: [Date: Int] = [:]
    let isCurrentCycleComplete = false
    let hasTodayQuotaMet = false
    let canPerformRepetition = true
    var longestStreak: Int = 5

    // Required protocol methods
    func recordRepetition() throws {}
    func updateEnergyLevel(_ level: Double) {}
    func recordMood(_ rating: Int, notes: String?) {}

    // Equatable conformance
    static func == (lhs: PreviewAffirmation, rhs: PreviewAffirmation) -> Bool {
        return lhs.id == rhs.id
    }
}
#endif
