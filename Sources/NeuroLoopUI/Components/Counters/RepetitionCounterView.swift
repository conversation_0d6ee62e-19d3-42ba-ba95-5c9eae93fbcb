import SwiftUI

/// RepetitionCounterView is a reusable SwiftUI component for displaying and interacting with repetition progress, including visual effects.
///
/// Usage:
/// ```swift
/// RepetitionCounterView(currentCount: 10, totalCount: 100) { ... }
/// ```
/// - Parameters:
///   - currentCount: Current repetition count
///   - totalCount: Total repetitions required
///   - onTap: Action to perform on tap (increments, triggers effects)
@available(macOS 10.15, iOS 13.0, *)
public struct RepetitionCounterView: View {
    public var currentCount: Int
    public var totalCount: Int
    public var onTap: () -> Void

    // Animation state
    @State private var animateProgress = false
    @State private var pulseEffect = false
    @State private var successPulse = false
    @State private var ringScale = 1.0

    // Colors
    private let progressColor = Color.white
    private let backgroundColor = Color.white.opacity(0.25)
    private let highlightColor = Color.white.opacity(0.8)
    private let successColor = Color.green

    public init(currentCount: Int, totalCount: Int, onTap: @escaping () -> Void) {
        self.currentCount = currentCount
        self.totalCount = totalCount
        self.onTap = onTap
    }

    public var body: some View {
        // Force view to update when currentCount changes - use a local variable to ensure it's captured
        let count = currentCount

        // Create a unique ID for this view based on the current count to force refresh
        // Using a deterministic ID based on count to prevent unnecessary redraws
        let viewId = "repetition-counter-\(count)"

        GeometryReader { geometry in
            ZStack {
                // Visual debugging removed for cleaner UI
                // Rectangle()
                //     .stroke(Color.red.opacity(0.3), lineWidth: 1)
                //     .background(Color.red.opacity(0.05))

                // Debug text removed for cleaner UI
                // Text("Counter: \(count)/\(totalCount)")
                //     .font(.system(size: 8))
                //     .foregroundColor(.yellow)
                //     .padding(4)
                //     .background(Color.black.opacity(0.5))
                //     .cornerRadius(4)
                //     .position(x: geometry.size.width / 2, y: 10)
                //     .zIndex(10) // Ensure it's on top

                // Background circle with subtle glow
                Circle()
                    .stroke(backgroundColor, lineWidth: 8)
                    .shadow(color: Color.white.opacity(0.2), radius: 4, x: 0, y: 0)

                // Highlight circle (subtle pulse animation)
                Circle()
                    .stroke(highlightColor, lineWidth: 1)
                    .scaleEffect(pulseEffect ? 1.05 : 0.95)
                    .opacity(pulseEffect ? 0.6 : 0.2)
                    .animation(
                        Animation.easeInOut(duration: 1.5)
                            .repeatForever(autoreverses: true),
                        value: pulseEffect
                    )

                // Success pulse ring (appears briefly when count increases)
                Circle()
                    .stroke(successColor.opacity(successPulse ? 0.7 : 0), lineWidth: 4)
                    .scaleEffect(successPulse ? 1.2 : 0.9)
                    .opacity(successPulse ? 0.8 : 0)
                    .animation(.easeOut(duration: 0.8), value: successPulse)

                // Progress circle with improved visual style
                Circle()
                    .trim(from: 0, to: CGFloat(count) / CGFloat(totalCount))
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                progressColor.opacity(0.9),
                                progressColor
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                    .scaleEffect(ringScale)
                    .animation(.easeInOut(duration: 0.5), value: count)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: ringScale)
                    .shadow(color: Color.white.opacity(0.3), radius: 2, x: 0, y: 0)

                // Center content with improved typography
                // Use a Text view with a unique ID to force updates
                Text("\(count)")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(progressColor)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                    .id("count-\(count)") // Force redraw when count changes with a deterministic ID
                    .scaleEffect(animateProgress ? 1.1 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: animateProgress)

                // Invisible button covering the entire area
                Circle()
                    .fill(Color.clear)
                    .contentShape(Circle())
                    .onTapGesture {
                        // Trigger tap animation (smaller than the success animation)
                        withAnimation {
                            animateProgress = true
                            ringScale = 1.05 // Slightly scale the ring on tap
                        }

                        // Reset animation after delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            animateProgress = false
                            ringScale = 1.0
                        }

                        // Call the onTap handler which will update the count
                        onTap()
                    }
            }
            .padding(6)
        }
        .aspectRatio(1, contentMode: .fit)
        .id(viewId) // Use the unique ID to force view recreation when count changes
        .onChange(of: currentCount) { oldValue, newValue in
            print("RepetitionCounterView: Count changed from \(oldValue) to \(newValue)")

            // Only animate if the count increased (not on initial load or reset)
            if newValue > oldValue {
                print("RepetitionCounterView: Triggering animations for count increase")

                // Trigger number animation
                withAnimation {
                    animateProgress = true
                }

                // Trigger success pulse animation
                withAnimation {
                    successPulse = true
                    ringScale = 1.1 // Make the ring slightly bigger
                }

                // Reset animations after delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    animateProgress = false
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                    successPulse = false
                    ringScale = 1.0
                }
            }
        }
        .onAppear {
            // Start the pulse animation when the view appears
            pulseEffect = true

            // Force an initial animation if needed
            if count > 0 {
                // Briefly animate to show the current progress
                withAnimation {
                    successPulse = true
                    ringScale = 1.05
                }

                // Reset after a short delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    successPulse = false
                    ringScale = 1.0
                }
            }
        }
    }
}

#if DEBUG
    @available(macOS 10.15, iOS 13.0, *)
    struct RepetitionCounterView_Previews: PreviewProvider {
        static var previews: some View {
            ZStack {
                // Add background to preview to see white elements
                Color.blue
                    .edgesIgnoringSafeArea(.all)

                RepetitionCounterView(
                    currentCount: 42,
                    totalCount: 100,
                    onTap: {}
                )
                .padding(50)
            }
        }
    }
#endif
