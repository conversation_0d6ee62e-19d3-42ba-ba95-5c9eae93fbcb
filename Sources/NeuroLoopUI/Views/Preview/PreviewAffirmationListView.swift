import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewAffirmationListView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: PreviewAffirmationListViewModel
    @State private var showingAddAffirmation = false
    @State private var showingAffirmationDetail = false
    @State private var selectedAffirmation: (any AffirmationProtocol)?
    @State private var selectedCategory: AffirmationCategory?
    @State private var showingCategoryPicker = false

    public init(repository: AffirmationRepositoryProtocol) {
        _viewModel = StateObject(
            wrappedValue: PreviewAffirmationListViewModel(repository: repository)
        )
    }

    public var body: some View {
        NavigationStack {
            List {
                ForEach(viewModel.affirmations, id: \.id) { affirmation in
                    AffirmationRow(affirmation: affirmation)
                        .onTapGesture {
                            selectedAffirmation = affirmation
                            showingAffirmationDetail = true
                        }
                }
                .onDelete { indexSet in
                    Task {
                        for index in indexSet {
                            await viewModel.deleteAffirmation(id: viewModel.affirmations[index].id)
                        }
                    }
                }
            }
            .navigationTitle(
                Text("Affirmations").foregroundColor(
                    themeManager.currentTheme.primaryTextColor.asColor)
            )
            .navigationBarTitleDisplayMode(.large)
            #if os(iOS)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button(action: { showingCategoryPicker = true }) {
                            HStack {
                                Image(systemName: "folder.fill")
                                Text(selectedCategory?.rawValue ?? "All Categories")
                            }
                        }
                    }

                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: { showingAddAffirmation = true }) {
                            Image(systemName: "plus")
                        }
                    }
                }
            #endif
            .task {
                await viewModel.loadAffirmations()
            }
            .sheet(isPresented: $showingAddAffirmation) {
                Text("Add Affirmation")
                // Temporarily replace with a simple view
                // PreviewAddAffirmationView(repository: viewModel.repository)
            }
            .sheet(isPresented: $showingAffirmationDetail) {
                if let affirmation = selectedAffirmation {
                    PreviewAffirmationDetailView(
                        viewModel: PreviewAffirmationDetailViewModel(affirmation: affirmation),
                        affirmation: affirmation
                    )
                }
            }
            .confirmationDialog(
                "Select Category",
                isPresented: $showingCategoryPicker,
                titleVisibility: .visible
            ) {
                Button("All Categories") {
                    selectedCategory = nil
                    Task {
                        await viewModel.loadAffirmations()
                    }
                }

                ForEach(AffirmationCategory.allCases, id: \.self) { category in
                    Button(category.rawValue) {
                        selectedCategory = category
                        Task {
                            await viewModel.loadAffirmations(category: category)
                        }
                    }
                }

                Button("Cancel", role: .cancel) {}
            }
        }
    }
}

private struct AffirmationRow: View {
    let affirmation: any AffirmationProtocol
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(affirmation.text)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Spacer()

                if affirmation.isFavorite {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                }
            }

            HStack {
                Text(affirmation.category.rawValue)
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)

                Spacer()

                Text("\(Int(affirmation.todayProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }

            ProgressView(value: affirmation.todayProgress)
                .tint(themeManager.currentTheme.accentColor.asColor)
        }
        .padding(.vertical, 8)
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewAffirmationListView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Use a simple preview instead of AsyncPreview
            // PreviewAffirmationListView(repository: MockAffirmationRepository())
            //     .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif
