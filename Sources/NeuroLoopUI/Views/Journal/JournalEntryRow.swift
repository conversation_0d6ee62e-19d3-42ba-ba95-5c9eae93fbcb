import SwiftUI
import Neuro<PERSON><PERSON><PERSON>ore
import NeuroLoopTypes

/// Row component for displaying journal entries in lists
@available(iOS 17.0, macOS 14.0, *)
public struct JournalEntryRow: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let entry: JournalEntry
    let onTap: () -> Void

    public init(entry: JournalEntry, onTap: @escaping () -> Void) {
        self.entry = entry
        self.onTap = onTap
    }

    public var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Mood indicator
                VStack(spacing: 4) {
                    Text(entry.mood.emoji)
                        .font(.title2)

                    Text(entry.mood.title)
                        .font(.caption2)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                }
                .frame(width: 60)

                // Entry content
                VStack(alignment: .leading, spacing: 8) {
                    // Date
                    Text(formatDate(entry.date))
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    // Thoughts preview
                    if !entry.thoughts.isEmpty {
                        Text(entry.thoughts)
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    } else {
                        Text("No thoughts recorded")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.disabledColor.color)
                            .italic()
                    }

                    // Connected affirmations indicator
                    if !entry.affirmationSessionIds.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "link")
                                .font(.caption2)
                            Text("\(entry.affirmationSessionIds.count) affirmation\(entry.affirmationSessionIds.count == 1 ? "" : "s")")
                                .font(.caption2)
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }

                Spacer()

                // Chevron
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.disabledColor.color)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(themeManager.currentTheme.primaryTextColor.color.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func formatDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let formatter = DateFormatter()

        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInYesterday(date) {
            return "Yesterday"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            formatter.dateFormat = "EEEE" // Day of week
            return formatter.string(from: date)
        } else {
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        }
    }
}

#if DEBUG
@available(iOS 17.0, macOS 14.0, *)
struct JournalEntryRow_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 12) {
            JournalEntryRow(
                entry: JournalEntry(
                    date: Date(),
                    mood: .high,
                    thoughts: "Had a great day today! Feeling really positive about my progress with affirmations."
                )
            ) {}

            JournalEntryRow(
                entry: JournalEntry(
                    date: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                    mood: .neutral,
                    thoughts: ""
                )
            ) {}

            JournalEntryRow(
                entry: JournalEntry(
                    date: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                    mood: .low,
                    thoughts: "Struggling a bit today but trying to stay positive. The affirmations help me remember my worth.",
                    affirmationSessionIds: [UUID(), UUID()]
                )
            ) {}
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .environmentObject(NeuroLoopCore.ThemeManager.shared)
    }
}
#endif
