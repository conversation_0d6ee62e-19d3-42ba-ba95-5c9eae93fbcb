import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

/// Main journal view with mood tracking and thoughts
@available(iOS 17.0, macOS 14.0, *)
public struct JournalView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: JournalViewModel
    @State private var thoughtsText: String = ""
    @State private var showingCalendar = false
    @State private var hasUnsavedChanges = false
    @State private var showingSaveConfirmation = false
    @FocusState private var isTextEditorFocused: Bool

    public init(repository: JournalRepositoryProtocol) {
        _viewModel = StateObject(wrappedValue: JournalViewModel(repository: repository))
    }

    public var body: some View {
        ZStack {
            // Background - matches main page exactly
            (themeManager.currentTheme.backgroundColor.asGradient
                ?? LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.backgroundColor.asColor,
                        themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Custom header with calendar button
                HStack {
                    Text("Journal")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                    Spacer()

                    Button(action: { showingCalendar = true }) {
                        Image(systemName: "calendar")
                            .font(.title2)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                .padding(.bottom, 20)

                ScrollView {
                    VStack(spacing: 20) {
                        // Header with date and mood stats
                        headerSection

                        // Today's mood selector
                        moodSelectorSection

                        // Thoughts section
                        thoughtsSection

                        // Recent entries
                        recentEntriesSection

                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .sheet(isPresented: $showingCalendar) {
            JournalCalendarView(viewModel: viewModel)
        }
        .overlay(
            // Save confirmation toast
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    if showingSaveConfirmation {
                        HStack(spacing: 8) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Thoughts saved!")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                                .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                        )
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                        .transition(.move(edge: .trailing).combined(with: .opacity))
                        .animation(
                            .spring(response: 0.5, dampingFraction: 0.8),
                            value: showingSaveConfirmation)
                    }
                }
                .padding(.trailing, 20)
                .padding(.bottom, 100)
            }
        )
        .onAppear {
            Task {
                await viewModel.loadTodaysEntry()
                // Sync the text field with current entry
                if let currentEntry = viewModel.currentEntry {
                    thoughtsText = currentEntry.thoughts
                }
            }
        }
        .onChange(of: viewModel.currentEntry) { _, newEntry in
            // Update text field when entry changes (e.g., when switching dates)
            if let entry = newEntry {
                thoughtsText = entry.thoughts
                hasUnsavedChanges = false
            }
        }
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 12) {
            // Date
            Text(viewModel.formatDate(viewModel.selectedDate))
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            // Mood statistics
            if let stats = viewModel.moodStatistics {
                HStack(spacing: 20) {
                    JournalStatCard(
                        title: "Streak",
                        value: "\(stats.streakDays)",
                        subtitle: "days",
                        icon: "flame.fill"
                    )

                    JournalStatCard(
                        title: "Trend",
                        value: stats.moodTrend.emoji,
                        subtitle: stats.moodTrend.title,
                        icon: "chart.line.uptrend.xyaxis"
                    )

                    JournalStatCard(
                        title: "Average",
                        value: String(format: "%.1f", stats.averageMood),
                        subtitle: "mood",
                        icon: "heart.fill"
                    )
                }
            }
        }
        .padding(.vertical, 10)
    }

    // MARK: - Mood Selector Section

    private var moodSelectorSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("How are you feeling?")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                Spacer()
            }

            HStack(spacing: 12) {
                ForEach(MoodLevel.allCases, id: \.self) { mood in
                    MoodButton(
                        mood: mood,
                        isSelected: viewModel.currentEntry?.mood == mood,
                        action: {
                            Task {
                                await viewModel.updateMood(mood)
                            }
                        }
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3), radius: 15,
                    x: 0, y: 8)
        )
    }

    // MARK: - Thoughts Section

    private var thoughtsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Your thoughts")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                Spacer()

                if hasUnsavedChanges {
                    Button("Save") {
                        Task {
                            await saveThoughts()
                        }
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(themeManager.currentTheme.accentColor.asColor.opacity(0.2))
                    )
                }
            }

            TextEditor(text: $thoughtsText)
                .frame(minHeight: 120)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    themeManager.currentTheme.primaryTextColor.asColor.opacity(0.2),
                                    lineWidth: 1)
                        )
                )
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                .scrollContentBackground(.hidden)
                .focused($isTextEditorFocused)
                .overlay(alignment: .topLeading) {
                    if thoughtsText.isEmpty && !isTextEditorFocused {
                        Text("Write your thoughts here...")
                            .font(.body)
                            .foregroundColor(
                                themeManager.currentTheme.secondaryTextColor.asColor.opacity(0.7)
                            )
                            .padding(18)
                    }
                }
                .toolbar {
                    ToolbarItemGroup(placement: .keyboard) {
                        Spacer()
                        Button("Done") {
                            isTextEditorFocused = false
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
                .onChange(of: thoughtsText) { _, newValue in
                    // Mark as having unsaved changes instead of auto-saving
                    if newValue != viewModel.currentEntry?.thoughts {
                        hasUnsavedChanges = true
                    }
                }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3), radius: 15,
                    x: 0, y: 8)
        )
    }

    // MARK: - Recent Entries Section

    private var recentEntriesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Entries")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                Spacer()
            }

            if viewModel.recentEntries.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "book.pages")
                        .font(.title)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

                    Text("No entries yet")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

                    Text("Start by selecting your mood and writing your thoughts above")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .multilineTextAlignment(.center)
                }
                .padding(.vertical, 20)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.recentEntries.prefix(5), id: \.id) { entry in
                        JournalEntryRow(entry: entry) {
                            Task {
                                await viewModel.loadEntry(for: entry.date)
                            }
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3), radius: 15,
                    x: 0, y: 8)
        )
    }

    // MARK: - Private Methods

    private func saveThoughts() async {
        // Dismiss keyboard first
        isTextEditorFocused = false

        await viewModel.updateThoughts(thoughtsText)
        hasUnsavedChanges = false
        showingSaveConfirmation = true

        // Hide confirmation after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            showingSaveConfirmation = false
        }
    }
}

// MARK: - Supporting Views

@available(iOS 17.0, macOS 14.0, *)
struct JournalStatCard: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let title: String
    let value: String
    let subtitle: String
    let icon: String?

    var body: some View {
        VStack(spacing: 4) {
            if let icon = icon {
                Image(systemName: icon)
                    .font(.body)
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            }

            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            Text(subtitle)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 65)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.currentTheme.primaryTextColor.asColor.opacity(0.1))
        )
    }
}

@available(iOS 17.0, macOS 14.0, *)
struct MoodButton: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let mood: MoodLevel
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(mood.emoji)
                    .font(.title2)

                Text(mood.title)
                    .font(.caption2)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isSelected
                            ? themeManager.currentTheme.accentColor.asColor.opacity(0.3)
                            : themeManager.currentTheme.primaryTextColor.asColor.opacity(0.1)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isSelected
                                    ? themeManager.currentTheme.accentColor.asColor
                                    : Color.clear,
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
