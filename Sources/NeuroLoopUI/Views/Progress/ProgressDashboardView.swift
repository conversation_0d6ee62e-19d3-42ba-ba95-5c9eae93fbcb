import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

public struct ProgressDashboardView: View {
    @ObservedObject var viewModel: ProgressDashboardViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var selectedFilter: ProgressDashboardViewModel.Filter = .all
    @State private var searchText: String = ""
    @State private var showSubscriptionSheet = false
    @State private var debugOverlayEnabled = false
    @State private var redrawCount = 0
    @State private var peakMemoryUsage: UInt64 = 0
    @State private var isLoading: Bool = true

    private let premiumUnlocker = PremiumFeatureUnlocker(
        featureName: "Advanced Analytics", premiumService: PremiumService.shared)

    public init(viewModel: ProgressDashboardViewModel) {
        self.viewModel = viewModel
    }

    @ViewBuilder
    private func metricsSection() -> some View {
        // Key Metrics
        HStack(spacing: 16) {
            ProgressMetricCard(title: "Active", value: "\(viewModel.activeCount)")
                .accessibilityLabel("Active affirmations")
                .accessibilityValue("\(viewModel.activeCount)")
            ProgressMetricCard(title: "Completed", value: "\(viewModel.completedCount)")
                .accessibilityLabel("Completed affirmations")
                .accessibilityValue("\(viewModel.completedCount)")
            ProgressMetricCard(title: "Not Started", value: "\(viewModel.notStartedCount)")
                .accessibilityLabel("Not started affirmations")
                .accessibilityValue("\(viewModel.notStartedCount)")
        }
        .padding(.horizontal)

        // Streak Info
        HStack(spacing: 16) {
            ProgressMetricCard(title: "Current Streak", value: "\(viewModel.currentStreak)")
                .accessibilityLabel("Current streak")
                .accessibilityValue("\(viewModel.currentStreak) days")
            ProgressMetricCard(title: "Longest Streak", value: "\(viewModel.longestStreak)")
                .accessibilityLabel("Longest streak")
                .accessibilityValue("\(viewModel.longestStreak) days")
            ProgressMetricCard(
                title: "Avg. Consistency",
                value: String(format: "%.1f%%", viewModel.averageConsistency * 100)
            )
            .accessibilityLabel("Average consistency")
            .accessibilityValue(String(format: "%.1f percent", viewModel.averageConsistency * 100))
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private func journalAnalyticsSection() -> some View {
        VStack(alignment: .center, spacing: 16) {
            Text("Journal Analytics")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                .accessibilityAddTraits(.isHeader)

            if let moodStats = viewModel.moodStatistics {
                HStack(spacing: 16) {
                    ProgressMetricCard(title: "Journal Streak", value: "\(viewModel.journalStreak)")
                        .accessibilityLabel("Journal streak")
                        .accessibilityValue("\(viewModel.journalStreak) days")

                    ProgressMetricCard(
                        title: "Total Entries", value: "\(viewModel.totalJournalEntries)"
                    )
                    .accessibilityLabel("Total journal entries")
                    .accessibilityValue("\(viewModel.totalJournalEntries)")

                    ProgressMetricCard(
                        title: "Avg. Mood",
                        value: String(format: "%.1f", viewModel.averageMood)
                    )
                    .accessibilityLabel("Average mood")
                    .accessibilityValue(String(format: "%.1f out of 5", viewModel.averageMood))
                }

                // Mood trend indicator
                HStack {
                    Text("Mood Trend:")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

                    Text(viewModel.moodTrend.emoji)
                        .font(.title2)

                    Text(viewModel.moodTrend.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(moodTrendColor(viewModel.moodTrend))

                    Spacer()
                }
            } else {
                Text("Start journaling to see analytics")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.2), radius: 10,
                    x: 0, y: 4)
        )
        .padding(.horizontal)
    }

    private func moodTrendColor(_ trend: MoodTrend) -> Color {
        switch trend {
        case .improving:
            return themeManager.currentTheme.successColor.asColor
        case .declining:
            return themeManager.currentTheme.errorColor.asColor
        case .stable:
            return themeManager.currentTheme.primaryTextColor.asColor
        }
    }

    @ViewBuilder
    private func analyticsSection() -> some View {
        premiumUnlocker.premiumFeatureView(
            .advancedAnalytics, onUpgrade: { showSubscriptionSheet = true }
        ) {
            if let chartData = viewModel.chartData {
                ProgressChartView(data: chartData)
                    .frame(height: 180)
                    .padding(.horizontal)
                    .accessibilityHidden(true)
            }
        }
        .sheet(isPresented: $showSubscriptionSheet) {
            SubscriptionView(viewModel: SubscriptionViewModel(premiumService: PremiumService()))
        }
    }

    @ViewBuilder
    private func mainContentVStack() -> some View {
        LazyVStack(spacing: 24) {
            // Overall Progress
            ModernProgressCard(
                title: "Overall Progress",
                progress: viewModel.overallCompletionRate,
                progressText: "\(Int(viewModel.overallCompletionRate * 100))% Complete"
            )
            .padding()
            .background(
                ZStack {
                    // Glassmorphism effect
                    RoundedRectangle(cornerRadius: 16)
                        .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    themeManager.currentTheme.cardBackgroundColor.asGradient
                                        ?? LinearGradient(
                                            gradient: Gradient(colors: [
                                                themeManager.currentTheme.cardBackgroundColor
                                                    .asColor
                                            ]), startPoint: .top, endPoint: .bottom),
                                    lineWidth: 1
                                )
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.asColor.opacity(0.2),
                            radius: 15,
                            x: 0,
                            y: 5
                        )
                }
            )

            metricsSection()
            journalAnalyticsSection()
            analyticsSection()
        }
        .padding(.horizontal)
        .padding(.top)
        .padding(.bottom, 10)
        .optimizePerformance()
    }

    public var body: some View {
        ZStack {
            // Background with gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    themeManager.currentTheme.backgroundColor.asColor,
                    themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)

            if isLoading {
                DashboardSkeletonView()
            } else {
                ScrollView {
                    mainContentVStack()
                        .padding(.bottom, 100)  // Add bottom padding to avoid tab bar
                }
            }
        }
        .navigationTitle(
            Text("Progress").foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
        )
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            // Simulate loading for demo; replace with real async load if needed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
                isLoading = false
            }
        }
    }
}

private struct ProgressMetricCard: View {
    let title: String
    let value: String
    @EnvironmentObject private var themeManager: ThemeManager
    var body: some View {
        VStack {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            Text(title)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            Group {
                if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.cardBackgroundColor.asColor
                }
            }
        )
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel(title)
        .accessibilityValue(value)
    }
}

private struct ProgressChartView: View {
    let data: [Double]
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Weekly Progress")
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            GeometryReader { geometry in
                HStack(alignment: .bottom, spacing: 4) {
                    ForEach(data.indices, id: \.self) { idx in
                        VStack(spacing: 4) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            themeManager.currentTheme.accentColor.asColor,
                                            themeManager.currentTheme.accentColor.asColor.opacity(
                                                0.6),
                                        ]),
                                        startPoint: .top,
                                        endPoint: .bottom
                                    )
                                )
                                .frame(
                                    width: (geometry.size.width / CGFloat(data.count)) - 4,
                                    height: max(CGFloat(data[idx]) * geometry.size.height * 0.8, 4)
                                )
                                .animation(.easeInOut(duration: 0.5), value: data[idx])

                            Text(dayLabel(for: idx))
                                .font(.caption2)
                                .foregroundColor(
                                    themeManager.currentTheme.secondaryTextColor.asColor)
                        }
                    }
                }
            }
            .frame(height: 120)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.5))
        )
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("Weekly progress chart")
        .accessibilityValue("Shows completion rates for the last 7 days")
    }

    private func dayLabel(for index: Int) -> String {
        let calendar = Calendar.current
        let today = Date()
        let dayOffset = index - (data.count - 1)
        let date = calendar.date(byAdding: .day, value: dayOffset, to: today) ?? today
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
}

// MARK: - Dashboard Skeleton View

private struct DashboardSkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            // Progress bar skeleton
            SkeletonView(size: CGSize(width: 220, height: 24), cornerRadius: 12)
            // Metric cards skeleton
            HStack(spacing: 16) {
                ForEach(0..<3) { _ in
                    SkeletonView(size: CGSize(width: 90, height: 60), cornerRadius: 12)
                }
            }
            HStack(spacing: 16) {
                ForEach(0..<3) { _ in
                    SkeletonView(size: CGSize(width: 90, height: 60), cornerRadius: 12)
                }
            }
            // Chart skeleton
            SkeletonView(size: CGSize(width: 320, height: 120), cornerRadius: 16)
            // Filter/search skeleton
            SkeletonView(size: CGSize(width: 280, height: 32), cornerRadius: 8)
            // Affirmation cards skeleton
            ForEach(0..<3) { _ in
                SkeletonView(size: CGSize(width: 320, height: 56), cornerRadius: 12)
            }
        }
        .padding()
        .accessibilityHidden(true)
    }
}

// MARK: - Modern Progress Card

@available(iOS 17.0, macOS 14.0, *)
private struct ModernProgressCard: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    let title: String
    let progress: Double
    let progressText: String

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                .accessibilityAddTraits(.isHeader)

            // Progress Section
            VStack(alignment: .leading, spacing: 12) {
                // Progress Text
                Text(progressText)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                // Modern Progress Bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background Track
                        RoundedRectangle(cornerRadius: 8)
                            .fill(
                                themeManager.currentTheme.backgroundColor.asColor.opacity(0.3)
                            )
                            .frame(height: 12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(
                                        themeManager.currentTheme.borderColor.asColor.opacity(0.1),
                                        lineWidth: 1
                                    )
                            )

                        // Progress Fill with Gradient
                        RoundedRectangle(cornerRadius: 8)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        themeManager.currentTheme.accentColor.asColor,
                                        themeManager.currentTheme.accentColor.asColor.opacity(0.8)
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(
                                width: max(12, geometry.size.width * CGFloat(progress)),
                                height: 12
                            )
                            .shadow(
                                color: themeManager.currentTheme.accentColor.asColor.opacity(0.4),
                                radius: 4,
                                x: 0,
                                y: 2
                            )
                            .animation(.easeInOut(duration: 0.8), value: progress)
                    }
                }
                .frame(height: 12)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(progressText)")
        .accessibilityValue("Progress: \(Int(progress * 100)) percent")
    }
}

#if DEBUG
    struct ProgressDashboardView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Temporarily disabled due to async initialization issues
        }
    }
#endif
