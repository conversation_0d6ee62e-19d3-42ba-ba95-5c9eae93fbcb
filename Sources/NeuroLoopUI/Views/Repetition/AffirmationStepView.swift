import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

public struct AffirmationStepView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    let affirmation: String
    let repetitions: Int
    let onComplete: () -> Void
    @State private var currentRepetition = 0
    @State private var isAnimating = false

    public init(affirmation: String, repetitions: Int, onComplete: @escaping () -> Void) {
        self.affirmation = affirmation
        self.repetitions = repetitions
        self.onComplete = onComplete
    }

    public var body: some View {
        VStack(spacing: 24) {
            // Progress
            HStack {
                Text("Repetition \(currentRepetition + 1) of \(repetitions)")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)

                Spacer()

                let percent = Int((Double(currentRepetition) / Double(repetitions)) * 100)
                Text("\(percent)%")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
            }

            // Affirmation Card
            VStack(spacing: 16) {
                Text(affirmation)
                    .font(.title2)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)
                    .padding(.horizontal)
                    .padding(.top)

                HStack {
                    Image(systemName: "quote.bubble.fill")
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor.opacity(0.5))

                    Spacer()

                    Image(systemName: "quote.bubble.fill")
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor.opacity(0.5))
                        .rotationEffect(.degrees(180))
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .cardStyle()

            // Controls
            HStack(spacing: 20) {
                Button(action: {
                    if currentRepetition > 0 {
                        currentRepetition -= 1
                    }
                }) {
                    Image(systemName: "arrow.counterclockwise")
                        .font(.title2)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)
                }
                .disabled(currentRepetition == 0)
                .opacity(currentRepetition == 0 ? 0.5 : 1)

                Button(action: {
                    if currentRepetition < repetitions - 1 {
                        withAnimation {
                            currentRepetition += 1
                        }
                    } else {
                        onComplete()
                    }
                }) {
                    HStack {
                        Text(currentRepetition < repetitions - 1 ? "Next" : "Complete")
                        Image(
                            systemName: currentRepetition < repetitions - 1
                                ? "arrow.right" : "checkmark")
                    }
                    .frame(maxWidth: .infinity)
                }
                .actionButtonStyle(isPrimary: true)
            }
        }
        .padding()
    }
}

#Preview {
    AffirmationStepView(
        affirmation:
            "I am capable of achieving great things through consistent effort and dedication.",
        repetitions: 5,
        onComplete: {}
    )
    .environmentObject(ThemeManager.shared)
}
