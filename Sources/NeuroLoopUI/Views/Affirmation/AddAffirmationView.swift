import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

/// View for adding a new affirmation
public struct AddAffirmationView: View {
    // MARK: - Properties

    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @StateObject private var viewModel: AddAffirmationViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init(repository: AffirmationRepositoryProtocol) {
        _viewModel = StateObject(wrappedValue: AddAffirmationViewModel(repository: repository))
    }

    // MARK: - Body

    public var body: some View {
        Form {
            Section {
                TextField("Enter your affirmation", text: $viewModel.text, axis: .vertical)
                    .lineLimit(3...6)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    .accessibilityLabel(Text("Affirmation text input"))
                    .accessibilityHint(
                        Text(
                            "Enter your affirmation statement. This field supports dynamic type and VoiceOver."
                        ))
            } header: {
                Text("Affirmation Text")
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    .accessibilityAddTraits(.isHeader)
            } footer: {
                Text("Write a positive statement in the present tense")
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
            }

            Section {
                Toggle("Start immediately", isOn: $viewModel.startImmediately)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    .accessibilityLabel(Text("Start affirmation immediately toggle"))
                    .accessibilityHint(
                        Text(
                            "Enable to start the affirmation cycle today. This toggle is accessible to VoiceOver."
                        ))
            } header: {
                Text("Start Options")
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    .accessibilityAddTraits(.isHeader)
            } footer: {
                Text("If enabled, the affirmation will begin its cycle today")
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
            }
        }
        .navigationTitle(
            Text("Add Affirmation").foregroundColor(
                themeManager.currentTheme.primaryTextColor.asColor)
        )
        #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar(content: {
            AddAffirmationToolbar(viewModel: viewModel, dismiss: dismiss)
        })
    }
}

private struct AddAffirmationToolbar: ToolbarContent {
    @ObservedObject var viewModel: AddAffirmationViewModel
    let dismiss: DismissAction

    var body: some ToolbarContent {
        ToolbarItemGroup(placement: .automatic) {
            Button("Cancel") {
                #if canImport(UIKit)
                    if UIAccessibility.isReduceMotionEnabled {
                        dismiss()
                    } else {
                        withAnimation(.easeInOut) {
                            dismiss()
                        }
                    }
                #else
                    dismiss()
                #endif
            }
            .accessibilityLabel(Text("Cancel adding affirmation"))
            .accessibilityAddTraits(.isButton)
            .accessibilityHint(Text("Double tap to cancel and return to the previous screen."))

            Button("Add") {
                #if canImport(UIKit)
                    if UIAccessibility.isReduceMotionEnabled {
                        Task<Void, Never> {
                            await viewModel.save()
                            dismiss()
                        }
                    } else {
                        withAnimation(.easeInOut) {
                            _ = Task<Void, Never> {
                                await viewModel.save()
                                dismiss()
                            }
                        }
                    }
                #else
                    Task<Void, Never> {
                        await viewModel.save()
                        dismiss()
                    }
                #endif
            }
            .disabled(!viewModel.isValid)
            .accessibilityLabel(Text("Add affirmation"))
            .accessibilityHint(
                Text(
                    "Double tap to add the affirmation. This button is disabled until the affirmation is valid."
                )
            )
            .accessibilityAddTraits(.isButton)
        }
    }
}

// MARK: - Preview

#if DEBUG
    struct AddAffirmationView_Previews: PreviewProvider {
        static var previews: some View {
            let repository = AddAffirmationMockRepository()
            return NavigationStack {
                AddAffirmationView(repository: repository)
            }
        }
    }

    // UI-only build: Mark as @unchecked Sendable to suppress Sendable warnings
    private class AddAffirmationMockRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
        func fetchAffirmations() async throws -> [any AffirmationProtocol] {
            await [
                AffirmationStub(
                    text: "I am confident and capable",
                    category: AffirmationCategory.confidence,
                    recordingURL: nil,
                    isFavorite: false,
                    todayProgress: 0.0,
                    cycleProgress: 0.0
                )
            ]
        }

        func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
            await AffirmationStub(
                text: "I am confident and capable",
                category: AffirmationCategory.confidence,
                recordingURL: nil,
                isFavorite: false,
                todayProgress: 0.0,
                cycleProgress: 0.0
            )
        }

        func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
            async throws -> any AffirmationProtocol
        {
            await AffirmationStub(
                text: text,
                category: category,
                recordingURL: recordingURL,
                isFavorite: false,
                todayProgress: 0.0,
                cycleProgress: 0.0
            )
        }

        func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func deleteAffirmation(id: UUID) async throws {}
        func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any AffirmationProtocol]
        { [] }
        func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? { nil }
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws {}
        func startCycle(for affirmation: any AffirmationProtocol) async throws {}
    }
#endif
