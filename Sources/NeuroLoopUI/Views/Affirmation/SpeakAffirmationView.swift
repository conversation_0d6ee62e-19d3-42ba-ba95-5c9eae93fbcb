import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

#if os(iOS) || os(tvOS)
    import UIKit
#elseif os(macOS)
    import AppKit
#endif

// MARK: - Modern Notification Component

@available(iOS 17.0, macOS 14.0, *)
struct ModernNotificationView: View {
    let title: String
    let message: String
    let type: NotificationType
    let repetitionCount: Int?
    @Binding var isShowing: Bool
    @EnvironmentObject private var themeManager: ThemeManager

    @State private var dragOffset: CGFloat = 0
    @State private var showProgressAnimation = false

    enum NotificationType {
        case success
        case milestone
        case warning
        case error

        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .milestone: return "star.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .error: return "xmark.circle.fill"
            }
        }

        var iconColor: Color {
            switch self {
            case .success: return .green
            case .milestone: return .yellow
            case .warning: return .orange
            case .error: return .red
            }
        }

        #if os(iOS)
        var hapticType: UINotificationFeedbackGenerator.FeedbackType {
            switch self {
            case .success, .milestone: return .success
            case .warning: return .warning
            case .error: return .error
            }
        }
        #endif
    }

    var body: some View {
        HStack(spacing: 16) {
            // Contextual Icon with animation
            ZStack {
                Circle()
                    .fill(type.iconColor.opacity(0.2))
                    .frame(width: 44, height: 44)

                Image(systemName: type.icon)
                    .font(.title2)
                    .foregroundColor(type.iconColor)
                    .scaleEffect(showProgressAnimation ? 1.2 : 1.0)
                    .animation(
                        .spring(response: 0.3, dampingFraction: 0.6), value: showProgressAnimation)
            }

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.leading)

                // Progress indicator for repetition count
                if let count = repetitionCount {
                    HStack(spacing: 8) {
                        ProgressView(value: Double(count), total: 100)
                            .progressViewStyle(LinearProgressViewStyle(tint: .white))
                            .frame(height: 4)
                            .background(Color.white.opacity(0.3))
                            .clipShape(Capsule())

                        Text("\(count)/100")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.top, 4)
                }
            }

            Spacer()

            // Close button for easy dismissal
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    isShowing = false
                }
                #if os(iOS)
                triggerHaptic(.light)
                #else
                triggerHaptic("light")
                #endif
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title3)
                    .foregroundColor(.white.opacity(0.8))
                    .background(Color.black.opacity(0.2))
                    .clipShape(Circle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            // Glassmorphic background with current theme gradient
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    (themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.9),
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                )
                .background(
                    // Frosted glass effect
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.ultraThinMaterial)
                        .opacity(0.8)
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 20,
                    x: 0, y: 10
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        // Removed horizontal padding - now controlled by parent for consistent width
        .offset(x: isShowing ? dragOffset : 300)  // Slide from right
        .opacity(isShowing ? 1 : 0)
        .scaleEffect(isShowing ? 1 : 0.9)
        .animation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0), value: isShowing)
        .gesture(
            DragGesture()
                .onChanged { value in
                    // Only allow rightward swipes to dismiss (positive translation for right-side notifications)
                    if value.translation.width > 0 {
                        dragOffset = value.translation.width
                    }
                }
                .onEnded { value in
                    if value.translation.width > 50 {
                        // Dismiss if swiped right enough
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isShowing = false
                        }
                        #if os(iOS)
                        triggerHaptic(.light)
                        #else
                        triggerHaptic("light")
                        #endif
                    } else {
                        // Snap back
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            dragOffset = 0
                        }
                    }
                }
        )
        .onChange(of: isShowing) { oldValue, newValue in
            if newValue {
                // Trigger haptic feedback
                #if os(iOS)
                triggerHaptic(type.hapticType)
                #else
                triggerHaptic(type)
                #endif

                // Show progress animation for success/milestone
                if type == .success || type == .milestone {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        showProgressAnimation = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            showProgressAnimation = false
                        }
                    }
                }

                // Auto-dismiss after 2 seconds (optimized for carousel swiping)
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        isShowing = false
                    }
                }
            } else {
                // Reset states when hiding
                dragOffset = 0
                showProgressAnimation = false
            }
        }
    }

    #if os(iOS)
    private func triggerHaptic(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }

    private func triggerHaptic(_ type: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: type)
        generator.impactOccurred()
    }
    #else
    private func triggerHaptic(_ type: Any) {
        // No haptic feedback on macOS
    }
    #endif
}

/// A view for speaking and recording affirmations with a modern UI
@available(iOS 17.0, macOS 14.0, *)
public struct SpeakAffirmationView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var viewModel: SpeakAffirmationViewModel
    @StateObject private var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel
    @State private var showingConfirmation = false
    @State private var forceRefreshID = UUID()  // Used to force view refresh
    @State private var progressCardId = UUID()  // Used to force progress card refresh
    @State private var showMatchAnimation = false  // For visual feedback when auto-stop activates

    // MARK: - Carousel Properties
    @State private var carouselIndex: Int = 0
    @State private var showingAddAffirmationSheet = false

    // Affirmations data
    @State private var affirmations: [any AffirmationProtocol]
    private let onAffirmationCreated: ((any AffirmationProtocol) -> Void)?

    // Modern notification state
    @State private var showingNotification = false
    @State private var notificationTitle = ""
    @State private var notificationMessage = ""
    @State private var notificationType: ModernNotificationView.NotificationType = .success
    @State private var notificationRepetitionCount: Int? = nil

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        affirmationService: AffirmationServiceProtocol,
        streakService: StreakServiceProtocol,
        affirmations: [any AffirmationProtocol] = [],
        onAffirmationCreated: ((any AffirmationProtocol) -> Void)? = nil
    ) {
        // Store affirmations data
        _affirmations = State(initialValue: affirmations.isEmpty ? [affirmation] : affirmations)
        self.onAffirmationCreated = onAffirmationCreated

        _viewModel = StateObject(
            wrappedValue: SpeakAffirmationViewModel(
                affirmation: affirmation,
                repetitionService: repetitionService,
                audioService: Self.createAudioService(),
                affirmationService: affirmationService,
                streakService: streakService
            ))

        _trackingViewModel = StateObject(
            wrappedValue: NeuroLoopCore.RepetitionTrackingViewModel(
                affirmationService: affirmationService,
                repetitionService: repetitionService,  // Use the SAME service instance
                streakService: streakService,
                audioService: Self.createAudioService()
            ))

        print(
            "SpeakAffirmationView.init: Both view models using same repetition service: \(type(of: repetitionService))"
        )

        // Set initial carousel index to current affirmation
        let initialAffirmations = affirmations.isEmpty ? [affirmation] : affirmations
        if let currentIndex = initialAffirmations.firstIndex(where: { $0.id == affirmation.id }) {
            _carouselIndex = State(initialValue: currentIndex)
        }
    }

    // MARK: - Computed Properties

    /// The currently selected affirmation from the carousel
    private var currentAffirmation: any AffirmationProtocol {
        guard carouselIndex < affirmations.count else {
            return viewModel.affirmation
        }
        return affirmations[carouselIndex]
    }

    private static func createAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            return AudioRecordingService()
        #endif
    }

    // MARK: - Helper Methods

    // Enhanced status message function from FixedSpeakAffirmationView
    private func getStatusMessage() -> String {
        if viewModel.isRecording {
            if !viewModel.partialRecognitionText.isEmpty {
                return viewModel.partialRecognitionText
            } else {
                return "Listening..."
            }
        } else {
            return "Tap to start recording"
        }
    }

    // Helper method to show modern notification
    private func showNotification(
        title: String,
        message: String,
        type: ModernNotificationView.NotificationType = .success,
        repetitionCount: Int? = nil
    ) {
        print("🎉 DEBUG: showNotification called with title: '\(title)', message: '\(message)'")
        notificationTitle = title
        notificationMessage = message
        notificationType = type
        notificationRepetitionCount = repetitionCount
        print("🎉 DEBUG: Setting showingNotification to true...")
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showingNotification = true
        }
        print("🎉 DEBUG: showingNotification is now: \(showingNotification)")
    }

    // Convenience method for success notifications with repetition count
    private func showSuccessNotification(title: String, message: String, count: Int) {
        showNotification(title: title, message: message, type: .success, repetitionCount: count)
    }

    // Convenience method for milestone notifications
    private func showMilestoneNotification(title: String, message: String, count: Int) {
        showNotification(title: title, message: message, type: .milestone, repetitionCount: count)
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Use current theme background gradient
            (themeManager.currentTheme.backgroundColor.asGradient
                ?? LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.backgroundColor.asColor,
                        themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .ignoresSafeArea()

            // Content
            VStack(spacing: 24) {  // Increased overall spacing
                // Add more space at the top to avoid status bar and provide notification space
                Spacer()
                    .frame(height: 260)  // Increased from 200 to 260 (60 more points) to move all content down

                // MARK: - Debug UI Removed for Production
                // Testing Mode toggle and debug controls have been removed for clean production UI
                // Uncomment below if debug mode is needed for development:
                /*
                // Top bar with Testing Mode toggle
                HStack {
                    Spacer()
                
                    // Testing Mode Toggle - moved to top area
                    VStack(alignment: .trailing, spacing: 2) {
                        HStack {
                            Text("Testing Mode:")
                                .font(.caption)
                                .foregroundColor(.white)
                            Toggle("", isOn: $viewModel.debugBypassSpeechRecognition)
                                .scaleEffect(0.8)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.black.opacity(0.3))
                        .cornerRadius(8)
                
                        if viewModel.debugBypassSpeechRecognition {
                            Text("Bypasses speech verification")
                                .font(.caption2)
                                .foregroundColor(.gray)
                                .padding(.horizontal, 8)
                        }
                    }
                
                    Spacer()
                }
                .padding(.horizontal)
                .padding(.bottom, 20)
                */

                // Affirmation Carousel
                TabView(selection: $carouselIndex) {
                    // Existing affirmations
                    ForEach(Array(affirmations.enumerated()), id: \.element.id) { index, affirmation in
                        IntegratedAffirmationCardView(
                            text: affirmation.text,
                            currentCount: carouselIndex == index ? trackingViewModel.todayRepetitions : affirmation.currentRepetitions,
                            totalCount: 100,
                            currentDay: trackingViewModel.currentDay,
                            showRepeatExactly: true,
                            onRepeatExactly: {
                                // Call the playRecording method
                                Task {
                                    await viewModel.playRecording()
                                }
                            },
                            isRecording: viewModel.isRecording
                        )
                        .tag(index)
                    }

                    // Add New Affirmation Card
                    AddNewAffirmationCardView(
                        onTap: {
                            showingAddAffirmationSheet = true
                        }
                    )
                    .tag(affirmations.count)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                .frame(height: 320)
                .padding(.horizontal, 24)  // Match notification card width with consistent horizontal padding
                .padding(.top, 4)
                .padding(.bottom, 4)  // Reduced vertical padding
                .onChange(of: carouselIndex) { oldValue, newValue in
                    // Handle affirmation switching
                    if newValue < affirmations.count {
                        let selectedAffirmation = affirmations[newValue]
                        Task {
                            await switchToAffirmation(selectedAffirmation)
                        }
                    }

                    // Auto-dismiss notification when swiping to different affirmation
                    if showingNotification {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showingNotification = false
                        }
                    }
                }

                // Recording Button
                VStack(spacing: 8) {  // Reduced spacing
                    ZStack {
                        // Main recording button
                        RecordingButton(
                            isRecording: viewModel.isRecording,
                            onTap: {
                                print(
                                    "Recording button tapped, current state: \(viewModel.isRecording ? "recording" : "not recording")"
                                )
                                Task {
                                    if viewModel.isRecording {
                                        print("Stopping recording...")
                                        await viewModel.stopRecording()

                                        // Add a delay after stopping recording before updating UI
                                        // This ensures the recording button has fully completed its state change
                                        try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

                                        // Force refresh the progress card
                                        DispatchQueue.main.async {
                                            progressCardId = UUID()
                                            forceRefreshID = UUID()
                                            print(
                                                "SpeakAffirmationView: Forced refresh after stopping recording"
                                            )
                                        }
                                    } else {
                                        print("Starting recording...")
                                        await viewModel.startRecording()
                                    }
                                }
                            },
                            haptics: HapticManager.shared,
                            audioLevel: viewModel.currentAudioLevel
                        )
                        .scaleEffect(1.2)  // Make the button 20% larger
                        .accessibilityLabel(
                            viewModel.isRecording ? "Stop recording" : "Start recording"
                        )
                        .accessibilityHint(
                            "Double tap to \(viewModel.isRecording ? "stop" : "start") recording your affirmation"
                        )
                        .onAppear {
                            print("Recording button appeared, checking audio permissions...")
                        }
                    }

                    // Enhanced message text with visual feedback for matches
                    Text(getStatusMessage())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .frame(minWidth: 200, minHeight: 36)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            // Show green background when match is detected
                            RoundedRectangle(cornerRadius: 8)
                                .fill(showMatchAnimation ? Color.green.opacity(0.3) : Color.clear)
                                .animation(.easeInOut(duration: 0.3), value: showMatchAnimation)
                        )
                        .padding(.top, 10)

                    // MARK: - Debug Controls Removed for Production
                    // Debug buttons have been removed for clean production UI
                    // Uncomment below if debug controls are needed for development:
                    /*
                    // DEBUG CONTROLS - only show debug buttons when testing mode is ON
                    if viewModel.debugBypassSpeechRecognition {
                        VStack(spacing: 4) {
                            Button("Debug +1") {
                                Task {
                                    print("🔧 DEBUG: +1 button pressed")
                    
                                    // Use the tracking view model as the single source of truth
                                    await trackingViewModel.performRepetition()
                    
                                    // The tracking view model will handle the affirmation update
                                    // The main view model will get updated through the unified data flow
                                    print("🔧 DEBUG: Repetition performed, count should be updated")
                                }
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.green.opacity(0.8))
                            .foregroundColor(.white)
                            .cornerRadius(6)
                    
                            Button("Test Notification") {
                                print("🔧 DEBUG: Test notification button pressed")
                                print("🔧 DEBUG: showingNotification before: \(showingNotification)")
                                showNotification(
                                    title: "Test Notification",
                                    message: "This is a test notification to check positioning",
                                    type: .success,
                                    repetitionCount: 42
                                )
                                print("🔧 DEBUG: showingNotification after: \(showingNotification)")
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.8))
                            .foregroundColor(.white)
                            .cornerRadius(6)
                    
                            Button("Reset Count") {
                                Task {
                                    print("🔧 DEBUG: Reset button pressed")
                    
                                    // Use the tracking view model to reset
                                    await trackingViewModel.resetTodayRepetitions()
                    
                                    // The tracking view model will handle the affirmation update
                                    // The main view model will get updated through the unified data flow
                                    print("🔧 DEBUG: Reset complete, count now: \(trackingViewModel.todayRepetitions)")
                                }
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.red.opacity(0.8))
                            .foregroundColor(.white)
                            .cornerRadius(6)
                        }
                        .padding(.top, 8)
                    }
                    */
                }
                .padding(.top, 12)  // Add more space between button and text
                .padding(.vertical, 10)  // Reduced vertical padding for the whole recording section

                // Add bottom safe area spacing to avoid tab bar overlap
                Spacer(minLength: 40)  // Reduced from 100 to 40 to balance the increased top spacing (260 points)
            }
            // Removed container horizontal padding - now controlled per-element for consistent widths
            .padding(.bottom, 120)  // Further increased bottom padding to avoid tab bar
            .padding(.top, 0)  // Removed top padding since we added a larger spacer
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)  // Ensure content aligns from the top

            // Modern toast notification with consistent width
            if showingNotification {
                VStack {
                    Spacer()
                        .frame(height: 140)  // Decreased from 150 to 140 (10 points up) to move notification up

                    ModernNotificationView(
                        title: notificationTitle,
                        message: notificationMessage,
                        type: notificationType,
                        repetitionCount: notificationRepetitionCount,
                        isShowing: $showingNotification
                    )
                    .padding(.horizontal, 24)  // Match affirmation card width with consistent horizontal padding
                    .transition(
                        .asymmetric(
                            insertion: .move(edge: .trailing).combined(with: .opacity),
                            removal: .move(edge: .trailing).combined(with: .opacity)
                        )
                    )
                    .zIndex(1000)  // Ensure it appears above all other content

                    Spacer()
                }
                .allowsHitTesting(true)  // Ensure it can receive touches
            }
        }
        .sheet(isPresented: $showingAddAffirmationSheet) {
            AddAffirmationSheetView(
                onAffirmationCreated: { newAffirmation in
                    // Call the parent callback to add the affirmation to the data source
                    onAffirmationCreated?(newAffirmation)

                    // Add the new affirmation to the local carousel array
                    affirmations.append(newAffirmation)

                    // Close the sheet
                    showingAddAffirmationSheet = false

                    // Switch to the new affirmation in the carousel
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        // Set carousel to the new affirmation (last index)
                        carouselIndex = affirmations.count - 1

                        // Switch to the new affirmation
                        Task {
                            await switchToAffirmation(newAffirmation)
                        }
                    }
                }
            )
        }
        .alert("Exit Session?", isPresented: $showingConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Exit", role: .destructive) {
                // Clean up resources before dismissing
                Task {
                    await viewModel.stopRecording()
                    dismiss()
                }
            }
        } message: {
            Text("Your progress for this session will not be saved.")
        }
        .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
            return Alert(
                title: Text(alertItem.title),
                message: Text(alertItem.message),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // Debug prints for repetition progress tracking
            print("SpeakAffirmationView: View appeared")
            print(
                "SpeakAffirmationView: Rendering with todayRepetitions=\(viewModel.todayRepetitions)"
            )
            print("SpeakAffirmationView: Affirmation ID: \(viewModel.affirmation.id)")
            print("SpeakAffirmationView: Current day: \(viewModel.currentDay)")
            print("SpeakAffirmationView: Total repetitions: \(viewModel.totalRepetitions)")

            // Connect the view models
            viewModel.trackingViewModel = trackingViewModel
            print("SpeakAffirmationView: Connected trackingViewModel to viewModel")

            // Load data in the tracking view model with the affirmation ID
            Task {
                // Store the current count before loading to prevent reset
                let currentCount = trackingViewModel.todayRepetitions
                print("SpeakAffirmationView: Current count before loading: \(currentCount)")

                // CRITICAL: Only update service if we're not using a mock service
                // Don't override RootViewMockRepetitionService with ServiceFactory services
                if !viewModel.repetitionServiceType.contains("RootViewMockRepetitionService") {
                    do {
                        let currentService = try ServiceFactory.shared.getRepetitionServiceForMode(
                            debugMode: viewModel.debugBypassSpeechRecognition)
                        trackingViewModel.updateRepetitionService(currentService)
                        print(
                            "SpeakAffirmationView: Updated tracking view model with service for debug mode: \(viewModel.debugBypassSpeechRecognition)"
                        )
                    } catch {
                        print("SpeakAffirmationView: Error getting repetition service: \(error)")
                    }
                } else {
                    print(
                        "SpeakAffirmationView: Keeping RootViewMockRepetitionService, not overriding with ServiceFactory"
                    )
                    print(
                        "SpeakAffirmationView: Current service type: \(viewModel.repetitionServiceType)"
                    )
                }

                await trackingViewModel.loadData(for: viewModel.affirmation.id)

                // CRITICAL: Set the current affirmation in the tracking view model
                await MainActor.run {
                    trackingViewModel.updateAffirmation(viewModel.affirmation)
                    print(
                        "SpeakAffirmationView: Set current affirmation in tracking view model: \(viewModel.affirmation.text)"
                    )

                    // If we're in debug mode, check the DebugRepetitionCounter for the actual count
                    if viewModel.debugBypassSpeechRecognition {
                        let debugCount = DebugRepetitionCounter.shared.getCount(
                            for: viewModel.affirmation.id)
                        print(
                            "SpeakAffirmationView: Debug mode - DebugRepetitionCounter has count: \(debugCount)"
                        )
                        if debugCount > trackingViewModel.todayRepetitions {
                            print(
                                "SpeakAffirmationView: Syncing tracking view model to debug counter: \(debugCount)"
                            )
                            trackingViewModel.updateRepetitionCount(debugCount)
                        }
                    }

                    // If the count was reset during loading, restore it to prevent data loss
                    if trackingViewModel.todayRepetitions == 0 && currentCount > 0 {
                        print(
                            "SpeakAffirmationView: Count was reset during loading, restoring from \(trackingViewModel.todayRepetitions) to \(currentCount)"
                        )
                        trackingViewModel.updateRepetitionCount(currentCount)
                    }
                }

                print(
                    "SpeakAffirmationView: Loaded data in trackingViewModel, final count: \(trackingViewModel.todayRepetitions)"
                )
            }

            // Debug mode should be disabled by default for production builds
            viewModel.debugBypassSpeechRecognition = false
            print("SpeakAffirmationView: Debug mode disabled by default")

            // Ensure testing mode toggle is OFF by default to prevent count jumping issues
            print("SpeakAffirmationView: Testing mode is OFF - using standard repetition service")

            // Enable auto-stop by default for modern UX (from FixedSpeakAffirmationView)
            viewModel.autoStopEnabled = true
            print("SpeakAffirmationView: Auto-stop enabled by default for modern UX")

            // Set up notification observer for repetition count changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("RepetitionCountChanged"), object: nil, queue: .main
            ) { notification in
                print("SpeakAffirmationView: Received RepetitionCountChanged notification")
                print(
                    "SpeakAffirmationView: Notification userInfo: \(notification.userInfo ?? [:])")

                // Capture values to avoid MainActor isolation issues
                Task { @MainActor in
                    // Only update if the notification is for this affirmation
                    if let affirmationId = notification.userInfo?["affirmationId"] as? UUID,
                        affirmationId == viewModel.affirmation.id,
                        let count = notification.userInfo?["count"] as? Int
                    {
                        print(
                            "SpeakAffirmationView: Received notification of count change to \(count) for affirmation \(affirmationId)"
                        )
                        print(
                            "SpeakAffirmationView: Current trackingViewModel.todayRepetitions: \(trackingViewModel.todayRepetitions)"
                        )

                        // ONLY update if the count is actually different to prevent loops
                        if count != trackingViewModel.todayRepetitions {
                            trackingViewModel.updateRepetitionCount(count)

                            // Force refresh the view
                            forceRefreshID = UUID()
                            print(
                                "SpeakAffirmationView: Updated count and generated new forceRefreshID: \(forceRefreshID)"
                            )
                        } else {
                            print(
                                "SpeakAffirmationView: Count unchanged (\(count)), skipping update to prevent loops"
                            )
                        }
                    } else {
                        print(
                            "SpeakAffirmationView: Notification is for a different affirmation or missing count"
                        )
                    }
                }
            }
        }
        // Watch for alert changes and convert to modern notifications
        .onChange(of: viewModel.alertItem) { oldValue, newValue in
            if let alertItem = newValue {
                // Determine notification type based on alert content
                let type: ModernNotificationView.NotificationType
                if alertItem.title.contains("Error") || alertItem.title.contains("Failed") {
                    type = .error
                } else if alertItem.title.contains("Warning")
                    || alertItem.title.contains("Not Quite")
                {
                    type = .warning
                } else {
                    type = .success
                }

                // Show the modern notification
                showNotification(
                    title: alertItem.title,
                    message: alertItem.message,
                    type: type
                )

                // Clear the alert item to prevent the system alert
                viewModel.alertItem = nil

                print("🎉 Converted alert to modern notification: \(alertItem.title)")
            }
        }
        // Watch for repetition count changes and show enhanced notifications
        .onChange(of: trackingViewModel.todayRepetitions) { oldValue, newValue in
            print("🔍 NOTIFICATION DEBUG: Count changed from \(oldValue) to \(newValue)")
            print(
                "🔍 NOTIFICATION DEBUG: Conditions - newValue > oldValue: \(newValue > oldValue), oldValue > 0: \(oldValue > 0)"
            )

            // Only show notification if count increased (not on initial load)
            if newValue > oldValue && oldValue >= 0 {  // Changed from oldValue > 0 to oldValue >= 0 to catch first repetition
                print("🔍 NOTIFICATION DEBUG: Triggering notification for count increase")

                // Check for milestones
                let isMilestone =
                    newValue % 10 == 0 || newValue == 25 || newValue == 50 || newValue == 75
                    || newValue == 100

                if isMilestone {
                    print("🔍 NOTIFICATION DEBUG: Milestone detected at \(newValue)")
                    // Milestone notification with special messaging
                    let title = newValue == 100 ? "🎉 Daily Goal Complete!" : "🌟 Milestone Reached!"
                    let message =
                        newValue == 100
                        ? "Amazing! You've completed all 100 repetitions today!"
                        : "Fantastic! You've reached \(newValue) repetitions. Keep going!"
                    showMilestoneNotification(title: title, message: message, count: newValue)
                } else {
                    print("🔍 NOTIFICATION DEBUG: Regular success notification for \(newValue)")
                    // Regular success notification with varied messaging
                    let titles = [
                        "Great Job!", "Well Done!", "Excellent!", "Keep Going!", "Amazing!",
                    ]
                    let title = titles.randomElement() ?? "Great Job!"
                    let message = "Perfect! You've completed \(newValue) repetitions."
                    showSuccessNotification(title: title, message: message, count: newValue)
                }
                print("🎯 Progress card updated: \(oldValue) → \(newValue)")
            } else {
                print("🔍 NOTIFICATION DEBUG: Not triggering notification - conditions not met")
                if newValue == oldValue {
                    print("🔍 NOTIFICATION DEBUG: Count unchanged (\(newValue))")
                } else if newValue < oldValue {
                    print("🔍 NOTIFICATION DEBUG: Count decreased from \(oldValue) to \(newValue)")
                }
            }
        }
        .onDisappear {
            // Clean up resources when the view disappears
            print("SpeakAffirmationView: View is disappearing, cleaning up resources")
            print(
                "SpeakAffirmationView: Current repetition count before disappearing: \(trackingViewModel.todayRepetitions)"
            )

            Task {
                await viewModel.stopRecording()

                // CRITICAL: Ensure data persistence before disappearing
                print(
                    "SpeakAffirmationView: Ensuring data persistence with count: \(trackingViewModel.todayRepetitions)"
                )
                print("SpeakAffirmationView: Debug mode: \(viewModel.debugBypassSpeechRecognition)")

                // If we're in debug mode, the count is stored in the DebugRepetitionCounter
                // We need to ensure this data persists across view changes
                if viewModel.debugBypassSpeechRecognition {
                    print(
                        "SpeakAffirmationView: Debug mode - data should persist in DebugRepetitionCounter"
                    )
                    // The DebugRepetitionService uses a shared counter that should persist
                } else {
                    print(
                        "SpeakAffirmationView: Standard mode - data should persist in storage service"
                    )
                }

                print("SpeakAffirmationView: Data persistence handling complete")
            }
        }
        // MARK: - Debug Mode Handler Removed for Production
        // Debug mode change handler has been commented out for clean production UI
        // Uncomment below if debug mode functionality is needed for development:
        /*
        .onChange(of: viewModel.debugBypassSpeechRecognition) { oldValue, newValue in
            print("SpeakAffirmationView: Testing mode changed to \(newValue)")
        
            // Update the tracking view model with the appropriate service
            Task {
                let factory = ServiceFactory.shared
                do {
                    // Get the appropriate repetition service based on the new debug mode
                    let service = try factory.getRepetitionServiceForMode(debugMode: newValue)
                    print("SpeakAffirmationView: Got \(newValue ? "debug" : "standard") repetition service: \(type(of: service))")
        
                    // Update the tracking view model
                    trackingViewModel.updateRepetitionService(service)
                    print("SpeakAffirmationView: Updated tracking view model with \(newValue ? "debug" : "standard") service")
                } catch {
                    print("SpeakAffirmationView: Error getting repetition service: \(error)")
                }
            }
        }
        */
        .onChange(of: viewModel.partialRecognitionText) { oldValue, newValue in
            // Show visual feedback when we detect a good match
            if !newValue.isEmpty && viewModel.isRecording {
                // Simple word overlap check for visual feedback
                let normalizedSpoken = newValue.lowercased().trimmingCharacters(
                    in: .whitespacesAndNewlines)
                let normalizedTarget = viewModel.affirmation.text.lowercased().trimmingCharacters(
                    in: .whitespacesAndNewlines)

                let spokenWords = Set(
                    normalizedSpoken.components(separatedBy: .whitespacesAndNewlines).filter {
                        !$0.isEmpty
                    })
                let targetWords = Set(
                    normalizedTarget.components(separatedBy: .whitespacesAndNewlines).filter {
                        !$0.isEmpty
                    })

                let commonWords = spokenWords.intersection(targetWords)
                let similarity = Double(commonWords.count) / Double(targetWords.count)

                // Show green feedback when similarity is high (70%+)
                if similarity >= 0.7 {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showMatchAnimation = true
                    }

                    // Reset animation after a short delay
                    Task {
                        try? await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second
                        await MainActor.run {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showMatchAnimation = false
                            }
                        }
                    }
                }
            }
        }
        .id(forceRefreshID)  // Force the entire view to refresh when this changes
    }

    // MARK: - Helper Methods

    /// Switches to a different affirmation in the carousel
    private func switchToAffirmation(_ affirmation: any AffirmationProtocol) async {
        print("SpeakAffirmationView: Switching to affirmation: \(affirmation.text)")

        // Stop any current recording
        if viewModel.isRecording {
            await viewModel.stopRecording()
        }

        // Update the view model with the new affirmation
        await MainActor.run {
            viewModel.affirmation = affirmation
            trackingViewModel.updateAffirmation(affirmation)
        }

        // Load data for the new affirmation
        await trackingViewModel.loadData(for: affirmation.id)

        print("SpeakAffirmationView: Successfully switched to affirmation: \(affirmation.text)")
    }
}

// MARK: - Integrated Affirmation Card View

@available(iOS 17.0, macOS 14.0, *)
private struct IntegratedAffirmationCardView: View {
    let text: String
    let currentCount: Int
    let totalCount: Int
    let currentDay: Int
    var showRepeatExactly: Bool = false
    var onRepeatExactly: (() -> Void)? = nil
    var isRecording: Bool = false
    @State private var showingTips = false
    @State private var animateProgress = false
    @State private var pulseEffect = false
    @State private var successPulse = false
    @State private var ringScale = 1.0
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack {
            // Use current theme card background
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15,
                    x: 0, y: 8)

            VStack(spacing: 16) {
                HStack {
                    // Left quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color.opacity(0.5))
                    .padding(.leading, -10)
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 2,
                        x: 1, y: 1)

                    if showRepeatExactly {
                        // Repeat Exactly button
                        Button(action: {
                            onRepeatExactly?()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.white)
                                    .font(.system(size: 12))
                                Text("Repeat Exactly")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(themeManager.currentTheme.accentColor.asColor.opacity(0.3))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                        .padding(.leading, 8)
                    }

                    Spacer()

                    Button(action: {
                        showingTips = true
                    }) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.title3)
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                            .padding(8)
                            .contentShape(Rectangle())
                    }
                    .accessibilityLabel("Affirmation Tips")
                    .accessibilityHint("Double tap to show tips for effective affirmations")
                }

                // Main affirmation text
                Text(text)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .fixedSize(horizontal: false, vertical: true)
                    .minimumScaleFactor(0.7)
                    .lineSpacing(4)
                    .padding(.horizontal)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                // Bottom section with quotation mark and progress
                HStack(alignment: .bottom) {
                    // Progress circle in bottom left
                    VStack(spacing: 4) {
                        ZStack {
                            // Background circle with subtle glow
                            Circle()
                                .stroke(Color.white.opacity(0.25), lineWidth: 4)
                                .frame(width: 60, height: 60)
                                .shadow(color: Color.white.opacity(0.2), radius: 2, x: 0, y: 0)

                            // Highlight circle (subtle pulse animation)
                            Circle()
                                .stroke(Color.white.opacity(0.4), lineWidth: 1)
                                .frame(width: 60, height: 60)
                                .scaleEffect(pulseEffect ? 1.05 : 0.95)
                                .opacity(pulseEffect ? 0.6 : 0.2)
                                .animation(
                                    Animation.easeInOut(duration: 1.5)
                                        .repeatForever(autoreverses: true),
                                    value: pulseEffect
                                )

                            // Success pulse ring (appears briefly when count increases)
                            Circle()
                                .stroke(Color.green.opacity(successPulse ? 0.7 : 0), lineWidth: 3)
                                .frame(width: 60, height: 60)
                                .scaleEffect(successPulse ? 1.2 : 0.9)
                                .opacity(successPulse ? 0.8 : 0)
                                .animation(.easeOut(duration: 0.8), value: successPulse)

                            // Progress circle with improved visual style
                            Circle()
                                .trim(from: 0, to: CGFloat(currentCount) / CGFloat(totalCount))
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.9),
                                            Color.white,
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    ),
                                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                                )
                                .frame(width: 60, height: 60)
                                .rotationEffect(.degrees(-90))
                                .scaleEffect(ringScale)
                                .animation(.easeInOut(duration: 0.5), value: currentCount)
                                .animation(
                                    .spring(response: 0.3, dampingFraction: 0.7), value: ringScale
                                )
                                .shadow(color: Color.white.opacity(0.3), radius: 1, x: 0, y: 0)

                            // Count number
                            Text("\(currentCount)")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.white)
                                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                                .scaleEffect(animateProgress ? 1.1 : 1.0)
                                .animation(
                                    .spring(response: 0.3, dampingFraction: 0.7),
                                    value: animateProgress)
                        }

                        // Day indicator
                        HStack(spacing: 2) {
                            Image(systemName: "flame.fill")
                                .foregroundColor(.orange)
                                .font(.system(size: 8))
                            Text("Day \(currentDay)")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.white.opacity(0.8))
                        }
                    }
                    .padding(.leading, 8)

                    Spacer()

                    // Right quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.trailing, -10)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)
                }
            }
            .padding()
        }
        .padding(.horizontal)
        .onChange(of: currentCount) { oldValue, newValue in
            if newValue > oldValue {
                print(
                    "IntegratedAffirmationCardView: Count increased from \(oldValue) to \(newValue), triggering animations"
                )

                // Trigger number animation
                withAnimation {
                    animateProgress = true
                }

                // Trigger success pulse animation
                withAnimation {
                    successPulse = true
                    ringScale = 1.1  // Make the ring slightly bigger
                }

                // Reset animations after delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    animateProgress = false
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                    successPulse = false
                    ringScale = 1.0
                }
            }
        }
        .onAppear {
            // Start the pulse animation when the view appears
            pulseEffect = true

            // Force an initial animation if needed
            if currentCount > 0 {
                // Briefly animate to show the current progress
                withAnimation {
                    successPulse = true
                    ringScale = 1.05
                }

                // Reset after a short delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    successPulse = false
                    ringScale = 1.0
                }
            }
        }
        .sheet(isPresented: $showingTips) {
            TipsView()
        }
    }
}

// MARK: - Affirmation Card Content View

@available(iOS 17.0, macOS 14.0, *)
private struct AffirmationCardContentView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    let text: String
    var showRepeatExactly: Bool = false
    var onRepeatExactly: (() -> Void)? = nil
    var isRecording: Bool = false
    @State private var showingTips = false

    // Animation properties
    @State private var animationTrigger = false

    var body: some View {
        ZStack {
            // Use current theme card background
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15,
                    x: 0, y: 8)

            VStack(spacing: 16) {
                HStack {
                    // Left quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color.opacity(0.5))
                    .padding(.leading, -10)
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 2,
                        x: 1, y: 1)

                    if showRepeatExactly {
                        // Repeat Exactly button
                        Button(action: {
                            onRepeatExactly?()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.white)
                                    .font(.system(size: 12))
                                Text("Repeat Exactly")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(themeManager.currentTheme.accentColor.asColor.opacity(0.3))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                            // Add a subtle shadow for better visibility
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                        .padding(.leading, 8)
                    }

                    Spacer()

                    Button(action: {
                        // Show hints or tips when tapped
                        showingTips = true
                    }) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.title3)
                            .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                            .padding(8)
                            .contentShape(Rectangle())
                    }
                    .accessibilityLabel("Affirmation Tips")
                    .accessibilityHint("Double tap to show tips for effective affirmations")
                }

                // Main affirmation text (simple, no highlighting)
                Text(text)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .fixedSize(horizontal: false, vertical: true)  // Allow text to expand vertically
                    .minimumScaleFactor(0.7)  // Scale down text if needed to fit
                    .lineSpacing(4)  // Add some space between lines
                    .padding(.horizontal)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                HStack {
                    Spacer()

                    // Right quotation mark
                    Text(
                        """
                        "
                        """
                    )
                    .font(.system(size: 40, weight: .bold, design: .serif))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.trailing, -10)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)
                }
            }
            .padding()
        }
        .padding(.horizontal)
        .sheet(isPresented: $showingTips) {
            TipsView()
        }
    }
}

// MARK: - Tips View

@available(iOS 17.0, macOS 14.0, *)
private struct TipsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Tips for Effective Affirmations")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.bottom, 10)

                    TipItem(
                        icon: "text.quote",
                        title: "Speak Clearly",
                        description: "Pronounce each word clearly and with conviction."
                    )

                    TipItem(
                        icon: "ear",
                        title: "Listen to Yourself",
                        description:
                            "Pay attention to how your voice sounds. Speak with confidence."
                    )

                    TipItem(
                        icon: "arrow.clockwise",
                        title: "Repeat Exactly",
                        description:
                            "Use the 'Repeat Exactly' button to hear the correct pronunciation."
                    )

                    TipItem(
                        icon: "chart.bar.fill",
                        title: "Track Progress",
                        description: "Complete your daily repetitions to build a streak."
                    )

                    TipItem(
                        icon: "heart.fill",
                        title: "Feel the Meaning",
                        description: "Connect emotionally with the words as you speak them."
                    )
                }
                .padding()
            }
            .navigationTitle(
                Text("Speak Affirmation").foregroundColor(
                    themeManager.currentTheme.primaryTextColor.asColor)
            )
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                #if os(iOS) || os(watchOS) || os(tvOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #else
                    ToolbarItem(placement: .automatic) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #endif
            }
        }
    }
}

private struct TipItem: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
}

// RepeatExactlyButton removed - now integrated into the affirmation card

// MARK: - Wave Shape

/// Shape that draws a sine wave with configurable parameters
struct WaveShape: Shape {
    var phase: Double
    var amplitude: Double
    var frequency: Double

    // Make the shape animatable
    var animatableData: Double {
        get { phase }
        set { phase = newValue }
    }

    func path(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let midHeight = height / 2
        let wavelength = width / CGFloat(frequency)

        var path = Path()
        path.move(to: CGPoint(x: 0, y: midHeight))

        // Draw a sine wave across the width
        for x in stride(from: 0, through: width, by: 1) {
            let relativeX = x / wavelength
            let sine = sin(relativeX + CGFloat(phase))
            // Reduce amplitude to keep wave more contained
            let y = midHeight + sine * midHeight * CGFloat(amplitude) * 0.8
            path.addLine(to: CGPoint(x: x, y: y))
        }

        // Complete the path by adding lines to the bottom corners
        // Only fill to 80% of the height to reduce visual impact
        let bottomY = min(height, midHeight + midHeight * 0.8)
        path.addLine(to: CGPoint(x: width, y: bottomY))
        path.addLine(to: CGPoint(x: 0, y: bottomY))
        path.closeSubpath()

        return path
    }
}

// MARK: - Animated Wave View

/// A view that displays an animated wave that responds to audio levels
struct AnimatedWaveView: View {
    let isRecording: Bool
    let audioLevel: Double
    let waveIndex: Int
    let colors: [Color]
    var animationSpeed: Double = 1.0
    var containedInArea: Bool = true  // New parameter to control if wave is contained
    var maxOpacity: Double = 0.3  // Reduced default opacity

    @State private var phase: Double = 0

    var body: some View {
        WaveShape(
            phase: phase + Double(waveIndex) * 0.5,
            amplitude: isRecording ? audioLevel * 0.3 : 0.05,  // Reduced amplitude
            frequency: 1.0 + Double(waveIndex) * 0.2
        )
        .fill(
            LinearGradient(
                gradient: Gradient(colors: colors),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .opacity(min(maxOpacity, maxOpacity - Double(waveIndex) * 0.1))  // Significantly reduced opacity
        .animation(.easeInOut(duration: 0.3), value: audioLevel)
        .clipShape(Rectangle())  // Ensure wave stays within its container
        .onAppear {
            // Start continuous animation
            withAnimation(
                Animation.linear(duration: 2 / animationSpeed).repeatForever(autoreverses: false)
            ) {
                phase = .pi * 2
            }
        }
    }
}

// MARK: - Add New Affirmation Card View

@available(iOS 17.0, macOS 14.0, *)
private struct AddNewAffirmationCardView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Use current theme card background
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15,
                        x: 0, y: 8)

                VStack(spacing: 16) {
                    // Plus icon
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.8))
                        .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)

                    // Text
                    VStack(spacing: 8) {
                        Text("Add New Affirmation")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)

                        Text("Tap to create a new affirmation")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                            .multilineTextAlignment(.center)
                    }
                }
                .padding()
            }
            .padding(.horizontal)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Add Affirmation Sheet View

@available(iOS 17.0, macOS 14.0, *)
private struct AddAffirmationSheetView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var affirmationText = ""
    @State private var isCreating = false

    let onAffirmationCreated: (any AffirmationProtocol) -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("Create New Affirmation")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                    Text("Enter your personal affirmation below")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .multilineTextAlignment(.center)
                }
                .padding(.top)

                // Text input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Affirmation Text")
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                    TextField("I am confident and capable...", text: $affirmationText, axis: .vertical)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.3))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(themeManager.currentTheme.accentColor.asColor.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                        .lineLimit(3...6)
                        .font(.body)
                }

                Spacer()

                // Create button
                Button(action: createAffirmation) {
                    HStack {
                        if isCreating {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        Text(isCreating ? "Creating..." : "Create Affirmation")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.accentColor.asColor,
                                themeManager.currentTheme.accentColor.asColor.opacity(0.8)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .disabled(affirmationText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isCreating)
                .opacity(affirmationText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)
            }
            .padding()
            .background(themeManager.currentTheme.backgroundColor.asColor)
            .navigationTitle("New Affirmation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                }
            }
        }
    }

    private func createAffirmation() {
        guard !affirmationText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        isCreating = true

        // Create a mock affirmation for now
        // In a real implementation, this would call the affirmation service
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // Simulate network delay

            await MainActor.run {
                // Create a simple affirmation stub
                let newAffirmation = AffirmationStub(
                    text: affirmationText.trimmingCharacters(in: .whitespacesAndNewlines),
                    category: .personal,
                    recordingURL: nil,
                    isFavorite: false,
                    todayProgress: 0.0,
                    cycleProgress: 0.0
                )

                onAffirmationCreated(newAffirmation)
                isCreating = false
            }
        }
    }
}
