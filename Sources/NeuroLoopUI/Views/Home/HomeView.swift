import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import SwiftUI

#if DEBUG
    // import NeuroLoopTestUtilities
#endif
#if os(macOS)
    import AppKit
#else
    import UIKit
#endif

// Helper function for platform-specific background color
@available(iOS 17.0, macOS 14.0, *)
private func platformBackgroundColor() -> Color {
    #if os(macOS)
        return Color(NSColor.windowBackgroundColor)
    #else
        return Color(.systemBackground)
    #endif
}

/// The main view for the home tab, showing active affirmations and progress
@available(iOS 17.0, macOS 14.0, *)
public struct HomeView: View {
    // MARK: - Properties

    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: HomeViewModel
    @State private var showingAddAffirmation = false
    @State private var showingAffirmationDetail = false
    @State private var showingAffirmationPicker = false
    @State private var selectedAffirmation: (any AffirmationProtocol)?
    @State private var isLoading: Bool = true
    private let repository: AffirmationRepositoryProtocol

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
        _viewModel = StateObject(
            wrappedValue: HomeViewModel(
                repository: repository,
                streakService: StreakService(repository: repository)
            )
        )
    }

    public init(repository: AffirmationRepositoryProtocol, viewModel: HomeViewModel) {
        self.repository = repository
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Background gradient
            BackgroundView()

            if isLoading {
                HomeSkeletonView()
            } else {
                HomeContentView(
                    viewModel: viewModel,
                    selectedAffirmation: $selectedAffirmation,
                    showingAffirmationDetail: $showingAffirmationDetail,
                    showingAffirmationPicker: $showingAffirmationPicker
                )
            }
        }
        .navigationTitle(Text("Home").foregroundColor(themeManager.currentTheme.primaryTextColor.asColor))
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            #if os(iOS)
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddAffirmation = true }) {
                        Image(systemName: "plus")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
            #else
                ToolbarItem {
                    Button(action: { showingAddAffirmation = true }) {
                        Image(systemName: "plus")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
            #endif
        }
        .task {
            await viewModel.loadData()
            isLoading = false
        }
        .sheet(isPresented: $showingAddAffirmation) {
            AddAffirmationView(repository: repository)
        }
        .sheet(isPresented: $showingAffirmationDetail) {
            if let affirmation = selectedAffirmation {
                AffirmationDetailView(
                    viewModel: AffirmationDetailViewModel(affirmation: affirmation),
                    affirmation: affirmation
                )
            }
        }
        .sheet(isPresented: $showingAffirmationPicker) {
            SimpleAffirmationPickerView(
                onAffirmationSelected: { affirmationId, affirmationText in
                    Task { @MainActor in
                        await viewModel.startSessionWithAffirmation(
                            id: affirmationId, text: affirmationText)
                    }
                },
                dataSource: RootViewMockRepetitionService.shared
            )
        }
    }

    // MARK: - Background View

    private struct BackgroundView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

        var body: some View {
            // Use current theme background gradient
            (themeManager.currentTheme.backgroundColor.asGradient
                ?? LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.backgroundColor.asColor,
                        themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .ignoresSafeArea()
        }
    }

    // MARK: - Home Content View

    private struct HomeContentView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let viewModel: HomeViewModel
        @Binding var selectedAffirmation: (any AffirmationProtocol)?
        @Binding var showingAffirmationDetail: Bool
        @Binding var showingAffirmationPicker: Bool

        var body: some View {
            ScrollView {
                VStack(spacing: 24) {
                    // Add more space at the top to avoid status bar and back button
                    Spacer()
                        .frame(height: 160)  // Added 160 points of top spacing

                    WelcomeSection()
                    QuickActionsView(
                        viewModel: viewModel,
                        showingAffirmationPicker: $showingAffirmationPicker
                    )

                    if !viewModel.activeAffirmations.isEmpty {
                        ActiveAffirmationsView(
                            affirmations: viewModel.activeAffirmations,
                            onTap: { affirmation in
                                selectedAffirmation = affirmation
                                showingAffirmationDetail = true
                            },
                            onFavorite: { affirmation in
                                Task { @MainActor in
                                    await viewModel.toggleFavorite(affirmation)
                                }
                            }
                        )
                    }
                }
                .padding()
            }
        }
    }

    // MARK: - Welcome Section

    private struct WelcomeSection: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                Text("Welcome back!")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Text("Track your progress and continue your journey")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding()
            .background(CardBackground())
        }
    }

    // MARK: - Quick Actions View

    private struct QuickActionsView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let viewModel: HomeViewModel
        @Binding var showingAffirmationPicker: Bool

        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                Text("Quick Actions")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                HStack(spacing: 16) {
                    // Dynamic session button based on current state
                    if viewModel.canContinueSession {
                        // Continue Session Button
                        Button {
                            Task { @MainActor in
                                await viewModel.continueSession()
                            }
                        } label: {
                            VStack(spacing: 8) {
                                Image(systemName: "play.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)

                                Text("Continue Session")
                                    .font(.caption)
                                    .foregroundColor(.white)

                                Text("\(viewModel.sessionProgress)/100")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                themeManager.currentTheme.successColor.color.asFallbackGradient()
                            )
                            .cornerRadius(12)
                            .shadow(
                                color: themeManager.currentTheme.successColor.color.opacity(0.3),
                                radius: 15,
                                x: 0,
                                y: 8
                            )
                        }
                        .accessibilityLabel("Continue Session")
                        .accessibilityHint(
                            "Continue your current affirmation session with \(viewModel.sessionProgress) repetitions completed"
                        )

                        // Start New Session Button (smaller) - Shows Affirmation Picker
                        Button {
                            showingAffirmationPicker = true
                        } label: {
                            VStack(spacing: 4) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title3)
                                    .foregroundColor(.white)

                                Text("New Session")
                                    .font(.caption2)
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 8)
                            .background(
                                themeManager.currentTheme.accentColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            themeManager.currentTheme.accentColor.asColor,
                                            themeManager.currentTheme.accentColor.asColor.opacity(
                                                0.8),
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                            )
                            .cornerRadius(12)
                            .shadow(
                                color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3),
                                radius: 10,
                                x: 0,
                                y: 5
                            )
                        }
                        .accessibilityLabel("Start New Session")
                        .accessibilityHint("Start a new affirmation session")
                    } else {
                        // Start Session Button (when no active session) - Shows Affirmation Picker
                        Button {
                            showingAffirmationPicker = true
                        } label: {
                            VStack(spacing: 8) {
                                Image(systemName: "play.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)

                                Text("Start Session")
                                    .font(.caption)
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                themeManager.currentTheme.accentColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            themeManager.currentTheme.accentColor.asColor,
                                            themeManager.currentTheme.accentColor.asColor.opacity(
                                                0.8),
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                            )
                            .cornerRadius(12)
                            .shadow(
                                color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3),
                                radius: 15,
                                x: 0,
                                y: 8
                            )
                        }
                        .accessibilityLabel("Start Session")
                        .accessibilityHint("Start a new affirmation session")
                    }

                    QuickActionButton(
                        title: "View Progress",
                        icon: "chart.bar.fill",
                        color: themeManager.currentTheme.accentColor.asColor
                    ) {
                        Task { @MainActor in
                            viewModel.viewProgress()
                        }
                    }
                }
            }
            .padding()
            .background(CardBackground())
        }
    }

    // MARK: - Active Affirmations View

    private struct ActiveAffirmationsView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let affirmations: [any AffirmationProtocol]
        let onTap: (any AffirmationProtocol) -> Void
        let onFavorite: (any AffirmationProtocol) -> Void

        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                Text("Active Affirmations")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                ForEach(affirmations, id: \.id) { affirmation in
                    AffirmationCard(
                        affirmation: affirmation,
                        onTap: { onTap(affirmation) },
                        onFavorite: { onFavorite(affirmation) }
                    )
                }
            }
            .padding()
            .background(CardBackground())
            .cornerRadius(12)
        }
    }

    // MARK: - Card Background

    private struct CardBackground: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

        var body: some View {
            // Use current theme card background
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15,
                    x: 0, y: 8)
        }
    }
}

// MARK: - Home Skeleton View

@available(iOS 17.0, macOS 14.0, *)
private struct HomeSkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 120)
                .redacted(reason: .placeholder)
            ForEach(0..<3) { _ in
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.15))
                    .frame(height: 60)
                    .redacted(reason: .placeholder)
            }
        }
        .padding()
        .accessibilityHidden(true)
    }
}

// MARK: - QuickAction Model
private struct QuickAction: Identifiable {
    let id = UUID()
    let title: String
    let icon: String
    let color: Color
    let handler: () -> Void
}

// MARK: - Welcome Section
private struct WelcomeSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Welcome back!")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text("Track your progress and continue your journey")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(platformBackgroundColor())
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
}

// MARK: - Progress Section
private struct ProgressSection: View {
    let completionRate: Double
    let currentDay: Int

    var body: some View {
        VStack(spacing: 8) {
            CircularProgressView(progress: completionRate)
                .frame(width: 80, height: 80)
                .accessibilityElement(children: .ignore)
                .accessibilityLabel(Text("Progress Circle"))
                .accessibilityValue(Text("\(Int(completionRate * 100)) percent complete"))
                .accessibilityHint(Text("Shows your progress for today."))
            DayCounterView(currentDay: currentDay)
                .accessibilityElement(children: .combine)
                .accessibilityLabel(Text("Day Counter"))
                .accessibilityValue(Text("Day \(currentDay) of 7"))
                .accessibilityHint(Text("Indicates your current day in the affirmation cycle."))
        }
    }
}

// MARK: - Circular Progress View
private struct CircularProgressView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let progress: Double

    var body: some View {
        ZStack {
            Circle()
                .stroke(themeManager.currentTheme.borderColor.color.opacity(0.3), lineWidth: 8)

            Circle()
                .trim(from: 0, to: CGFloat(min(progress, 1.0)))
                .stroke(
                    themeManager.currentTheme.accentColor.asColor,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.linear, value: progress)

            Text("\(Int(progress * 100))%")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
        }
        .frame(width: 80, height: 80)
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("Progress")
        .accessibilityValue("\(Int(progress * 100)) percent complete")
        .accessibilityHint("Shows your current completion rate for today.")
    }
}

// MARK: - Day Counter View
private struct DayCounterView: View {
    let currentDay: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Day \(currentDay)")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text("of 7")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Day Counter")
        .accessibilityValue("Day \(currentDay) of 7")
        .accessibilityHint("Indicates your current day in the affirmation cycle.")
    }
}

// MARK: - Quick Actions Section
private struct QuickActionsSection: View {
    let actions: [QuickAction]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            HStack(spacing: 16) {
                ForEach(actions) { action in
                    QuickActionButton(
                        title: action.title,
                        icon: action.icon,
                        color: action.color,
                        action: action.handler
                    )
                    .accessibilityLabel(Text(action.title))
                    .accessibilityHint(
                        Text(
                            "Quick action: \(action.title). Double tap to \(action.title.lowercased())."
                        ))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(platformBackgroundColor())
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
}

// MARK: - Active Affirmations Section
private struct ActiveAffirmationsSection: View {
    let affirmations: [Affirmation]
    let onTap: (UUID) -> Void
    let onFavorite: (UUID) -> Void
    @EnvironmentObject var themeManager: NeuroLoopCore.ThemeManager

    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                themeManager.currentTheme.cardBackgroundColor.asGradient
                    ?? LinearGradient(
                        gradient: Gradient(colors: [
                            themeManager.currentTheme.cardBackgroundColor.asColor,
                            themeManager.currentTheme.cardBackgroundColor.asColor,
                        ]),
                        startPoint: .top, endPoint: .bottom
                    )
            )
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }

    var body: some View {
        ActiveAffirmationsSectionContent(
            affirmations: affirmations,
            onTap: onTap,
            onFavorite: onFavorite
        )
    }
}

private struct ActiveAffirmationsSectionContent: View {
    let affirmations: [Affirmation]
    let onTap: (UUID) -> Void
    let onFavorite: (UUID) -> Void
    @EnvironmentObject var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Active Affirmations")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            ForEach(affirmations) { affirmation in
                let accessibilityLabelString =
                    "Affirmation: \(affirmation.text). Category: \(affirmation.category.displayName). "
                    + (affirmation.isFavorite ? "Favorite." : "Not favorite.")
                    + (affirmation.recordingURL != nil ? " Audio available." : "")

                AffirmationCard(
                    affirmation: affirmation,
                    onTap: { onTap(affirmation.id) },
                    onFavorite: { onFavorite(affirmation.id) }
                )
                .accessibilityLabel(Text(accessibilityLabelString))
                .accessibilityHint(
                    Text(
                        "Double tap to view details. Swipe right to mark as favorite or play audio if available."
                    ))
            }
        }
        .padding()
        .background(platformBackgroundColor())
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

// MARK: - Statistics Section
private struct StatisticsSection: View {
    let statistics: AffirmationStatistics

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Statistics")
                .font(.headline)

            VStack(alignment: .leading, spacing: 4) {
                Text("Total affirmations: \(statistics.totalAffirmations)")
                Text("Active: \(statistics.activeAffirmations)")
                Text("Favorites: \(statistics.favoriteAffirmations)")
                Text("Completed cycles: \(statistics.completedCycles)")
                Text("Longest current streak: \(statistics.longestCurrentStreak)")
            }
            .font(.subheadline)
        }
        .padding()
        .background(platformBackgroundColor())
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

// MARK: - Statistic Card
private struct StatisticCard: View {
    let title: String
    let value: String
    let subtitle: String
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            Text(subtitle)
                .font(.caption2)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.1), radius: 8,
                    x: 0, y: 4)
        )
    }
}

// MARK: - Performance Monitoring
private func logPerformanceMetrics() {
    Task { @MainActor in
        let usage = await PerformanceMonitor.shared.currentMemoryUsage()
        print("[Performance] Memory usage: \(usage / 1024 / 1024) MB")
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct HomeView_Previews: PreviewProvider {
        static var previews: some View {
            let repository = HomeViewMockRepository()
            let _ = MockStreakService()

            return NavigationStack {
                HomeView(repository: repository)
                    .environmentObject(NavigationCoordinator())
            }
        }
    }

    @available(iOS 17.0, macOS 14.0, *)
    private class HomeViewMockRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
        func fetchAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? { nil }
        func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
            async throws -> any AffirmationProtocol
        {
            await AffirmationStub(
                text: text,
                category: category,
                recordingURL: recordingURL,
                isFavorite: false,
                todayProgress: 0.0,
                cycleProgress: 0.0)
        }
        func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func deleteAffirmation(id: UUID) async throws {}
        func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any AffirmationProtocol]
        { [] }
        func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? { nil }
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws {}
        func startCycle(for affirmation: any AffirmationProtocol) async throws {}

        // Missing protocol methods
        func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func updateProgress(for affirmationId: UUID, repetitions: Int, date: Date) async throws {}
        func getStatistics(for affirmationId: UUID) async throws -> AffirmationStatistics { AffirmationStatistics() }
        func searchAffirmations(query: String) async throws -> [any AffirmationProtocol] { [] }
        func getAffirmations(sortedBy option: AffirmationSortOption) async throws -> [any AffirmationProtocol] { [] }
        func updateFavoriteStatus(for affirmationId: UUID, isFavorite: Bool) async throws {}
        func getDailyProgress(for affirmationId: UUID, date: Date) async throws -> Int { 0 }
        func updateEnergyLevel(for affirmationId: UUID, level: Double) async throws {}
        func recordMood(for affirmationId: UUID, rating: Int, notes: String?) async throws {}
        func getAffirmationsWithActiveCycles() async throws -> [any AffirmationProtocol] { [] }
        func completeCycle(for affirmationId: UUID) async throws {}
        func startNewCycle(for affirmationId: UUID) async throws {}
        func getStreakInfo(for affirmationId: UUID) async throws -> (currentStreak: Int, longestStreak: Int) { (0, 0) }
        func updateLongestStreak(for affirmationId: UUID, streak: Int) async throws {}
    }
#endif

// MARK: - Simple Affirmation Picker (Temporary)

@available(iOS 17.0, macOS 14.0, *)
private struct SimpleAffirmationPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var availableAffirmations: [(UUID, String, Int)] = []
    @State private var showingAddAffirmation = false

    let onAffirmationSelected: (UUID, String) -> Void
    let dataSource: AffirmationPickerDataSource

    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient matching the current theme
                (themeManager.currentTheme.backgroundColor.asGradient
                    ?? LinearGradient(
                        gradient: Gradient(colors: [
                            themeManager.currentTheme.backgroundColor.asColor,
                            themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    ))
                    .ignoresSafeArea()

                VStack(spacing: 20) {
                    // Header section with app-style card
                    VStack(spacing: 12) {
                        Image(systemName: "quote.bubble.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)

                        Text("Select Your Affirmation")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Choose which affirmation you'd like to practice")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                themeManager.currentTheme.cardBackgroundColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            themeManager.currentTheme.cardBackgroundColor.asColor,
                                            themeManager.currentTheme.cardBackgroundColor.asColor
                                                .opacity(0.8),
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                            )
                            .shadow(
                                color: themeManager.currentTheme.shadowColor.color.opacity(0.3),
                                radius: 15, x: 0, y: 8)
                    )

                    ScrollView {
                        VStack(spacing: 16) {
                            // Default affirmation
                            affirmationCard(
                                id: UUID(uuidString: "12345678-1234-1234-1234-123456789ABC")
                                    ?? UUID(),
                                text: "I am confident and capable in everything I do",
                                progress: getDefaultAffirmationProgress()
                            )

                            // Available affirmations with swipe-to-delete
                            ForEach(availableAffirmations, id: \.0) { affirmation in
                                affirmationCard(
                                    id: affirmation.0,
                                    text: affirmation.1,
                                    progress: affirmation.2
                                )
                                .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                                    Button(role: .destructive) {
                                        deleteAffirmation(id: affirmation.0)
                                    } label: {
                                        Label("Delete", systemImage: "trash")
                                    }
                                }
                            }

                            // Add New Affirmation Card
                            addNewAffirmationCard
                        }
                        .padding(.horizontal)
                    }

                }
                .padding()
            }
            .navigationTitle("Choose Affirmation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                }
            }
        }
        .onAppear {
            loadAvailableAffirmations()
        }
        .sheet(isPresented: $showingAddAffirmation) {
            NavigationView {
                AddAffirmationView(repository: MockAffirmationRepository())
            }
        }
        .onChange(of: showingAddAffirmation) { isShowing in
            // Refresh the affirmations list when the modal is dismissed
            if !isShowing {
                loadAvailableAffirmations()
            }
        }
    }

    private func affirmationCard(id: UUID, text: String, progress: Int) -> some View {
        Button(action: {
            // Tap to select and immediately start session
            onAffirmationSelected(id, text)
            dismiss()
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(text)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .lineLimit(3)

                    Text("Day \(min((progress / 100) + 1, 7)) of 7 • \(progress % 100)/100 today")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                VStack(spacing: 4) {
                    Text("\(progress)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("reps")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        // Use current theme gradient for all cards
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor.opacity(
                                        0.8),
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15,
                        x: 0, y: 8)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func loadAvailableAffirmations() {
        let allAffirmations = dataSource.getAllAffirmationsWithProgress()

        // Filter out the default affirmation to prevent duplication
        let defaultAffirmationId =
            UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") ?? UUID()
        availableAffirmations = allAffirmations.filter { $0.0 != defaultAffirmationId }

        print("📋 Loaded \(availableAffirmations.count) custom affirmations (excluding default)")
    }

    private func deleteAffirmation(id: UUID) {
        // Don't allow deleting the default affirmation
        if id == UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") {
            return
        }

        // Remove from data source
        if let dataSource = dataSource as? RootViewMockRepetitionService {
            Task { @MainActor in
                dataSource.removeAffirmation(id: id)
            }
        }

        // Remove from local array
        availableAffirmations.removeAll { $0.0 == id }
    }

    private func getAffirmationText(for id: UUID) -> String {
        if id == UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") {
            return "I am confident and capable in everything I do"
        }

        return availableAffirmations.first(where: { $0.0 == id })?.1 ?? "Unknown affirmation"
    }

    // MARK: - Helper Functions

    private func getDefaultAffirmationProgress() -> Int {
        // Create a mock affirmation to get progress
        let mockAffirmation = MockAffirmationForProgress()
        let progressInfo = RootViewMockRepetitionService.shared.getProgress(for: mockAffirmation)
        return progressInfo.currentRepetitions
    }

    // Simple mock affirmation for getting progress
    private class MockAffirmationForProgress: AffirmationProtocol, @unchecked Sendable {
        let id = UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") ?? UUID()
        let text = "I am confident and capable in everything I do"
        let category: AffirmationCategory = .confidence
        let recordingURL: URL? = nil
        let createdAt = Date()
        let updatedAt = Date()
        let currentCycleDay = 1
        let cycleStartDate: Date? = Date()
        let completedCycles = 0
        let currentRepetitions = 0
        let dailyProgress: [Date: Int] = [:]
        let lastRepetitionDate: Date? = nil
        let energyLevel = 0.5
        let moodRating: Int? = nil
        let notes: String? = nil
        let isFavorite = false
        let playCount = 0
        let hasActiveCycle = true
        let isCurrentCycleComplete = false
        let todayProgress = 0.0
        let cycleProgress = 0.0
        let hasTodayQuotaMet = false
        let hasRecording = false
        let canPerformRepetition = true
        var longestStreak: Int = 0

        func recordRepetition() throws {}
        func updateEnergyLevel(_ level: Double) {}
        func recordMood(_ rating: Int, notes: String?) {}

        static func == (lhs: MockAffirmationForProgress, rhs: MockAffirmationForProgress) -> Bool {
            return lhs.id == rhs.id
        }
    }

    // MARK: - Add New Affirmation Card

    private var addNewAffirmationCard: some View {
        Button(action: {
            showingAddAffirmation = true
        }) {
            VStack(spacing: 12) {
                // Plus icon
                Image(systemName: "plus.circle.fill")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.white)

                // Title
                Text("Add New Affirmation")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                // Subtitle with options
                HStack(spacing: 16) {
                    HStack(spacing: 6) {
                        Image(systemName: "mic.fill")
                            .font(.system(size: 14))
                        Text("Speak it")
                            .font(.system(size: 14, weight: .medium))
                    }

                    Text("|")
                        .font(.system(size: 14))
                        .opacity(0.7)

                    HStack(spacing: 6) {
                        Image(systemName: "pencil")
                            .font(.system(size: 14))
                        Text("Type it")
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(.white.opacity(0.9))
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 24)
            .padding(.horizontal, 20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        (themeManager.currentTheme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.accentColor.asColor.opacity(0.7),
                                    themeManager.currentTheme.accentColor.asColor.opacity(0.5),
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .shadow(
                color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15, x: 0,
                y: 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
