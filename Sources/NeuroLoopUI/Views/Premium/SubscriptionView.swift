import NeuroLoopCore
import StoreKit
import SwiftUI

// import PremiumFeedbackView

public struct SubscriptionView: View {
    @ObservedObject private var viewModel: SubscriptionViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init(viewModel: SubscriptionViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                mainContent
                    .frame(maxHeight: geometry.size.height)
            }
        }
        .background(themeManager.currentTheme.backgroundColor.asColor)
        .overlay(overlayContent)
        .onAppear {
            Task {
                await viewModel.loadProducts()
            }
        }
    }

    private var mainContent: some View {
        VStack(spacing: 16) {
            // Header
            VStack(spacing: 6) {
                Text("Upgrade to Premium")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Text("Unlock all features and take your practice to the next level")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 12)

            // Features - Compact layout
            VStack(alignment: .leading, spacing: 8) {
                HStack(spacing: 16) {
                    CompactFeatureRow(icon: "infinity", title: "Unlimited Affirmations")
                    CompactFeatureRow(icon: "paintpalette", title: "Custom Themes")
                }
                HStack(spacing: 16) {
                    CompactFeatureRow(icon: "chart.bar", title: "Advanced Analytics")
                    CompactFeatureRow(icon: "square.and.arrow.up", title: "Data Export")
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                        radius: 8, x: 0, y: 4)
            )

            // Subscription Tiers
            VStack(spacing: 16) {
                Text("Choose Your Plan")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                    .padding(.bottom, 4)

                ForEach(viewModel.products) { product in
                    EnhancedSubscriptionOptionView(
                        product: product,
                        isSelected: viewModel.selectedProductId == product.id,
                        action: { viewModel.selectProduct(product) }
                    )
                }

                // If no products loaded, show mock tiers
                if viewModel.products.isEmpty {
                    MockSubscriptionTiers()
                }
            }
            .padding()

            // Purchase Button
            Button(action: {
                Task {
                    await viewModel.purchase()
                }
            }) {
                Text(viewModel.isPurchasing ? "Processing..." : "Subscribe Now")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(viewModel.isPurchasing ? themeManager.currentTheme.disabledColor.asColor : themeManager.currentTheme.accentColor.asColor)
                    )
            }
            .disabled(viewModel.isPurchasing || viewModel.selectedProductId == nil)
            .padding()
            .accessibilityLabel("Subscribe to Premium")
            .accessibilityIdentifier("SubscribeButton")

            // Restore Purchases
            Button(action: {
                Task {
                    await viewModel.restorePurchases()
                }
            }) {
                Text("Restore Purchases")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            }
            .padding(.bottom)
            .accessibilityLabel("Restore Purchases")
            .accessibilityIdentifier("RestorePurchasesButton")

            // Terms and Privacy
            VStack(spacing: 5) {
                Text("By subscribing, you agree to our")
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

                HStack(spacing: 5) {
                    Link(
                        "Terms of Service",
                        destination: URL(string: "https://example.com/terms")!)
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    Text("and")
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    Link(
                        "Privacy Policy",
                        destination: URL(string: "https://example.com/privacy")!)
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                }
                .font(.caption)
            }
        }
        .padding()
    }

    private var overlayContent: some View {
        Group {
            if viewModel.isLoading {
                LoadingView(message: "Loading...")
            }

            if let error = viewModel.error {
                PremiumFeedbackView(
                    type: .error(message: error.localizedDescription),
                    dismissAction: { viewModel.dismissError() }
                )
            }
            if viewModel.purchaseSuccess {
                PremiumFeedbackView(
                    type: .success(
                        message: "Your purchase was successful! Enjoy premium features."),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
            if viewModel.restoreSuccess {
                PremiumFeedbackView(
                    type: .success(message: "Your purchases have been restored!"),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
            if viewModel.didBecomePremium {
                PremiumFeedbackView(
                    type: .success(message: "Congratulations! You are now a Premium user. 🎉"),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
        }
    }
}

private struct FeatureRow: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
            }
        }
    }
}

private struct SubscriptionOptionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let product: Product
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.displayName)
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                    Text(product.description)
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                }

                Spacer()

                Text(product.displayPrice)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.disabledColor.asColor)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.borderColor.asColor, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Subscription option: \(product.displayName), \(product.displayPrice)")
        .accessibilityIdentifier("SubscriptionOption_\(product.id)")
    }
}

// MARK: - Preview

// MARK: - Enhanced Subscription Option View

private struct EnhancedSubscriptionOptionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let product: Product
    let isSelected: Bool
    let action: () -> Void

    private var isYearly: Bool {
        product.id.contains("yearly")
    }

    private var savingsText: String? {
        if isYearly {
            return "Save 33%"
        }
        return nil
    }

    private var backgroundFill: AnyShapeStyle {
        if isSelected {
            if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                return AnyShapeStyle(gradient)
            } else {
                return AnyShapeStyle(themeManager.currentTheme.cardBackgroundColor.asColor)
            }
        } else {
            return AnyShapeStyle(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.5))
        }
    }

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(product.displayName)
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                            if let savings = savingsText {
                                Text(savings)
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(themeManager.currentTheme.successColor.asColor)
                                    )
                            }
                        }

                        Text(product.description)
                            .font(.subheadline)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Text(product.displayPrice)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)

                        Text(isYearly ? "per year" : "per month")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    }

                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.disabledColor.asColor)
                }

                // Features list for yearly plan
                if isYearly {
                    VStack(alignment: .leading, spacing: 4) {
                        FeatureRow(icon: "crown.fill", title: "All Premium Features", description: "")
                        FeatureRow(icon: "percent", title: "33% Savings", description: "")
                        FeatureRow(icon: "star.fill", title: "Exclusive Themes", description: "")
                    }
                    .padding(.top, 8)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundFill)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.borderColor.asColor,
                                lineWidth: isSelected ? 3 : 1
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.asColor.opacity(isSelected ? 0.3 : 0.1),
                        radius: isSelected ? 12 : 6,
                        x: 0,
                        y: isSelected ? 6 : 3
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Subscription option: \(product.displayName), \(product.displayPrice)")
        .accessibilityIdentifier("SubscriptionOption_\(product.id)")
    }
}

// MARK: - Mock Subscription Tiers

private struct MockSubscriptionTiers: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(spacing: 16) {
            MockTierView(
                title: "Monthly Premium",
                price: "$4.99",
                period: "per month",
                isSelected: true,
                isYearly: false
            )

            MockTierView(
                title: "Yearly Premium",
                price: "$39.99",
                period: "per year",
                savings: "Save 33%",
                isSelected: false,
                isYearly: true
            )
        }
    }
}

private struct MockTierView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let title: String
    let price: String
    let period: String
    let savings: String?
    let isSelected: Bool
    let isYearly: Bool

    init(title: String, price: String, period: String, savings: String? = nil, isSelected: Bool, isYearly: Bool) {
        self.title = title
        self.price = price
        self.period = period
        self.savings = savings
        self.isSelected = isSelected
        self.isYearly = isYearly
    }

    private var mockBackgroundFill: AnyShapeStyle {
        if isSelected {
            if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                return AnyShapeStyle(gradient)
            } else {
                return AnyShapeStyle(themeManager.currentTheme.cardBackgroundColor.asColor)
            }
        } else {
            return AnyShapeStyle(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.5))
        }
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(title)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                        if let savings = savings {
                            Text(savings)
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(themeManager.currentTheme.successColor.asColor)
                                )
                        }
                    }

                    Text("Full access to all premium features")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text(price)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)

                    Text(period)
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                }

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.disabledColor.asColor)
            }

            // Features list for yearly plan
            if isYearly {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                        Text("All Premium Features")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                        Spacer()
                    }
                    HStack {
                        Image(systemName: "percent")
                            .foregroundColor(themeManager.currentTheme.successColor.asColor)
                        Text("33% Savings vs Monthly")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                        Spacer()
                    }
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                        Text("Exclusive Premium Themes")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                        Spacer()
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(mockBackgroundFill)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isSelected ? themeManager.currentTheme.accentColor.asColor : themeManager.currentTheme.borderColor.asColor,
                            lineWidth: isSelected ? 3 : 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(isSelected ? 0.3 : 0.1),
                    radius: isSelected ? 12 : 6,
                    x: 0,
                    y: isSelected ? 6 : 3
                )
        )
    }
}

struct SubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        SubscriptionView(
            viewModel: SubscriptionViewModel(
                premiumService: MockPremiumService()
            )
        )
        .environmentObject(NeuroLoopCore.ThemeManager.shared)
    }
}

// MARK: - Compact Feature Row

private struct CompactFeatureRow: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let icon: String
    let title: String

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                .frame(width: 16, height: 16)

            Text(title)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}
