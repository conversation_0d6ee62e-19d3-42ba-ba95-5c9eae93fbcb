import NeuroLoopTypes
import NeuroLoopCore
import SwiftUI

#if os(iOS)
    import UIKit
#endif

@available(iOS 17.0, macOS 14.0, *)
public struct StreakVisualizationView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: StreakVisualizationViewModel

    public init(viewModel: StreakVisualizationViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    public var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Streak Summary
                streakSummarySection

                // Milestones
                milestoneSection

                // Achievements
                achievementSection

                // Heat Map
                streakHeatMap

                // Monthly Calendar
                MonthlyCalendarView(
                    streakDays: viewModel.streakDays,
                    selectedMonth: $viewModel.selectedMonth
                )

                // Streak Timeline
                StreakTimelineView(
                    streakDays: viewModel.streakDays,
                    onDayTap: viewModel.handleDayTap
                )
            }
            .padding()
        }
        .onAppear {
            viewModel.loadStreakData()
        }
    }

    private var streakSummarySection: some View {
        VStack(spacing: 10) {
            Text("Current Streak: \(viewModel.currentStreak) days")
                .font(.headline)
            Text("Longest Streak: \(viewModel.longestStreak) days")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(10)
        .shadow(radius: 2)
    }

    private var milestoneSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Milestones")
                .font(.headline)

            ForEach(viewModel.milestones) { milestone in
                HStack {
                    Image(systemName: milestone.isAchieved ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(milestone.isAchieved ? .green : .gray)

                    VStack(alignment: .leading) {
                        Text(milestoneTitle(for: milestone))
                            .font(.subheadline)
                        Text("\(milestone.value) days")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(10)
        .shadow(radius: 2)
    }

    private var achievementSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Achievements")
                .font(.headline)

            ForEach(viewModel.achievements) { achievement in
                HStack {
                    Image(systemName: achievement.icon)
                        .foregroundColor(achievement.isUnlocked ? .yellow : .gray)

                    VStack(alignment: .leading) {
                        Text(achievement.title)
                            .font(.subheadline)
                        Text(achievement.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(10)
        .shadow(radius: 2)
    }

    private var streakHeatMap: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("30-Day Activity")
                .font(.headline)

            LazyVGrid(
                columns: Array(repeating: GridItem(.flexible(), spacing: 4), count: 7), spacing: 4
            ) {
                ForEach(viewModel.heatMapData) { day in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(heatMapColor(for: day))
                        .frame(height: 20)
                        .overlay(
                            Text(day.date.formatted(.dateTime.day()))
                                .font(.caption2)
                                .foregroundColor(.white)
                        )
                }
            }
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(10)
        .shadow(radius: 2)
    }

    private func milestoneTitle(for milestone: StreakMilestone) -> String {
        switch milestone.type {
        case .currentStreak:
            return "Current Streak"
        case .longestStreak:
            return "Longest Streak"
        case .completedCycles:
            return "Completed Cycles"
        case .upcomingStreak:
            return "\(milestone.value) Day Streak"
        }
    }

    private func heatMapColor(for day: StreakDay) -> Color {
        if day.isComplete {
            return themeManager.currentTheme.successColor.color
        } else if day.progress > 0 {
            return themeManager.currentTheme.warningColor.color
        } else {
            return themeManager.currentTheme.disabledColor.color.opacity(0.3)
        }
    }
}

// MARK: - Preview
@available(iOS 17.0, macOS 14.0, *)
struct StreakVisualizationView_Previews: PreviewProvider {
    static var previews: some View {
        StreakVisualizationView(viewModel: .preview)
    }
}
