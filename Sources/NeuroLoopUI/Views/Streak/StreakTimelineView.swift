import NeuroLoopTypes
import Swift<PERSON>

#if os(iOS)
    import UIKit
#endif

@available(iOS 17.0, macOS 14.0, *)
public struct StreakTimelineView: View {
    let streakDays: [StreakDay]
    let onDayTap: (StreakDay) -> Void

    public init(streakDays: [StreakDay], onDayTap: @escaping (StreakDay) -> Void) {
        self.streakDays = streakDays
        self.onDayTap = onDayTap
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Daily Progress")
                .font(.headline)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(streakDays) { day in
                        StreakDayView(day: day)
                            .onTapGesture {
                                onDayTap(day)
                            }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(10)
        .shadow(radius: 2)
    }
}

private struct StreakDayView: View {
    let day: StreakDay

    var body: some View {
        VStack(spacing: 4) {
            Text(day.date.formatted(.dateTime.day()))
                .font(.caption)
                .foregroundColor(.secondary)

            Circle()
                .fill(backgroundColor)
                .frame(width: 32, height: 32)
                .overlay(
                    Text("\(day.repetitions)")
                        .font(.caption2)
                        .foregroundColor(textColor)
                )
        }
    }

    private var backgroundColor: Color {
        if day.isComplete {
            return .green
        } else if day.progress > 0 {
            return .yellow.opacity(day.progress)
        }
        return .gray.opacity(0.1)
    }

    private var textColor: Color {
        if day.isComplete {
            return .white
        }
        return .primary
    }
}

// MARK: - Preview
@available(iOS 17.0, macOS 14.0, *)
struct StreakTimelineView_Previews: PreviewProvider {
    static var previews: some View {
        StreakTimelineView(
            streakDays: [
                StreakDay(
                    date: Date(),
                    progress: 1.0,
                    isComplete: true,
                    repetitions: 10,
                    isInCurrentMonth: true
                )
            ],
            onDayTap: { _ in }
        )
    }
}
