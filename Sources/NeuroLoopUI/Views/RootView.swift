import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

/// The root view of the app containing the main TabView with optimized async initialization
public struct RootView: View {
    // MARK: - Properties

    @StateObject private var navigationCoordinator = NavigationCoordinator()
    @StateObject private var asyncServiceFactory = AsyncServiceFactory.shared
    @StateObject private var speechManager = SpeechRecognitionManager.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var selectedTab: RootTab = .home
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding: Bool = true
    @State private var showOnboarding: Bool = false
    @State private var selectedAffirmation: (any AffirmationProtocol)?
    @State private var affirmations: [any AffirmationProtocol] = []
    @State private var showingAffirmationDetail = false
    @State private var isInitialized = false

    // MARK: - Initializer

    /// Public initializer to allow instantiation from other modules
    public init() {}

    // MARK: - Body

    public var body: some View {
        ZStack {
            // App-wide background gradient
            BackgroundGradientView()
                .ignoresSafeArea()

            if !isInitialized {
                // Show loading screen during service initialization
                ServiceInitializationView {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        isInitialized = true
                    }
                }
                .transition(.opacity)
            } else {
                // Main app content
                mainAppContent
                    .transition(.opacity)
            }

            if showOnboarding {
                OnboardingView(isPresented: $showOnboarding)
                    .transition(.opacity)
                    .accessibilityAddTraits(.isModal)
                    .accessibilityLabel("Onboarding")
                    .accessibilityHint("Guides you through the app's main features")
                    .onDisappear {
                        hasCompletedOnboarding = true
                    }
            }
        }
        .onAppear {
            showOnboarding = !hasCompletedOnboarding
            startAsyncInitialization()
        }
    }

    // MARK: - Main App Content

    private var mainAppContent: some View {
        TabView(selection: $selectedTab) {
                HomeTabView()
                    .tabItem {
                        Label(RootTab.home.title, systemImage: RootTab.home.icon)
                            .accessibilityLabel("Home Tab")
                            .accessibilityHint(
                                "Shows your daily affirmations and progress overview")
                    }
                    .tag(RootTab.home)

                JournalTabView()
                    .tabItem {
                        Label(RootTab.journal.title, systemImage: RootTab.journal.icon)
                            .accessibilityLabel("Journal Tab")
                            .accessibilityHint("Track your daily mood and thoughts")
                    }
                    .tag(RootTab.journal)

                AnalyticsTabView()
                    .tabItem {
                        Label(RootTab.analytics.title, systemImage: RootTab.analytics.icon)
                            .accessibilityLabel("Analytics Tab")
                            .accessibilityHint("View your progress charts and statistics")
                    }
                    .tag(RootTab.analytics)

                SettingsTabView()
                    .tabItem {
                        Label(RootTab.settings.title, systemImage: RootTab.settings.icon)
                            .accessibilityLabel("Settings Tab")
                            .accessibilityHint("Access app settings and premium features")
                    }
                    .tag(RootTab.settings)
            }
            .tint(themeManager.currentTheme.accentColor.asColor)
            .environmentObject(navigationCoordinator)
    }

    // MARK: - Async Initialization

    private func startAsyncInitialization() {
        Task {
            // Start background initialization of services and speech recognition
            async let serviceInit: Void = asyncServiceFactory.initializeCriticalServices()
            async let speechInit: Void = speechManager.preInitialize()

            // Wait for both to complete
            _ = await (serviceInit, speechInit)

            print("[RootView] Async initialization completed successfully")
        }
    }

    // MARK: - Background Gradient View

    private struct BackgroundGradientView: View {
        @EnvironmentObject private var themeManager: ThemeManager

        var body: some View {
            // Use current theme background
            (themeManager.currentTheme.backgroundColor.asGradient
                ?? LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.backgroundColor.asColor,
                        themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .ignoresSafeArea()
        }
    }

    private func handleAffirmationSelection(_ affirmation: any AffirmationProtocol) {
        selectedAffirmation = affirmation
        showingAffirmationDetail = true
    }

    private func handleAffirmationDeletion(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationEdit(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationShare(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationFavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationArchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationLock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationHide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationSync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationComplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUncomplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnfavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnarchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnlock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnhide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnsync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }
}

// MARK: - Tab Views

/// The home tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct HomeTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedAffirmationId: UUID?
    @StateObject private var viewModel = HomeViewModel(
        repository: MockAffirmationRepository(),
        streakService: StreakService(repository: MockAffirmationRepository())
    )

    var body: some View {
        NavigationStack(path: $navigationCoordinator.homePath) {
            // Go directly to choose affirmation page on app load to reduce friction and improve user retention
            AffirmationPickerView(
                dataSource: RootViewMockRepetitionService.shared,
                onAffirmationSelected: { affirmationId, affirmationText in
                    // Navigate directly to speak affirmation
                    navigationCoordinator.navigate(
                        to: .speakAffirmation(affirmationId), in: .home)
                }
            )
            .environmentObject(ThemeManager.shared)
            .onAppear {
                // Initialize default affirmation if not already present
                Task { @MainActor in
                    let defaultId =
                        UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") ?? UUID()
                    let defaultText = "I am confident and capable in everything I do"

                    // Check if default affirmation is already added
                    let existingAffirmations = RootViewMockRepetitionService.shared
                        .getAllAffirmationsWithProgress()
                    let hasDefault = existingAffirmations.contains { $0.0 == defaultId }

                    if !hasDefault {
                        // Add default affirmation only if it doesn't exist
                        RootViewMockRepetitionService.shared.addAffirmation(
                            id: defaultId, text: defaultText)
                        print("📝 Added default affirmation")
                    } else {
                        print("📝 Default affirmation already exists")
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: .navigateToSpeakAffirmation)) {
                notification in
                if let userInfo = notification.userInfo,
                    let affirmationId = userInfo["affirmationId"] as? UUID
                {
                    selectedAffirmationId = affirmationId
                    navigationCoordinator.navigate(
                        to: .speakAffirmation(affirmationId), in: .home)
                }
            }
            .navigationDestination(for: NavigationRoute.self) { route in
                switch route {
                case .speakAffirmation(let affirmationId):
                    if #available(iOS 17.0, macOS 14.0, *) {
                        // Create the correct affirmation for the selected ID
                        let selectedAffirmation = createAffirmationForId(affirmationId)

                        // Get all available affirmations for the carousel
                        let allAffirmationsData = RootViewMockRepetitionService.shared.getAllAffirmationsWithProgress()
                        let allAffirmations = allAffirmationsData.map { (id, text, repetitions) in
                            MockAffirmationObject(id: id, text: text)
                        }

                        // Use the SpeakAffirmationView with carousel functionality
                        SpeakAffirmationView(
                            affirmation: selectedAffirmation,
                            repetitionService: RootViewMockRepetitionService.shared,
                            affirmationService: MockAffirmationService(),
                            streakService: MockStreakService(),
                            affirmations: allAffirmations,
                            onAffirmationCreated: { newAffirmation in
                                // Handle new affirmation creation
                                Task { @MainActor in
                                    RootViewMockRepetitionService.shared.addAffirmation(
                                        id: newAffirmation.id,
                                        text: newAffirmation.text
                                    )
                                    print("🎯 Added new affirmation to carousel: \(newAffirmation.text)")
                                }
                            }
                        )
                        .environmentObject(ThemeManager.shared)
                    } else {
                        Text("This feature requires iOS 17.0 or later")
                    }
                default:
                    Text("Route not implemented yet")
                }
            }
        }
    }

    // Helper function to create a mock affirmation
    private func createMockAffirmation() -> any AffirmationProtocol {
        // Create a simple mock affirmation without async calls
        return MockAffirmationObject()
    }

    // Helper function to create the correct affirmation for a given ID
    private func createAffirmationForId(_ affirmationId: UUID) -> any AffirmationProtocol {
        // Check if it's the default affirmation
        if affirmationId == UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") {
            return MockAffirmationObject()
        }

        // Get the affirmation text from the data source
        let dataSource = RootViewMockRepetitionService.shared
        let allAffirmations = dataSource.getAllAffirmationsWithProgress()

        if let affirmationData = allAffirmations.first(where: { $0.0 == affirmationId }) {
            // Create a mock affirmation with the correct ID and text
            return MockAffirmationObject(id: affirmationId, text: affirmationData.1)
        }

        // Fallback to default if not found
        print("⚠️ Affirmation with ID \(affirmationId) not found, using default")
        return MockAffirmationObject()
    }

    // Simple mock implementation of AffirmationProtocol for UI testing
    private class MockAffirmationObject: AffirmationProtocol, @unchecked Sendable {
        let id: UUID
        let text: String

        // Default constructor for the default affirmation
        init() {
            self.id = UUID(uuidString: "12345678-1234-1234-1234-123456789ABC") ?? UUID()
            self.text = "I am confident and capable in everything I do"
        }

        // Custom constructor for selected affirmations
        init(id: UUID, text: String) {
            self.id = id
            self.text = text
        }
        let category: AffirmationCategory = .confidence
        let recordingURL: URL? = nil
        let createdAt = Date()
        let updatedAt = Date()
        let currentCycleDay = 1
        let cycleStartDate: Date? = Date()
        let completedCycles = 0
        let currentRepetitions = 0
        let dailyProgress: [Date: Int] = [:]
        let lastRepetitionDate: Date? = nil
        let energyLevel = 0.5
        let moodRating: Int? = nil
        let notes: String? = nil
        let isFavorite = false
        let playCount = 0
        let hasActiveCycle = true
        let isCurrentCycleComplete = false
        let todayProgress = 0.0
        let cycleProgress = 0.0
        let hasTodayQuotaMet = false
        let hasRecording = false
        let canPerformRepetition = true

        // Add longestStreak property for AffirmationProtocol conformance
        var longestStreak: Int = 0

        // Required methods from AffirmationProtocol
        func recordRepetition() throws {
            // Mock implementation - does nothing
        }

        func updateEnergyLevel(_ level: Double) {
            // Mock implementation - does nothing
        }

        func recordMood(_ rating: Int, notes: String?) {
            // Mock implementation - does nothing
        }

        static func == (lhs: MockAffirmationObject, rhs: MockAffirmationObject) -> Bool {
            return lhs.id == rhs.id
        }
    }

}

/// The journal tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct JournalTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.journalPath) {
            // Try to activate the journal directly
            JournalViewContainer()
        }
    }
}

/// Container to handle journal view instantiation
@available(iOS 17.0, macOS 14.0, *)
private struct JournalViewContainer: View {
    var body: some View {
        // Use ServiceFactory to get the appropriate journal repository
        let repository = NeuroLoopCore.ServiceFactory.shared.getJournalRepository()
        JournalView(repository: repository)
    }
}

/// Placeholder view for journal functionality
@available(iOS 17.0, macOS 14.0, *)
private struct JournalPlaceholderView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "book.pages.fill")
                .font(.system(size: 60))
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)

            Text("Journal")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            Text("Track your daily mood and thoughts")
                .font(.subheadline)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                .multilineTextAlignment(.center)

            Text("✅ Feature Complete - IDE Indexing Issue")
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.successColor.asColor)
                .padding(.top)

            Text("Build succeeds, app runs with full journal functionality")
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

/// The analytics tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct AnalyticsTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.analyticsPath) {
            AnalyticsViewContainer()
        }
    }
}

/// Container to handle analytics view instantiation
@available(iOS 17.0, macOS 14.0, *)
private struct AnalyticsViewContainer: View {
    var body: some View {
        // Use mock services for UI-only builds
        let affirmationService = MockAffirmationService()
        let journalRepository: JournalRepositoryProtocol? = nil  // Use nil for now, will be enhanced later

        ProgressDashboardView(
            viewModel: ProgressDashboardViewModel(
                affirmationService: affirmationService,
                journalRepository: journalRepository
            )
        )
    }
}

/// The settings tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct SettingsTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.settingsPath) {
            let mockViewModel = SettingsViewModel(
                userDefaults: .standard,
                purchaseManager: MockPurchaseManager(),
                dataExportService: MockDataExportService(),
                themeManager: ThemeManager.shared,
                hapticManager: MockHapticManager(),
                syncService: MockSyncService()
            )
            SettingsView(viewModel: mockViewModel)
        }
    }
}

// MARK: - Tab Enum

public enum RootTab: String {
    case home, journal, analytics, settings

    public var title: String {
        switch self {
        case .home: return "Home"
        case .journal: return "Journal"
        case .analytics: return "Analytics"
        case .settings: return "Settings"
        }
    }

    public var icon: String {
        switch self {
        case .home: return "house.fill"
        case .journal: return "book.pages.fill"
        case .analytics: return "chart.bar.fill"
        case .settings: return "gear"
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct RootView_Previews: PreviewProvider {
        static var previews: some View {
            RootView()
        }
    }
#endif

// MARK: - Mock Services

// Mock affirmation that can have an updated repetition count
public class RootViewMockAffirmationWithCount: AffirmationProtocol, @unchecked Sendable {
    private let originalAffirmation: any AffirmationProtocol
    private let _currentRepetitions: Int

    nonisolated public init(originalAffirmation: any AffirmationProtocol, newRepetitionCount: Int) {
        self.originalAffirmation = originalAffirmation
        self._currentRepetitions = newRepetitionCount
    }

    public var id: UUID { originalAffirmation.id }
    public var text: String { originalAffirmation.text }
    public var category: AffirmationCategory { originalAffirmation.category }
    public var recordingURL: URL? { originalAffirmation.recordingURL }
    public var createdAt: Date { originalAffirmation.createdAt }
    public var updatedAt: Date { originalAffirmation.updatedAt }
    public var currentCycleDay: Int { originalAffirmation.currentCycleDay }
    public var cycleStartDate: Date? { originalAffirmation.cycleStartDate }
    public var completedCycles: Int { originalAffirmation.completedCycles }
    public var currentRepetitions: Int { _currentRepetitions }  // Use the updated count
    public var dailyProgress: [Date: Int] { originalAffirmation.dailyProgress }
    public var lastRepetitionDate: Date? { originalAffirmation.lastRepetitionDate }
    public var energyLevel: Double { originalAffirmation.energyLevel }
    public var moodRating: Int? { originalAffirmation.moodRating }
    public var notes: String? { originalAffirmation.notes }
    public var isFavorite: Bool { originalAffirmation.isFavorite }
    public var playCount: Int { originalAffirmation.playCount }
    public var hasActiveCycle: Bool { originalAffirmation.hasActiveCycle }
    public var isCurrentCycleComplete: Bool { originalAffirmation.isCurrentCycleComplete }
    public var todayProgress: Double { originalAffirmation.todayProgress }
    public var cycleProgress: Double { originalAffirmation.cycleProgress }
    public var hasTodayQuotaMet: Bool { originalAffirmation.hasTodayQuotaMet }
    public var hasRecording: Bool { originalAffirmation.hasRecording }
    public var canPerformRepetition: Bool { originalAffirmation.canPerformRepetition }
    public var longestStreak: Int {
        get { originalAffirmation.longestStreak }
        set { /* Mock implementation - ignore setter */  }
    }

    public func recordRepetition() throws {
        try originalAffirmation.recordRepetition()
    }

    public func updateEnergyLevel(_ level: Double) {
        originalAffirmation.updateEnergyLevel(level)
    }

    public func recordMood(_ rating: Int, notes: String?) {
        originalAffirmation.recordMood(rating, notes: notes)
    }

    public static func == (
        lhs: RootViewMockAffirmationWithCount, rhs: RootViewMockAffirmationWithCount
    ) -> Bool {
        return lhs.id == rhs.id
    }
}

public class MockAffirmationRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
    public init() {}

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        // Create the affirmation stub
        let affirmation = await AffirmationStub(
            text: text, category: category, recordingURL: recordingURL)

        // Add it to the shared data source so it appears in the picker
        await RootViewMockRepetitionService.shared.addAffirmation(id: affirmation.id, text: text)

        return affirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
    }

    public func deleteAffirmation(id: UUID) async throws {
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        return []
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
    }
}

public class MockAffirmationService: AffirmationServiceProtocol, @unchecked Sendable {
    public init() {}

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        // Use the public AffirmationStub from Mocks folder
        return await AffirmationStub(text: text, category: category, recordingURL: recordingURL)
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
    }

    public func deleteAffirmation(id: UUID) async throws {
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        return []
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
        async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        return affirmation
    }

    public func getStatistics() async throws -> AffirmationStatistics {
        var statistics = AffirmationStatistics()
        statistics.totalAffirmations = 0
        statistics.activeAffirmations = 0
        statistics.favoriteAffirmations = 0
        statistics.totalCompletedCycles = 0
        statistics.totalRepetitions = 0
        statistics.categoryDistribution = [:]
        statistics.activeStreaks = 0
        statistics.completedCycles = 0
        statistics.longestCurrentStreak = 0
        return statistics
    }
}

public class MockPurchaseManager: PurchaseManagerSendable, @unchecked Sendable {
    public init() {}

    public func purchasePremium() async throws {}

    public func restorePurchases() async throws {}
}

public class MockDataExportService: DataExportServiceProtocol, @unchecked Sendable {
    public init() {}

    public func exportData() async throws -> URL {
        return URL(fileURLWithPath: NSTemporaryDirectory())
    }

    public func deleteAllData() async throws {
    }
}

// Add mock haptic and sync services for UI-only builds
public class MockHapticManager: HapticGenerating, @unchecked Sendable {
    public init() {}

    @MainActor
    public func lightImpact() async {}
    @MainActor
    public func mediumImpact() async {}
    @MainActor
    public func heavyImpact() async {}
    @MainActor
    public func playSuccess() async {}
    @MainActor
    public func playError() async {}
    @MainActor
    public func playWarning() async {}
    @MainActor
    public func playSelection() async {}
    @MainActor
    public func playAffirmationCompletionPattern() async {}
    @MainActor
    public func playCelebrationPattern() async {}
    @MainActor
    public func playStreakCompletionPattern() async {}
    @MainActor
    public func playImpact(style: ImpactStyle) async {}
}

public class MockSyncService: SyncServiceProtocol, @unchecked Sendable {
    public init() {}

    public var syncStatus: SyncStatus { .idle }
    public var syncStatusPublisher: AnyPublisher<SyncStatus, Never> {
        Just(.idle).eraseToAnyPublisher()
    }
    public var lastSyncError: Error? { nil }
    public var lastSyncDate: Date? { nil }
    public var isAutomaticSyncEnabled: Bool { true }
    public func sync() async throws {}
    public func syncNow() async throws {}
    public func setAutomaticSyncEnabled(_ enabled: Bool) async {}
    public func configureBackgroundSyncIfNeeded(isPremium: Bool) {}
    public func deleteAffirmationWithTombstone(id: UUID) async throws {}
}

/// Protocol for affirmation picker data source
public protocol AffirmationPickerDataSource {
    func getAllAffirmationsWithProgress() -> [(UUID, String, Int)]
    func getMostActiveAffirmation() -> (UUID, String, Int)?
    func hasProgress(for affirmationId: UUID) -> Bool
}

public class RootViewMockRepetitionService: RepetitionServiceProtocol, AffirmationPickerDataSource,
    @unchecked Sendable
{
    // Shared singleton instance to preserve counts across navigation
    public static let shared = RootViewMockRepetitionService()

    // Enhanced storage for multiple affirmations
    private var repetitionCounts: [UUID: Int] = [:]
    private var affirmationCycles: [UUID: Int] = [:]  // Track current day for each affirmation
    private var affirmationStreaks: [UUID: Int] = [:]  // Track longest streak for each affirmation
    private var lastRepetitionDates: [UUID: Date] = [:]  // Track last repetition date
    private var affirmationTexts: [UUID: String] = [:]  // Store affirmation texts for reference

    // Private initializer to enforce singleton pattern
    private init() {}

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        // Get the affirmation ID and text
        let affirmationId = await affirmation.id
        let affirmationText = await affirmation.text

        // Use a simple counter approach to avoid async property access issues
        let newCount = await MainActor.run {
            let currentCount = repetitionCounts[affirmationId] ?? 0
            let newCount = currentCount + 1
            repetitionCounts[affirmationId] = newCount

            // Store affirmation text for reference
            affirmationTexts[affirmationId] = affirmationText

            // Update last repetition date
            lastRepetitionDates[affirmationId] = Date()

            // Update cycle day if daily quota met
            if newCount % 100 == 0 {  // Every 100 repetitions = 1 day complete
                let currentDay = affirmationCycles[affirmationId] ?? 1
                affirmationCycles[affirmationId] = min(currentDay + 1, 7)

                // Update longest streak if applicable
                let currentStreak = affirmationCycles[affirmationId] ?? 1
                let longestStreak = affirmationStreaks[affirmationId] ?? 0
                if currentStreak > longestStreak {
                    affirmationStreaks[affirmationId] = currentStreak
                }
            }

            return newCount
        }

        print(
            "RootViewMockRepetitionService: Incremented count to \(newCount) for '\(affirmationText)'"
        )

        // Create a new mock affirmation with the updated count
        let updatedAffirmation = RootViewMockAffirmationWithCount(
            originalAffirmation: affirmation,
            newRepetitionCount: newCount
        )

        return RepetitionResult(
            success: true,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: false,
            isCycleComplete: false
        )
    }

    nonisolated public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        // Access the stored count using MainActor.assumeIsolated for synchronous access
        let (currentCount, currentDay, affirmationText) = MainActor.assumeIsolated {
            let count = self.repetitionCounts[affirmation.id] ?? 0
            let text = self.affirmationTexts[affirmation.id] ?? affirmation.text

            // Calculate current day based on repetitions (100 per day)
            let completedDays = count / 100
            let actualCurrentDay = min(completedDays + 1, 7)

            print(
                "RootViewMockRepetitionService.getProgress: Affirmation '\(text)' (\(affirmation.id))"
            )
            print(
                "RootViewMockRepetitionService.getProgress: Count: \(count), Completed Days: \(completedDays), Current Day: \(actualCurrentDay)"
            )
            print(
                "RootViewMockRepetitionService.getProgress: All stored counts: \(self.repetitionCounts)"
            )

            return (count, actualCurrentDay, text)
        }

        let progress = Double(currentCount % 100) / 100.0  // Progress within current day
        let cycleProgress = Double(currentCount) / 700.0  // Progress through entire 7-day cycle
        let hasTodayQuotaMet = (currentCount % 100) >= 100
        let isCycleComplete = currentCount >= 700
        let hasActiveCycle = currentCount > 0 && !isCycleComplete

        print(
            "RootViewMockRepetitionService.getProgress: Returning currentRepetitions: \(currentCount)"
        )

        return ProgressInfo(
            todayProgress: progress,
            cycleProgress: cycleProgress,
            currentDay: currentDay,
            totalDays: 7,
            currentRepetitions: currentCount,
            totalRepetitions: 100,
            hasTodayQuotaMet: hasTodayQuotaMet,
            isCycleComplete: isCycleComplete,
            hasActiveCycle: hasActiveCycle
        )
    }

    public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 1,
            longestStreak: 3,
            completedCycles: 1,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }

    public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        return true
    }

    public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return nil
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return false
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func validateStreaks() async throws -> StreakValidationResult {
        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: 1,
            brokenStreaks: 0
        )
    }

    // MARK: - Multi-Affirmation Support Methods

    /// Get all affirmations with their progress for the picker
    public func getAllAffirmationsWithProgress() -> [(UUID, String, Int)] {
        return MainActor.assumeIsolated {
            var results: [(UUID, String, Int)] = []

            for (id, text) in affirmationTexts {
                let count = repetitionCounts[id] ?? 0
                results.append((id, text, count))
            }

            // Sort by most recent activity (highest count first, then alphabetically)
            results.sort { first, second in
                if first.2 != second.2 {
                    return first.2 > second.2  // Higher count first
                }
                return first.1 < second.1  // Alphabetical for same count
            }

            return results
        }
    }

    /// Get the most active affirmation (highest count)
    public func getMostActiveAffirmation() -> (UUID, String, Int)? {
        return getAllAffirmationsWithProgress().first
    }

    /// Check if an affirmation has any progress
    public func hasProgress(for affirmationId: UUID) -> Bool {
        return MainActor.assumeIsolated {
            return (repetitionCounts[affirmationId] ?? 0) > 0
        }
    }

    /// Add a new affirmation to the data source
    @MainActor
    public func addAffirmation(id: UUID, text: String) {
        affirmationTexts[id] = text
        repetitionCounts[id] = 0
        affirmationCycles[id] = 1
        affirmationStreaks[id] = 0
        lastRepetitionDates[id] = nil

        print("🎯 ADDED NEW AFFIRMATION: '\(text)' with ID: \(id)")
    }

    /// Remove an affirmation from the data source
    @MainActor
    public func removeAffirmation(id: UUID) {
        let text = affirmationTexts[id] ?? "Unknown"
        affirmationTexts.removeValue(forKey: id)
        repetitionCounts.removeValue(forKey: id)
        affirmationCycles.removeValue(forKey: id)
        affirmationStreaks.removeValue(forKey: id)
        lastRepetitionDates.removeValue(forKey: id)

        print("🗑️ REMOVED AFFIRMATION: '\(text)' with ID: \(id)")
    }
}
