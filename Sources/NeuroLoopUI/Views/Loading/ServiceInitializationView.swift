import SwiftUI
import NeuroLoopCore

/// Loading screen shown during async service initialization
public struct ServiceInitializationView: View {
    @StateObject private var serviceFactory = AsyncServiceFactory.shared
    @EnvironmentObject private var themeManager: ThemeManager
    
    private let onInitializationComplete: () -> Void
    
    public init(onInitializationComplete: @escaping () -> Void) {
        self.onInitializationComplete = onInitializationComplete
    }
    
    public var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    themeManager.currentTheme.backgroundColor.asColor,
                    themeManager.currentTheme.cardBackgroundColor.asColor
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // App logo/icon area
                VStack(spacing: 16) {
                    // Animated logo placeholder
                    RoundedRectangle(cornerRadius: 20)
                        .fill(themeManager.currentTheme.accentColor.asColor.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 40, weight: .light))
                                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                        )
                        .scaleEffect(serviceFactory.isInitializing ? 1.1 : 1.0)
                        .animation(
                            .easeInOut(duration: 1.5).repeatForever(autoreverses: true),
                            value: serviceFactory.isInitializing
                        )
                    
                    Text("NeuroLoop")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                }
                
                // Loading progress section
                VStack(spacing: 24) {
                    // Progress bar
                    VStack(spacing: 12) {
                        HStack {
                            Text("Initializing...")
                                .font(.headline)
                                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                            
                            Spacer()
                            
                            Text("\(Int(serviceFactory.initializationProgress * 100))%")
                                .font(.subheadline)
                                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        }
                        
                        // Progress bar
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                // Background
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.3))
                                    .frame(height: 8)
                                
                                // Progress fill
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                themeManager.currentTheme.accentColor.asColor,
                                                themeManager.currentTheme.accentColor.asColor.opacity(0.8)
                                            ]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(
                                        width: geometry.size.width * serviceFactory.initializationProgress,
                                        height: 8
                                    )
                                    .animation(.easeInOut(duration: 0.3), value: serviceFactory.initializationProgress)
                            }
                        }
                        .frame(height: 8)
                    }
                    
                    // Current step
                    Text(serviceFactory.currentInitializationStep)
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .multilineTextAlignment(.center)
                        .animation(.easeInOut(duration: 0.2), value: serviceFactory.currentInitializationStep)
                    
                    // Loading dots animation
                    HStack(spacing: 8) {
                        ForEach(0..<3, id: \.self) { index in
                            Circle()
                                .fill(themeManager.currentTheme.accentColor.asColor.opacity(0.6))
                                .frame(width: 8, height: 8)
                                .scaleEffect(serviceFactory.isInitializing ? 1.2 : 0.8)
                                .animation(
                                    .easeInOut(duration: 0.6)
                                        .repeatForever(autoreverses: true)
                                        .delay(Double(index) * 0.2),
                                    value: serviceFactory.isInitializing
                                )
                        }
                    }
                }
                .padding(.horizontal, 40)
                
                Spacer()
                
                // Version info or tips
                VStack(spacing: 8) {
                    Text("Preparing your affirmation experience...")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .opacity(0.7)
                    
                    Text("v1.0.0")
                        .font(.caption2)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .opacity(0.5)
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal, 32)
        }
        .onAppear {
            startInitialization()
        }
        .onChange(of: serviceFactory.isReady) { _, isReady in
            if isReady {
                // Add a small delay to show completion state
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    onInitializationComplete()
                }
            }
        }
    }
    
    private func startInitialization() {
        Task {
            await serviceFactory.initializeCriticalServices()
        }
    }
}

// MARK: - Preview

#Preview {
    ServiceInitializationView {
        print("Initialization complete!")
    }
    .environmentObject(ThemeManager.shared)
}

// MARK: - Specialized Loading Components

/// Skeleton view for affirmation cards
public struct AffirmationCardSkeleton: View {
    @EnvironmentObject private var themeManager: ThemeManager

    public init() {}

    public var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title skeleton
            SkeletonView(size: CGSize(width: 200, height: 20))

            // Content skeleton
            VStack(alignment: .leading, spacing: 8) {
                SkeletonView(size: CGSize(width: 300, height: 16))
                SkeletonView(size: CGSize(width: 250, height: 16))
                SkeletonView(size: CGSize(width: 280, height: 16))
            }

            // Progress skeleton
            HStack {
                SkeletonView(shape: .circle, size: CGSize(width: 60, height: 60))
                Spacer()
                SkeletonView(size: CGSize(width: 100, height: 16))
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.1))
        )
    }
}

/// Skeleton view for speech recognition setup
public struct SpeechSetupSkeleton: View {
    @EnvironmentObject private var themeManager: ThemeManager

    public init() {}

    public var body: some View {
        VStack(spacing: 16) {
            // Recording button skeleton
            SkeletonView(shape: .circle, size: CGSize(width: 120, height: 120))

            // Status text skeleton
            SkeletonView(size: CGSize(width: 200, height: 20))

            // Progress skeleton
            SkeletonView(size: CGSize(width: 150, height: 16))
        }
        .padding(20)
    }
}
