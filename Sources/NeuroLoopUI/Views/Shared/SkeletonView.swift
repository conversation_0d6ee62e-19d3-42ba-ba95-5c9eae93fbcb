import SwiftUI
import NeuroLoop<PERSON>ore

public struct SkeletonView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    public var shape: SkeletonShape = .rectangle
    public var size: CGSize
    public var cornerRadius: CGFloat = 8

    public enum SkeletonShape {
        case rectangle, circle
    }

    public init(shape: SkeletonShape = .rectangle, size: CGSize, cornerRadius: CGFloat = 8) {
        self.shape = shape
        self.size = size
        self.cornerRadius = cornerRadius
    }

    public var body: some View {
        Group {
            switch shape {
            case .rectangle:
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(themeManager.currentTheme.disabledColor.asColor.opacity(0.15))
                    .frame(width: size.width, height: size.height)
            case .circle:
                Circle()
                    .fill(themeManager.currentTheme.disabledColor.asColor.opacity(0.15))
                    .frame(width: size.width, height: size.height)
            }
        }
        .redacted(reason: .placeholder)
        .accessibilityHidden(true)
    }
}