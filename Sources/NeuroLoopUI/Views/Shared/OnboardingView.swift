import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

/// A view that introduces users to the app's key features and functionality
@available(iOS 17.0, macOS 14.0, *)
public struct OnboardingView: View {
    // MARK: - Properties

    @Binding private var isPresented: Bool
    @State private var currentPage: Int = 0
    @EnvironmentObject private var themeManager: ThemeManager

    private let pages: [OnboardingPage] = [
        OnboardingPage(
            title: "Welcome to NeuroLoop",
            description:
                "Your personal journey to positive change starts here. Let's explore how affirmations can transform your mindset.",
            imageName: "brain.head.profile",
            accentColor: Color(red: 0.1, green: 0.4, blue: 0.8)
        ),
        OnboardingPage(
            title: "Daily Practice",
            description:
                "Build lasting habits through daily repetition. Track your progress and watch your mindset evolve.",
            imageName: "chart.line.uptrend.xyaxis",
            accentColor: Color(red: 0.2, green: 0.7, blue: 0.3)
        ),
        OnboardingPage(
            title: "Ready to Begin?",
            description:
                "Start your journey to positive change with <PERSON>euro<PERSON>oop. Your path to a better mindset begins now.",
            imageName: "sparkles",
            accentColor: Color(red: 0.1, green: 0.4, blue: 0.8)
        ),
    ]

    // MARK: - Initialization

    public init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            themeManager.currentTheme.backgroundColor.asColor
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // Page content
                #if os(iOS)
                    TabView(selection: $currentPage) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            OnboardingPageView(page: pages[index])
                                .tag(index)
                        }
                    }
                    .tabViewStyle(.page)
                    .indexViewStyle(.page(backgroundDisplayMode: .always))
                #else
                    VStack {
                        OnboardingPageView(page: pages[currentPage])

                        // Page indicator
                        HStack(spacing: 8) {
                            ForEach(0..<pages.count, id: \.self) { index in
                                Circle()
                                    .fill(
                                        index == currentPage
                                            ? themeManager.currentTheme.accentColor.asColor
                                            : themeManager.currentTheme.secondaryTextColor.asColor
                                                .opacity(0.3)
                                    )
                                    .frame(width: 8, height: 8)
                            }
                        }
                        .padding(.top)
                    }
                #endif

                // Navigation buttons
                HStack {
                    if currentPage > 0 {
                        Button("Back") {
                            withAnimation {
                                currentPage -= 1
                            }
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }

                    Spacer()

                    if currentPage < pages.count - 1 {
                        Button("Next") {
                            withAnimation {
                                currentPage += 1
                            }
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    } else {
                        Button("Get Started") {
                            isPresented = false
                        }
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            Group {
                                if let gradient = themeManager.currentTheme.accentColor.asGradient {
                                    gradient
                                } else {
                                    themeManager.currentTheme.accentColor.asColor
                                }
                            }
                        )
                        .cornerRadius(10)
                    }
                }
                .padding()
            }
        }
        .accessibilityElement(children: .contain)
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct OnboardingView_Previews: PreviewProvider {
        static var previews: some View {
            OnboardingView(isPresented: .constant(true))
        }
    }
#endif
