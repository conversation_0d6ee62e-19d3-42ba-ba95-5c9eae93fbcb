import NeuroLoopCore
import NeuroLoopInterfaces
import SwiftUI

/// A view that provides help and guidance for using the app
@available(iOS 17.0, macOS 14.0, *)
public struct HelpView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var selectedSection: HelpSection = .gettingStarted

    // MARK: - Body

    public var body: some View {
        NavigationView {
            List {
                ForEach(HelpSection.allCases) { section in
                    Section {
                        ForEach(section.items) { item in
                            NavigationLink(destination: HelpDetailView(item: item)) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(item.title)
                                        .font(.headline)
                                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                                    Text(item.shortDescription)
                                        .font(.subheadline)
                                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                                }
                                .padding(.vertical, 4)
                            }
                        }
                    } header: {
                        Text(section.title)
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
            }
            .navigationTitle(
                Text("Help").foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            )
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                #if os(iOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                #else
                    ToolbarItem(placement: .automatic) {
                        Button("Done") {
                            dismiss()
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                #endif
            }
        }
    }
}

// MARK: - Help Section

private enum HelpSection: String, CaseIterable, Identifiable {
    case gettingStarted = "Getting Started"
    case features = "Features"
    case premium = "Premium Features"
    case troubleshooting = "Troubleshooting"

    var id: String { rawValue }

    var title: String { rawValue }

    var items: [HelpItem] {
        switch self {
        case .gettingStarted:
            return [
                HelpItem(
                    title: "Creating Your First Affirmation",
                    shortDescription: "Learn how to write and record your first affirmation",
                    content: """
                        To create your first affirmation:
                        1. Tap the + button in the Library tab
                        2. Write your affirmation in the present tense
                        3. Choose a category that fits your goal
                        4. Optionally record your voice
                        5. Tap Save to add it to your library

                        Tips for effective affirmations:
                        • Use present tense ("I am" not "I will be")
                        • Keep it positive and specific
                        • Make it personal and meaningful
                        • Focus on what you want, not what you don't want
                        """
                ),
                HelpItem(
                    title: "Daily Practice",
                    shortDescription: "How to maintain your daily affirmation practice",
                    content: """
                        Daily practice is key to creating lasting change:
                        1. Set a daily goal in Settings
                        2. Use the Home tab to track your progress
                        3. Complete your repetitions each day
                        4. Use guided sessions for focused practice
                        5. Celebrate your milestones

                        Remember: Consistency is more important than perfection. Even a few repetitions each day can make a difference.
                        """
                ),
            ]
        case .features:
            return [
                HelpItem(
                    title: "Guided Sessions",
                    shortDescription: "Using guided sessions for focused practice",
                    content: """
                        Guided sessions help you practice effectively:
                        1. Choose a session from the Home tab
                        2. Follow the breathing exercises
                        3. Repeat your affirmation as guided
                        4. Complete the reflection step

                        Sessions include:
                        • Preparation phase
                        • Breathing exercises
                        • Affirmation repetition
                        • Reflection and completion
                        """
                ),
                HelpItem(
                    title: "Progress Tracking",
                    shortDescription: "Understanding your progress and statistics",
                    content: """
                        Track your progress in several ways:
                        • Daily repetition count
                        • Cycle completion status
                        • Streak tracking
                        • Progress charts

                        View your progress in:
                        • Home tab overview
                        • Detailed statistics
                        • Achievement badges
                        """
                ),
            ]
        case .premium:
            return [
                HelpItem(
                    title: "Premium Benefits",
                    shortDescription: "Features available with Premium",
                    content: """
                        Premium features include:
                        ⭐️ Cross-Device Sync
                        • Keep your affirmations and progress synchronized
                        • Automatic sync across all your devices
                        • Manual sync option available

                        ⭐️ Unlimited Affirmations
                        • Create as many affirmations as you need
                        • No restrictions on categories
                        • Full access to all features

                        ⭐️ Advanced Analytics
                        • Detailed progress tracking
                        • Custom reports and insights
                        • Export functionality

                        ⭐️ Custom Themes
                        • Choose from premium themes
                        • Customize colors and appearance
                        • Dark mode support

                        ⭐️ Priority Support
                        • Faster response times
                        • Direct support channel
                        • Premium-only features

                        Upgrade anytime from the Settings tab.
                        """
                ),
                HelpItem(
                    title: "Cross-Device Sync",
                    shortDescription: "Keeping your data in sync across devices",
                    content: """
                        Cross-Device Sync keeps your data up to date:
                        • Syncs affirmations across devices
                        • Maintains progress and statistics
                        • Works automatically in background
                        • Manual sync option available

                        Requirements:
                        • Premium subscription
                        • Internet connection
                        • iCloud account

                        Troubleshooting:
                        • Check internet connection
                        • Verify iCloud is enabled
                        • Try manual sync
                        • Contact support if issues persist
                        """
                ),
            ]
        case .troubleshooting:
            return [
                HelpItem(
                    title: "Common Issues",
                    shortDescription: "Solutions for common problems",
                    content: """
                        Common issues and solutions:

                        Sync Issues:
                        • Check internet connection
                        • Verify iCloud is enabled
                        • Try manual sync
                        • Contact support if persistent

                        Audio Problems:
                        • Check microphone permissions
                        • Restart the app
                        • Update to latest version

                        Performance:
                        • Clear app cache
                        • Restart device
                        • Check storage space

                        Premium Features:
                        • Verify subscription status
                        • Try restoring purchases
                        • Check internet connection
                        • Contact support if needed
                        """
                ),
                HelpItem(
                    title: "Contact Support",
                    shortDescription: "Getting help from our support team",
                    content: """
                        Need additional help?

                        Contact us:
                        • Email: <EMAIL>
                        • In-app feedback
                        • Premium priority support

                        Include:
                        • Device information
                        • App version
                        • Detailed description
                        • Steps to reproduce

                        Premium users:
                        • Get priority support
                        • Faster response times
                        • Direct support channel
                        """
                ),
            ]
        }
    }
}

// MARK: - Help Item

private struct HelpItem: Identifiable {
    let id = UUID()
    let title: String
    let shortDescription: String
    let content: String
}

// MARK: - Help Detail View

private struct HelpDetailView: View {
    let item: HelpItem
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text(item.title)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Text(item.content)
                    .font(.body)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            }
            .padding()
        }
        .navigationTitle(item.title)
        #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
        #endif
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct HelpView_Previews: PreviewProvider {
        static var previews: some View {
            HelpView()
                .environmentObject(ThemeManager.shared)
        }
    }
#endif
