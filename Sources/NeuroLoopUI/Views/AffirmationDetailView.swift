import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import SwiftUI

/// View for displaying and interacting with an affirmation
@available(iOS 17.0, macOS 14.0, *)
public struct AffirmationDetailView: View {
    // MARK: - Properties
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var viewModel: AffirmationDetailViewModel
    public let affirmation: any AffirmationProtocol
    @State private var isPlaying = false
    @State private var showingDeleteConfirmation = false
    @StateObject private var audioPlaybackViewModel = AudioRecordingViewModel(
        audioRecordingService: {
            #if os(iOS)
                return AudioRecordingService(audioFileManager: AudioFileManager.shared)
            #else
                return AudioRecordingService()
            #endif
        }())
    @State private var redrawCount = 0
    @EnvironmentObject var themeManager: NeuroLoopCore.ThemeManager

    // MARK: - Initialization
    public init(
        viewModel: AffirmationDetailViewModel,
        affirmation: any AffirmationProtocol
    ) {
        self.viewModel = viewModel
        self.affirmation = affirmation
    }

    @ViewBuilder
    private func audioPlaybackSection(recordingURL: URL?) -> some View {
        if let recordingURL = recordingURL {
            VStack(spacing: 12) {
                AudioWaveform(
                    samples: audioPlaybackViewModel.audioSamples,
                    color: audioPlaybackViewModel.isPlaying ? .blue : .gray,
                    backgroundColor: Color.gray.opacity(0.2),
                    lineWidth: 2
                )
                .frame(height: 60)
                .accessibilityHidden(true)

                HStack {
                    PlayButton(
                        isPlaying: audioPlaybackViewModel.isPlaying,
                        size: 50,
                        color: themeManager.currentTheme.accentColor.asColor
                    ) {
                        audioPlaybackViewModel.togglePlayback()
                    }
                    .accessibilityLabel(
                        audioPlaybackViewModel.isPlaying ? "Pause audio" : "Play audio"
                    )
                    .accessibilityHint(
                        "Double tap to " + (audioPlaybackViewModel.isPlaying ? "pause" : "play")
                            + " the affirmation audio recording")

                    Spacer()

                    Text(audioPlaybackViewModel.isPlaying ? "Playing..." : "Play Audio")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .accessibilityLabel(
                            audioPlaybackViewModel.isPlaying
                                ? "Audio is playing" : "Audio is paused")
                }
                .padding(.top, 4)

                if let error = audioPlaybackViewModel.error {
                    Text(error.localizedDescription)
                        .foregroundColor(.red)
                        .font(.caption)
                }
            }
            .padding()
            .background(themeManager.currentTheme.backgroundColor.asColor)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            .onAppear {
                audioPlaybackViewModel.playRecording(from: recordingURL)
            }
        }
    }

    @ViewBuilder
    private func actionButtonsSection() -> some View {
        VStack(spacing: 12) {
            Button(action: {
                showingDeleteConfirmation = true
            }) {
                Text("Delete Affirmation")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.red, lineWidth: 1)
                    )
            }
            .accessibilityLabel("Delete affirmation")
            .accessibilityHint("Double tap to delete this affirmation")
        }
        .padding(.top)
    }

    @ViewBuilder
    private func mainContentVStack() -> some View {
        VStack(spacing: 20) {
            // Affirmation text
            Text(affirmation.text)
                .font(.title2)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                .multilineTextAlignment(.center)
                .padding()
                .frame(maxWidth: .infinity)
                .background(themeManager.currentTheme.backgroundColor.asColor)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                .accessibilityLabel("Affirmation: \(affirmation.text)")
                .accessibilityHint("Main affirmation text")

            // Category and favorite
            HStack {
                Label(
                    affirmation.category.displayName,
                    systemImage: affirmation.category.iconName
                )
                .font(.subheadline)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                .accessibilityLabel("Category: \(affirmation.category.displayName)")
                .accessibilityHint("Affirmation category")

                Spacer()

                Button(action: {
                    viewModel.editAffirmation()
                }) {
                    Label(
                        "Edit",
                        systemImage: "pencil"
                    )
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            }
            .padding()
            .background(themeManager.currentTheme.backgroundColor.asColor)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)

            // Repetition tracking integration
            // (Placeholder, as actual implementation may require more context)
            // RepetitionTrackingView(...)

            // Audio playback (if available)
            audioPlaybackSection(recordingURL: nil)

            // Action buttons
            actionButtonsSection()
        }
        .padding()
    }

    // MARK: - Body
    public var body: some View {
        NavigationView {
            mainContent
        }
    }

    private var mainContent: some View {
        ZStack(alignment: .topTrailing) {
            themeManager.currentTheme.backgroundColor.asColor
                .ignoresSafeArea()

            ScrollView {
                mainContentVStack()
            }
        }
        .navigationTitle(
            Text("Affirmation Details").foregroundColor(
                themeManager.currentTheme.primaryTextColor.asColor)
        )
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            #if os(iOS)
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            #elseif os(macOS)
                ToolbarItem(placement: .automatic) {
                    Button("Close") {
                        dismiss()
                    }
                }
            #endif
        }
        .alert("Delete Affirmation", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Delete", role: .destructive) {
                viewModel.deleteAffirmation()
                dismiss()
            }
        } message: {
            Text(
                "Are you sure you want to delete this affirmation? This action cannot be undone."
            )
            .accessibilityLabel("Delete affirmation confirmation")
            .accessibilityHint("This action cannot be undone")
        }
    }
}

#if DEBUG
    struct AffirmationDetailView_Previews: PreviewProvider {
        static var previews: some View {
            let previewAffirmation = createPreviewAffirmation(
                text: "I am confident and capable",
                category: .confidence,
                recordingURL: nil,
                currentRepetitions: 5,
                energyLevel: 0.5,
                isFavorite: true
            )

            return NavigationStack {
                AffirmationDetailView(
                    viewModel: AffirmationDetailViewModel(affirmation: previewAffirmation),
                    affirmation: previewAffirmation
                )
                .environmentObject(ThemeManager.shared)
            }
        }
    }
#endif
