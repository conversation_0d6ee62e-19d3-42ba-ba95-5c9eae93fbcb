import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

public struct AudioRecordingView: View {
    @ObservedObject var viewModel: AudioRecordingViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    @State private var showError = false
    @State private var errorMessage = ""

    // Initialize with a service
    public init(audioRecordingService: AudioRecordingServiceProtocol) {
        _viewModel = ObservedObject(
            wrappedValue: AudioRecordingViewModel(audioRecordingService: audioRecordingService)
        )
    }

    // Initialize with a view model (for use in AddAffirmationView)
    public init(viewModel: AudioRecordingViewModel) {
        _viewModel = ObservedObject(wrappedValue: viewModel)
    }

    public var body: some View {
        VStack(spacing: 16) {
            AudioWaveform(
                samples: viewModel.audioSamples,
                color: viewModel.isRecording
                    ? themeManager.currentTheme.errorColor.color
                    : themeManager.currentTheme.accentColor.asColor,
                backgroundColor: themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.2)
            )
            .frame(height: 60)
            .accessibilityHidden(true)

            HStack {
                if viewModel.isRecording {
                    Text("Recording...")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                } else {
                    Text("Ready to record")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                }

                Spacer()

                Button(action: {
                    Task {
                        do {
                            if viewModel.isRecording {
                                try await viewModel.stopRecording()
                            } else {
                                try await viewModel.startRecording()
                            }
                        } catch {
                            errorMessage = error.localizedDescription
                            showError = true
                        }
                    }
                }) {
                    Image(
                        systemName: viewModel.isRecording ? "stop.circle.fill" : "mic.circle.fill"
                    )
                    .font(.system(size: 44))
                    .foregroundColor(
                        viewModel.isRecording
                            ? themeManager.currentTheme.errorColor.asColor
                            : themeManager.currentTheme.accentColor.asColor)
                }
                .accessibilityLabel(viewModel.isRecording ? "Stop recording" : "Start recording")
                .accessibilityHint(
                    "Double tap to " + (viewModel.isRecording ? "stop" : "start") + " recording")
            }

            if let error = viewModel.error {
                Text(error.localizedDescription)
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.errorColor.color)
            }
        }
        .padding()
        .background(themeManager.currentTheme.cardBackgroundColor.asColor)
        .cornerRadius(12)
        .shadow(
            color: themeManager.currentTheme.shadowColor.color.opacity(0.1), radius: 5, x: 0, y: 2
        )
        .alert("Error", isPresented: $showError) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(errorMessage)
        }
    }
}

// MARK: - Preview

struct AudioRecordingView_Previews: PreviewProvider {
    static var previews: some View {
        AudioRecordingView(
            audioRecordingService: PreviewServiceFactory.shared.getAudioRecordingService()
        )
        .environmentObject(PreviewServiceFactory.shared.getThemeManager())
        .environment(\.hapticManager, PreviewServiceFactory.shared.getHapticManager())
    }
}

// MARK: - Mock Service

private class MockAudioRecordingService: AudioRecordingServiceProtocol, @unchecked Sendable {
    @Published var isRecording: Bool = false
    @Published var recordingTime: TimeInterval = 0
    @Published var recordingPower: Double = 0
    @Published var recordingURL: URL? = nil
    @Published var isPlaying: Bool = false
    @Published var playbackProgress: Double = 0
    @Published var playbackTime: TimeInterval = 0
    @Published var error: Error? = nil

    var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    var errorPublisher: Published<Error?>.Publisher { $error }

    func startRecording() async throws {}
    func stopRecording() async throws -> URL { URL(fileURLWithPath: "") }
    func deleteRecording() {}
    func startPlayback() async throws {}
    func pausePlayback() {}
    func stopPlayback() {}
    func deleteRecording(at url: URL) async throws {}
    func getRecordingDuration(for url: URL) async throws -> TimeInterval { 0 }
    func getRecordingWaveform(for url: URL) async throws -> [Float] { [] }

    /// Diagnostic method to test microphone access and audio session setup
    func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Mock microphone test successful", -30.0)
    }
}
