import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct SettingsView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: SettingsViewModel

    public init(viewModel: SettingsViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    public var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                ThemeSectionView()
                DataPrivacySectionView(viewModel: viewModel)
                PremiumSectionView(viewModel: viewModel)
            }
            .padding()
        }
        .navigationTitle(
            Text("Settings").foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
        )
        .navigationBarTitleDisplayMode(.large)
        .background(
            Group {
                if let gradient = themeManager.currentTheme.backgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.backgroundColor.asColor
                }
            }
            .ignoresSafeArea()
        )
        .onAppear {
            viewModel.loadSettings()
        }
    }
}



// MARK: - Data & Privacy Section
@available(iOS 17.0, macOS 14.0, *)
private struct DataPrivacySectionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Data & Privacy")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            Button(action: { viewModel.exportData() }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                    Text("Export Data")
                }
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            }

            Button(action: { viewModel.clearData() }) {
                HStack {
                    Image(systemName: "trash")
                    Text("Clear All Data")
                }
                .foregroundColor(.red)
            }
        }
        .padding()
        .background(sectionBackground)
    }

    private var sectionBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                themeManager.currentTheme.cardBackgroundColor.asGradient
                    ?? themeManager.currentTheme.cardBackgroundColor.asColor
                    .asFallbackGradient()
            )
            .shadow(
                color: themeManager.currentTheme.shadowColor.asColor.opacity(0.1),
                radius: 10,
                x: 0,
                y: 5
            )
    }
}



// MARK: - Premium Section
@available(iOS 17.0, macOS 14.0, *)
private struct PremiumSectionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        SettingsSectionView(title: "Premium") {
            HStack {
                Text(viewModel.isPremiumUser ? "Premium Active" : "Upgrade to Premium")
                    .font(.headline)
                Spacer()
                if viewModel.isPremiumUser {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Button("View Plans") {
                        // Navigate to SubscriptionView
                        print("Navigate to SubscriptionView")
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct SettingsView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationStack {
                SettingsView(
                    viewModel: SettingsViewModel(
                        userDefaults: UserDefaults.standard,
                        purchaseManager: PreviewMockPurchaseManager(),
                        dataExportService: PreviewMockDataExportService(),
                        themeManager: ThemeManager.shared,
                        hapticManager: HapticManager.shared,
                        syncService: nil
                    )
                )
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif
