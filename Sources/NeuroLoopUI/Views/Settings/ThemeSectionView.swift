import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct ThemeSectionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var showingThemeSelector = false

    public init() {}

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Appearance")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

            // Beautiful theme selector button
            Button(action: {
                showingThemeSelector = true
            }) {
                HStack(spacing: 12) {
                    // Paintbrush icon
                    Image(systemName: "paintbrush.fill")
                        .font(.title2)
                        .foregroundColor(.white)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Choose Your Theme")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)

                        Text("Currently: \(themeManager.currentTheme.name)")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            themeManager.currentTheme.accentColor.asGradient ??
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.accentColor.asColor,
                                    themeManager.currentTheme.accentColor.asColor.opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: themeManager.currentTheme.shadowColor.asColor.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient ??
                    LinearGradient(
                        gradient: Gradient(colors: [
                            themeManager.currentTheme.cardBackgroundColor.asColor,
                            themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
        .sheet(isPresented: $showingThemeSelector) {
            ThemeSelectorView()
                .environmentObject(themeManager)
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct ThemeSectionView_Previews: PreviewProvider {
        static var previews: some View {
            ThemeSectionView()
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
                .padding()
                .background(Color.gray.opacity(0.1))
        }
    }
#endif
