import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct ThemeSelectorView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTheme: NeuroLoopTypes.Theme

    public init() {
        _selectedTheme = State(initialValue: ThemeManager.shared.currentTheme)
    }

    public var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Choose Your Theme")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(selectedTheme.primaryTextColor.color)
                            .animation(.easeInOut, value: selectedTheme)

                        Text("Select a beautiful gradient theme that matches your mood")
                            .font(.subheadline)
                            .foregroundColor(selectedTheme.secondaryTextColor.color)
                            .multilineTextAlignment(.center)
                            .animation(.easeInOut, value: selectedTheme)
                    }
                    .padding(.top, 20)
                    .padding(.horizontal)

                    // Theme Grid
                    LazyVGrid(
                        columns: [
                            GridItem(.flexible(), spacing: 16),
                            GridItem(.flexible(), spacing: 16),
                        ], spacing: 20
                    ) {
                        ForEach(themeManager.builtInThemes, id: \.id) { theme in
                            ThemePreviewCard(
                                theme: theme,
                                isSelected: selectedTheme.id == theme.id,
                                onSelect: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        selectedTheme = theme
                                        themeManager.setTheme(theme)
                                    }
                                }
                            )
                        }
                    }
                    .padding(.horizontal)

                    // Apply Button
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            dismiss()
                        }
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title3)
                            Text("Apply Theme")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)

                    Spacer(minLength: 40)
                }
            }
            .background(
                // Use current theme background with animation
                selectedTheme.backgroundColor.asGradient
                    ?? LinearGradient(
                        gradient: Gradient(colors: [
                            selectedTheme.backgroundColor.asColor,
                            selectedTheme.backgroundColor.asColor.opacity(0.8),
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
            )
            .animation(.easeInOut(duration: 0.3), value: selectedTheme)
            .ignoresSafeArea()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        // Revert to original theme with animation
                        withAnimation(.easeInOut(duration: 0.3)) {
                            themeManager.setTheme(ThemeManager.shared.currentTheme)
                            dismiss()
                        }
                    }
                    .foregroundColor(selectedTheme.primaryTextColor.color)
                    .animation(.easeInOut, value: selectedTheme)
                }
            }
        }
        .onAppear {
            selectedTheme = themeManager.currentTheme
        }
    }
}

@available(iOS 17.0, macOS 14.0, *)
private struct ThemePreviewCard: View {
    let theme: NeuroLoopTypes.Theme
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 12) {
                // Theme Preview
                ZStack {
                    // Background gradient
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            theme.backgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        theme.backgroundColor.asColor,
                                        theme.backgroundColor.asColor.opacity(0.8),
                                    ]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                        )
                        .frame(height: 80)

                    // Sample card
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            theme.cardBackgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        theme.cardBackgroundColor.asColor,
                                        theme.cardBackgroundColor.asColor.opacity(0.8),
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                        )
                        .frame(width: 60, height: 40)
                        .shadow(color: theme.shadowColor.color.opacity(0.3), radius: 4, x: 0, y: 2)
                }

                // Theme Name
                Text(theme.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(theme.primaryTextColor.color)
                    .multilineTextAlignment(.center)
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(theme.primaryTextColor.asColor.opacity(isSelected ? 0.2 : 0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected
                                    ? theme.primaryTextColor.asColor.opacity(0.6)
                                    : theme.borderColor.asColor,
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct ThemeSelectorView_Previews: PreviewProvider {
        static var previews: some View {
            ThemeSelectorView()
                .environmentObject(ThemeManager.shared)
        }
    }
#endif
