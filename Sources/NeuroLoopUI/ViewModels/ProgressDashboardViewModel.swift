import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class ProgressDashboardViewModel: ObservableObject {
    public enum Filter: String, CaseIterable {
        case all, active, completed, notStarted, favorites
        public var displayName: String {
            switch self {
            case .all: return "All"
            case .active: return "Active"
            case .completed: return "Completed"
            case .notStarted: return "Not Started"
            case .favorites: return "Favorites"
            }
        }
    }

    @Published public private(set) var affirmations: [any AffirmationProtocol] = []
    @Published public private(set) var filteredAffirmations: [any AffirmationProtocol] = []
    @Published public var overallCompletionRate: Double = 0.0
    @Published public var activeCount: Int = 0
    @Published public var completedCount: Int = 0
    @Published public var notStartedCount: Int = 0
    @Published public var currentStreak: Int = 0
    @Published public var longestStreak: Int = 0
    @Published public var averageConsistency: Double = 0.0
    @Published public var chartData: [Double]? = nil
    @Published public private(set) var recentAffirmations: [any AffirmationProtocol] = []
    @Published public private(set) var topAffirmations: [any AffirmationProtocol] = []
    @Published public private(set) var selectedAffirmation: (any AffirmationProtocol)?

    // Journal Analytics
    @Published public var moodStatistics: MoodStatistics?
    @Published public var journalStreak: Int = 0
    @Published public var totalJournalEntries: Int = 0
    @Published public var averageMood: Double = 0.0
    @Published public var moodTrend: MoodTrend = .stable

    private let affirmationService: AffirmationServiceProtocol
    private let journalRepository: JournalRepositoryProtocol?
    private var allAffirmations: [any AffirmationProtocol] = []
    private var filter: Filter = .all
    private var search: String = ""

    public init(affirmationService: AffirmationServiceProtocol, journalRepository: JournalRepositoryProtocol? = nil) {
        self.affirmationService = affirmationService
        self.journalRepository = journalRepository
        Task { await loadData() }
    }

    public func loadData() async {
        do {
            let affirmations = try await affirmationService.fetchAffirmations()
            self.allAffirmations = affirmations
            self.affirmations = affirmations
            aggregateMetrics()
            await loadJournalAnalytics()
            applyFilter(filter)
        } catch {
            print("Error loading affirmations: \(error)")
        }
    }

    private func loadJournalAnalytics() async {
        guard let journalRepository = journalRepository else { return }

        do {
            // Get last 30 days of data
            let endDate = Date()
            let startDate = Calendar.current.date(byAdding: .day, value: -30, to: endDate) ?? endDate

            let stats = try await journalRepository.getMoodStatistics(from: startDate, to: endDate)

            await MainActor.run {
                self.moodStatistics = stats
                self.journalStreak = stats.streakDays
                self.totalJournalEntries = stats.totalEntries
                self.averageMood = stats.averageMood
                self.moodTrend = stats.moodTrend
            }
        } catch {
            print("Error loading journal analytics: \(error)")
        }
    }

    private func aggregateMetrics() {
        let total = allAffirmations.count
        let completed = allAffirmations.filter { $0.isCurrentCycleComplete }.count
        let active = allAffirmations.filter { $0.hasActiveCycle }.count
        let notStarted = allAffirmations.filter { !$0.hasActiveCycle && !$0.isCurrentCycleComplete }
            .count
        // We'll use this value in a future implementation
        _ = allAffirmations.filter { $0.isFavorite }.count
        let totalReps = allAffirmations.reduce(0) { $0 + $1.currentRepetitions }
        let totalPossible =
            total * AffirmationConstants.DAILY_REPETITIONS * AffirmationConstants.CYCLE_DAYS
        overallCompletionRate = totalPossible > 0 ? Double(totalReps) / Double(totalPossible) : 0.0
        activeCount = active
        completedCount = completed
        notStartedCount = notStarted
        // Streaks - simplified for mock implementation
        let streaks = allAffirmations.map { _ in 1 } // Mock streak data
        currentStreak = streaks.max() ?? 0
        longestStreak = streaks.max() ?? 0
        averageConsistency =
            streaks.isEmpty
            ? 0.0
            : Double(streaks.reduce(0, +)) / Double(streaks.count)
                / Double(AffirmationConstants.CYCLE_DAYS)
        // Chart data (placeholder: daily completion rates)
        chartData = Array(repeating: overallCompletionRate, count: 7)
    }

    public func applyFilter(_ filter: Filter) {
        self.filter = filter
        switch filter {
        case .all:
            filteredAffirmations = allAffirmations
        case .active:
            filteredAffirmations = allAffirmations.filter { $0.hasActiveCycle }
        case .completed:
            filteredAffirmations = allAffirmations.filter { $0.isCurrentCycleComplete }
        case .notStarted:
            filteredAffirmations = allAffirmations.filter {
                !$0.hasActiveCycle && !$0.isCurrentCycleComplete
            }
        case .favorites:
            filteredAffirmations = allAffirmations.filter { $0.isFavorite }
        }
        applySearch(search)
    }

    public func applySearch(_ search: String) {
        self.search = search
        if search.isEmpty {
            // No search, keep filtered
        } else {
            filteredAffirmations = filteredAffirmations.filter {
                $0.text.localizedCaseInsensitiveContains(search)
            }
        }
    }

    public func progress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        // Mock progress info for analytics display
        return ProgressInfo(
            todayProgress: 0.7,
            cycleProgress: 0.4,
            currentDay: 3,
            totalDays: 7,
            currentRepetitions: 70,
            totalRepetitions: 100,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: true
        )
    }

    public func navigateToAffirmationDetail(_ affirmation: any AffirmationProtocol) {
        // Implement navigation logic (e.g., via coordinator or callback)
    }

    public func getAffirmationDetails(for affirmation: any AffirmationProtocol) async {
        // Implementation
    }

    public func getAffirmationStats(for affirmation: any AffirmationProtocol) async {
        // Implementation
    }
}


