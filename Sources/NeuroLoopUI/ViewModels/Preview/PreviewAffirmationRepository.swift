import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewAffirmationRepository: AffirmationRepositoryProtocol {
    private var affirmations: [AffirmationStub] = []

    public init() {
        // Initialize with some preview data
        affirmations = [
            AffirmationStub(
                text: "I am confident and capable",
                category: .confidence,
                recordingURL: nil,
                isFavorite: true,
                todayProgress: 0.0,  // Changed from 0.5 to 0.0 to start at 0 repetitions
                cycleProgress: 0.3
            ),
            AffirmationStub(
                text: "I embrace challenges with courage",
                category: .mindfulness,
                recordingURL: nil,
                isFavorite: false,
                todayProgress: 0.0,
                cycleProgress: 0.0
            ),
            AffirmationStub(
                text: "I am grateful for all the good in my life",
                category: .gratitude,
                recordingURL: nil,
                isFavorite: true,
                todayProgress: 0.0,  // Changed from 0.8 to 0.0 to start at 0 repetitions
                cycleProgress: 0.6
            ),
        ]
    }

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        affirmations
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        affirmations.first { $0.id == id }
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        let affirmation = AffirmationStub(
            text: text,
            category: category,
            recordingURL: recordingURL,
            isFavorite: false,
            todayProgress: 0.0,
            cycleProgress: 0.0
        )
        affirmations.append(affirmation)
        return affirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }),
            let stub = affirmation as? AffirmationStub
        {
            affirmations[index] = stub
        }
    }

    public func deleteAffirmation(id: UUID) async throws {
        affirmations.removeAll { $0.id == id }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        affirmations.filter { $0.category == category }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        affirmations.filter { $0.isFavorite }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        affirmations.first { $0.hasActiveCycle }
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let oldStub = affirmations[index]
            // Create a new stub with updated progress values
            let newStub = AffirmationStub(
                id: oldStub.id,
                text: oldStub.text,
                category: oldStub.category,
                recordingURL: oldStub.recordingURL,
                isFavorite: oldStub.isFavorite,
                todayProgress: min(oldStub.todayProgress + 0.1, 1.0),
                cycleProgress: min(oldStub.cycleProgress + 0.1, 1.0)
            )
            affirmations[index] = newStub
        }
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let oldStub = affirmations[index]
            // Create a new stub with cycle started
            let newStub = AffirmationStub(
                id: oldStub.id,
                text: oldStub.text,
                category: oldStub.category,
                recordingURL: oldStub.recordingURL,
                isFavorite: oldStub.isFavorite,
                todayProgress: oldStub.todayProgress,
                cycleProgress: 0.0
            )
            affirmations[index] = newStub
        }
    }
}
