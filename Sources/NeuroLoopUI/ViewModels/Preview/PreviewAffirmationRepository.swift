import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewAffirmationRepository: AffirmationRepositoryProtocol {
    private var affirmations: [AffirmationStub] = []

    public init() {
        // Initialize with some preview data
        affirmations = [
            AffirmationStub(
                text: "I am confident and capable",
                category: .confidence,
                recordingURL: nil,
                isFavorite: true,
                todayProgress: 0.0,  // Changed from 0.5 to 0.0 to start at 0 repetitions
                cycleProgress: 0.3
            ),
            AffirmationStub(
                text: "I embrace challenges with courage",
                category: .mindfulness,
                recordingURL: nil,
                isFavorite: false,
                todayProgress: 0.0,
                cycleProgress: 0.0
            ),
            AffirmationStub(
                text: "I am grateful for all the good in my life",
                category: .gratitude,
                recordingURL: nil,
                isFavorite: true,
                todayProgress: 0.0,  // Changed from 0.8 to 0.0 to start at 0 repetitions
                cycleProgress: 0.6
            ),
        ]
    }

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        affirmations
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        affirmations.first { $0.id == id }
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        let affirmation = AffirmationStub(
            text: text,
            category: category,
            recordingURL: recordingURL,
            isFavorite: false,
            todayProgress: 0.0,
            cycleProgress: 0.0
        )
        affirmations.append(affirmation)
        return affirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }),
            let stub = affirmation as? AffirmationStub
        {
            affirmations[index] = stub
        }
    }

    public func deleteAffirmation(id: UUID) async throws {
        affirmations.removeAll { $0.id == id }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        affirmations.filter { $0.category == category }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        affirmations.filter { $0.isFavorite }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        affirmations.first { $0.hasActiveCycle }
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let oldStub = affirmations[index]
            // Create a new stub with updated progress values
            let newStub = AffirmationStub(
                id: oldStub.id,
                text: oldStub.text,
                category: oldStub.category,
                recordingURL: oldStub.recordingURL,
                isFavorite: oldStub.isFavorite,
                todayProgress: min(oldStub.todayProgress + 0.1, 1.0),
                cycleProgress: min(oldStub.cycleProgress + 0.1, 1.0)
            )
            affirmations[index] = newStub
        }
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let oldStub = affirmations[index]
            // Create a new stub with cycle started
            let newStub = AffirmationStub(
                id: oldStub.id,
                text: oldStub.text,
                category: oldStub.category,
                recordingURL: oldStub.recordingURL,
                isFavorite: oldStub.isFavorite,
                todayProgress: oldStub.todayProgress,
                cycleProgress: 0.0
            )
            affirmations[index] = newStub
        }
    }

    // MARK: - Missing Protocol Methods

    public func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        try await updateAffirmation(affirmation)
    }

    public func updateProgress(for affirmationId: UUID, repetitions: Int, date: Date) async throws {
        // Preview implementation - just update the affirmation
    }

    public func getStatistics(for affirmationId: UUID) async throws -> AffirmationStatistics {
        return AffirmationStatistics()
    }

    public func searchAffirmations(query: String) async throws -> [any AffirmationProtocol] {
        return affirmations.filter { $0.text.localizedCaseInsensitiveContains(query) }
    }

    public func getAffirmations(sortedBy option: AffirmationSortOption) async throws -> [any AffirmationProtocol] {
        switch option {
        case .alphabetical:
            return affirmations.sorted { $0.text < $1.text }
        case .newest:
            return affirmations.sorted { $0.createdAt > $1.createdAt }
        case .oldest:
            return affirmations.sorted { $0.createdAt < $1.createdAt }
        case .favorite:
            return affirmations.sorted { $0.isFavorite && !$1.isFavorite }
        }
    }

    public func updateFavoriteStatus(for affirmationId: UUID, isFavorite: Bool) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmationId }) {
            let oldStub = affirmations[index]
            let newStub = AffirmationStub(
                id: oldStub.id,
                text: oldStub.text,
                category: oldStub.category,
                recordingURL: oldStub.recordingURL,
                isFavorite: isFavorite,
                todayProgress: oldStub.todayProgress,
                cycleProgress: oldStub.cycleProgress
            )
            affirmations[index] = newStub
        }
    }

    public func getDailyProgress(for affirmationId: UUID, date: Date) async throws -> Int {
        return 0 // Preview implementation
    }

    public func updateEnergyLevel(for affirmationId: UUID, level: Double) async throws {
        // Preview implementation
    }

    public func recordMood(for affirmationId: UUID, rating: Int, notes: String?) async throws {
        // Preview implementation
    }

    public func getAffirmationsWithActiveCycles() async throws -> [any AffirmationProtocol] {
        return affirmations.filter { $0.hasActiveCycle }
    }

    public func completeCycle(for affirmationId: UUID) async throws {
        // Preview implementation
    }

    public func startNewCycle(for affirmationId: UUID) async throws {
        // Preview implementation
    }

    public func getStreakInfo(for affirmationId: UUID) async throws -> (currentStreak: Int, longestStreak: Int) {
        return (currentStreak: 0, longestStreak: 0)
    }

    public func updateLongestStreak(for affirmationId: UUID, streak: Int) async throws {
        // Preview implementation
    }
}
