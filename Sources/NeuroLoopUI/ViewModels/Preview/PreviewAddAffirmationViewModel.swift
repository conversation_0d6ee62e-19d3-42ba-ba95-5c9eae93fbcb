import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
public final class PreviewAddAffirmationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published public var text: String = ""
    @Published public var selectedCategory: AffirmationCategory = .confidence
    @Published public var startImmediately: Bool = true
    @Published public private(set) var isSaving = false
    @Published public private(set) var error: Error?

    // MARK: - Properties

    private let repository: AffirmationRepositoryProtocol

    // MARK: - Computed Properties

    public var isValid: Bool {
        !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - Initialization

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    // MARK: - Public Methods

    @MainActor
    public func save() async {
        guard isValid else { return }

        isSaving = true
        error = nil

        do {
            _ = try await repository.createAffirmation(
                text: text.trimming<PERSON>haracters(in: .whitespacesAndNewlines),
                category: selectedCategory,
                recordingURL: nil
            )
        } catch {
            self.error = error
        }

        isSaving = false
    }
}

#if DEBUG
@available(iOS 17.0, macOS 14.0, *)
extension PreviewAddAffirmationViewModel {
    @MainActor
    static func preview() async -> PreviewAddAffirmationViewModel {
        let viewModel = PreviewAddAffirmationViewModel(repository: PreviewAffirmationRepository())
        viewModel.text = "I am confident and capable"
        viewModel.selectedCategory = .confidence
        viewModel.startImmediately = true
        return viewModel
    }
}
#endif
