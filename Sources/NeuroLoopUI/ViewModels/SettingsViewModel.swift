import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove this if not needed for other types
import NeuroLoopTypes
import SwiftUI

/// View model for managing app settings
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class SettingsViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published public var isDarkModeEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isDarkModeEnabled, forKey: "isDarkModeEnabled")
            // Theme update must be triggered by the UI layer after changing isDarkModeEnabled.
            // SettingsViewModel.dispatchDarkModeChanged(isDarkModeEnabled, themeManager: ThemeManager.shared)
        }
    }

    @Published public var notificationsEnabled: Bool {
        didSet {
            UserDefaults.standard.set(notificationsEnabled, forKey: "notificationsEnabled")
            SettingsViewModel.dispatchNotificationsChanged(notificationsEnabled)
        }
    }

    @Published public var dailyRemindersEnabled: Bool {
        didSet {
            UserDefaults.standard.set(dailyRemindersEnabled, forKey: "dailyRemindersEnabled")
            if dailyRemindersEnabled {
                scheduleReminders()
            } else {
                cancelReminders()
            }
        }
    }

    @Published public var reminderTime: Date {
        didSet {
            UserDefaults.standard.set(reminderTime, forKey: "reminderTime")
            if dailyRemindersEnabled {
                scheduleReminders()
            }
        }
    }

    @Published public var hapticFeedbackEnabled: Bool {
        didSet {
            UserDefaults.standard.set(hapticFeedbackEnabled, forKey: "hapticFeedbackEnabled")
            SettingsViewModel.dispatchHapticFeedbackChanged(hapticFeedbackEnabled)
        }
    }

    @Published public var soundEffectsEnabled: Bool {
        didSet {
            UserDefaults.standard.set(soundEffectsEnabled, forKey: "soundEffectsEnabled")
            SettingsViewModel.dispatchSoundEffectsChanged(soundEffectsEnabled)
        }
    }

    @Published public var dailyGoal: Int {
        didSet {
            UserDefaults.standard.set(dailyGoal, forKey: "dailyGoal")
            SettingsViewModel.dispatchDailyGoalChanged(dailyGoal)
        }
    }

    @Published public private(set) var isPremiumUser: Bool
    public var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }

    @Published public private(set) var syncStatus: SyncStatus = .idle
    @Published public private(set) var syncError: Error?
    @Published public private(set) var lastSyncDate: Date?

    @Published public var isLoading: Bool = false

    /// User-friendly sync error message for UI
    public var syncErrorMessage: String? {
        guard let error = syncError else { return nil }
        return "Sync failed: \(error.localizedDescription)"
    }

    // MARK: - Properties

    private let userDefaults: UserDefaults
    private let purchaseManager: PurchaseManagerSendable
    private let dataExportService: DataExportServiceProtocol
    private let themeManager: ThemeManagingSendable
    private let hapticManager: any HapticGenerating
    private var isHapticEnabled: Bool { hapticFeedbackEnabled }
    private var cancellables = Set<AnyCancellable>()
    public let syncService: SyncServiceProtocol?

    // MARK: - Initialization

    public init(
        userDefaults: UserDefaults,
        purchaseManager: PurchaseManagerSendable,
        dataExportService: DataExportServiceProtocol,
        themeManager: ThemeManagingSendable,
        hapticManager: any HapticGenerating,
        syncService: SyncServiceProtocol?
    ) {
        self.userDefaults = userDefaults
        self.purchaseManager = purchaseManager
        self.dataExportService = dataExportService
        self.themeManager = themeManager
        self.hapticManager = hapticManager
        self.syncService = syncService

        // Initialize all stored properties
        self.isDarkModeEnabled = userDefaults.bool(forKey: "isDarkModeEnabled")
        self.notificationsEnabled = userDefaults.bool(forKey: "notificationsEnabled")
        self.dailyRemindersEnabled = userDefaults.bool(forKey: "dailyRemindersEnabled")
        self.reminderTime =
            userDefaults.object(forKey: "reminderTime") as? Date ?? Calendar.current.date(
                from: DateComponents(hour: 9, minute: 0)) ?? Date()
        self.hapticFeedbackEnabled = userDefaults.bool(forKey: "hapticFeedbackEnabled")
        self.soundEffectsEnabled = userDefaults.bool(forKey: "soundEffectsEnabled")
        self.dailyGoal = userDefaults.integer(forKey: "dailyGoal")
        self.isPremiumUser = userDefaults.bool(forKey: "isPremiumUser")

        // NOTE: No Task or async work in initializer. Call setup() after init.
    }

    /// Call this method after initialization to set up subscriptions and async work.
    @MainActor
    public func setup() {
        if syncService != nil {
            setupSyncStatusSubscription()
        }
    }

    @MainActor
    private func setupSyncStatusSubscription() {
        guard let syncService = syncService else { return }
        syncService.syncStatusPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                guard let self = self else { return }
                let syncService = self.syncService
                Task { @MainActor in
                    self.handleSyncStatusUpdate(status: status, syncService: syncService)
                }
            }
            .store(in: &cancellables)
    }

    /// Dedicated @MainActor handler for sync status updates.
    /// Receives only the required values, not self.
    @MainActor
    private func handleSyncStatusUpdate(status: SyncStatus, syncService: SyncServiceProtocol?) {
        guard let syncService = syncService else { return }
        Task { @MainActor in
            self.syncStatus = status
            self.syncError = await syncService.lastSyncError
            self.lastSyncDate = await syncService.lastSyncDate
        }
    }

    // MARK: - Public Methods

    public func restoreDefaults() {
        isDarkModeEnabled = false
        notificationsEnabled = true
        dailyRemindersEnabled = false
        reminderTime = Calendar.current.date(from: DateComponents(hour: 9, minute: 0)) ?? Date()
        hapticFeedbackEnabled = true
        soundEffectsEnabled = true
        dailyGoal = 5
    }

    /// Call this method from the UI layer, passing a nonisolated purchaseManager.
    @MainActor
    public func purchasePremium(using purchaseManager: PurchaseManagerSendable) async throws {
        do {
            try await purchaseManager.purchasePremium()
            isPremiumUser = true
            userDefaults.set(true, forKey: "isPremiumUser")
        } catch {
            throw error
        }
    }

    /// Call this method from the UI layer, passing a nonisolated purchaseManager.
    @MainActor
    public func restorePurchases(using purchaseManager: PurchaseManagerSendable) async throws {
        do {
            try await purchaseManager.restorePurchases()
            isPremiumUser = userDefaults.bool(forKey: "isPremiumUser")
        } catch {
            throw error
        }
    }

    /// Call this method from the UI layer, passing a nonisolated dataExportService.
    @MainActor
    public func exportData(using dataExportService: DataExportServiceProtocol) async throws -> URL {
        do {
            return try await dataExportService.exportData()
        } catch {
            throw error
        }
    }

    /// Call this method from the UI layer, passing a nonisolated dataExportService.
    @MainActor
    public func deleteAllData(using dataExportService: DataExportServiceProtocol) async throws {
        do {
            try await dataExportService.deleteAllData()
            restoreDefaults()
        } catch {
            throw error
        }
    }

    @MainActor
    public func syncNow() async {
        guard isPremiumUser, let syncService = syncService else { return }
        do {
            try await syncService.syncNow()
        } catch {
            self.syncError = error
        }
    }

    public func exportData() {
        Task {
            do {
                _ = try await dataExportService.exportData()
                handleHapticFeedback()
            } catch {
                handleHapticError()
                // TODO: Show error alert
            }
        }
    }

    public func clearData() {
        Task {
            do {
                try await dataExportService.deleteAllData()
                handleHapticError()
            } catch {
                handleHapticError()
                // TODO: Show error alert
            }
        }
    }

    public func openPrivacyPolicy() {
        if let url = URL(string: "https://neuroloop.app/privacy") {
            #if os(iOS)
                UIApplication.shared.open(url)
            #else
                NSWorkspace.shared.open(url)
            #endif
        }
    }

    public func openTermsOfService() {
        if let url = URL(string: "https://neuroloop.app/terms") {
            #if os(iOS)
                UIApplication.shared.open(url)
            #else
                NSWorkspace.shared.open(url)
            #endif
        }
    }

    private func scheduleReminders() {
        // TODO: Implement reminder scheduling
    }

    private func cancelReminders() {
        // TODO: Implement reminder cancellation
    }

    private func handleHapticFeedback() {
        if hapticFeedbackEnabled {
            Task {
                await hapticManager.playSuccess()
            }
        }
    }

    private func handleHapticError() {
        if hapticFeedbackEnabled {
            Task {
                await hapticManager.playError()
            }
        }
    }

    private func handleHapticWarning() {
        if hapticFeedbackEnabled {
            Task {
                await hapticManager.playWarning()
            }
        }
    }

    private func handleHapticSelection() {
        if hapticFeedbackEnabled {
            Task {
                await hapticManager.playSelection()
            }
        }
    }

    // MARK: - Static Dispatchers for Property Observer Side Effects
    /// These static helpers ensure no actor-isolated state is sent across concurrency boundaries.
    /// They should be used in property observers to avoid capturing 'self'.
    /// Call this method from the UI layer, passing a nonisolated ThemeManager.
    static func dispatchDarkModeChanged(_ isDark: Bool, themeManager: ThemeManagingSendable) {
        Task { @MainActor in
            themeManager.setTheme(isDark ? NeuroLoopTypes.Theme.dark : NeuroLoopTypes.Theme.light)
        }
    }
    static func dispatchNotificationsChanged(_ enabled: Bool) {
        Task { @MainActor in
            // Add notification scheduling/cancellation logic here if needed
        }
    }
    static func dispatchReminderTimeChanged(_ time: Date) {
        Task { @MainActor in
            // Add reminder scheduling logic here if needed
        }
    }
    static func dispatchHapticFeedbackChanged(_ enabled: Bool) {
        Task { @MainActor in
            // Add haptic feedback logic here if needed
        }
    }
    static func dispatchSoundEffectsChanged(_ enabled: Bool) {
        Task { @MainActor in
            // Add sound effects logic here if needed
        }
    }
    static func dispatchDailyGoalChanged(_ goal: Int) {
        Task { @MainActor in
            // Add daily goal update logic here if needed
        }
    }

    /**
     Concurrency Pattern Documentation:
     - All async methods that require dependencies (e.g., purchaseManager, dataExportService, themeManager) must receive them as parameters from the UI layer or coordinator.
     - This ensures no actor-isolated state is sent across concurrency boundaries, complying with Swift 6.1's strict actor isolation rules.
     - The ViewModel itself should not capture or send self or its properties into async contexts.
     - The UI layer is responsible for providing nonisolated references (e.g., singletons or stateless objects) to these methods.
    */

    // Public getter for dataExportService (from NeuroLoopCore version)
    public var exportService: DataExportServiceProtocol { dataExportService }

    public func loadSettings() {
        // Load settings from UserDefaults
        isDarkModeEnabled = userDefaults.bool(forKey: "isDarkModeEnabled")
        notificationsEnabled = userDefaults.bool(forKey: "notificationsEnabled")
        dailyRemindersEnabled = userDefaults.bool(forKey: "dailyRemindersEnabled")
        reminderTime =
            userDefaults.object(forKey: "reminderTime") as? Date ?? Calendar.current.date(
                from: DateComponents(hour: 9, minute: 0)) ?? Date()
        hapticFeedbackEnabled = userDefaults.bool(forKey: "hapticFeedbackEnabled")
        soundEffectsEnabled = userDefaults.bool(forKey: "soundEffectsEnabled")
        dailyGoal = userDefaults.integer(forKey: "dailyGoal")

        // Check premium status
        isPremiumUser = userDefaults.bool(forKey: "isPremiumUser")
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    extension SettingsViewModel {
        @MainActor
        static func preview() async -> SettingsViewModel {
            let viewModel = SettingsViewModel(
                userDefaults: .standard,
                purchaseManager: PurchaseManager.shared,
                dataExportService: ServiceFactory.shared.getDataExportService(),
                themeManager: ThemeManager.shared,
                hapticManager: HapticManager.shared,
                syncService: ServiceFactory.shared.getSyncService()
            )
            return viewModel
        }
    }
#endif
