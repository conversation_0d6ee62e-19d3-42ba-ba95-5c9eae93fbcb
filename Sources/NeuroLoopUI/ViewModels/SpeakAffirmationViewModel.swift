import AVFoundation
import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import Speech
import Swift<PERSON>

/**
 * SpeakAffirmationViewModel
 *
 * The main view model for SpeakAffirmationView that handles speech recognition,
 * audio recording, and affirmation verification. This version includes:
 *
 * 1. Proper Debug Mode that bypasses speech recognition
 * 2. Accurate repetition counter increments
 * 3. Audio level detection and display
 * 4. Auto-stop functionality for modern UX
 * 5. Enhanced error handling and user feedback
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class SpeakAffirmationViewModel: ObservableObject {

    // MARK: - Published Properties

    @Published public var isRecording = false
    @Published public var isPlaying = false
    @Published public var spokenText = ""
    @Published public var partialRecognitionText = ""
    @Published public var audioLevel: Float = 0.0
    @Published public var currentAudioLevel: Double = 0.0
    @Published public var alertItem: AlertItem?
    @Published public var debugBypassSpeechRecognition = false
    @Published public var autoStopEnabled = true

    // Progress tracking properties
    @Published public var todayRepetitions: Int = 0
    @Published public var totalRepetitions: Int = 100
    @Published public var currentDay: Int = 1

    // MARK: - Private Properties

    public var affirmation: any AffirmationProtocol
    private var repetitionService: any RepetitionServiceProtocol
    public let audioService: any AudioRecordingServiceProtocol
    private let affirmationService: any AffirmationServiceProtocol
    private let streakService: any StreakServiceProtocol

    // Public getter to check service type for debugging
    public var repetitionServiceType: String {
        return String(describing: type(of: repetitionService))
    }
    private var cancellables = Set<AnyCancellable>()

    // Speech recognition
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?

    // Audio monitoring
    private var audioLevelTimer: Timer?
    private var isStoppingRecording = false

    // Auto-stop debouncing
    private var autoStopTask: Task<Void, Never>?

    // CRITICAL FIX: Flag to prevent duplicate repetition recording in the same session
    private var hasRecordedRepetitionThisSession = false

    // Reference to the tracking view model for syncing repetition counts
    public var trackingViewModel: NeuroLoopCore.RepetitionTrackingViewModel?

    // Computed property to get the repetition count from the tracking view model
    public var todayRepetitionsFromTracking: Int {
        return trackingViewModel?.todayRepetitions ?? affirmation.currentRepetitions
    }

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        audioService: any AudioRecordingServiceProtocol,
        affirmationService: any AffirmationServiceProtocol,
        streakService: any StreakServiceProtocol
    ) {
        self.affirmation = affirmation
        self.repetitionService = repetitionService
        self.audioService = audioService
        self.affirmationService = affirmationService
        self.streakService = streakService

        // Print service types for debugging
        print("SpeakAffirmationViewModel: Initialized with services:")
        print("- Repetition Service: \(type(of: repetitionService))")
        print("- Audio Service: \(type(of: audioService))")
        print("- Affirmation Service: \(type(of: affirmationService))")
        print("- Streak Service: \(type(of: streakService))")

        // Initialize progress
        let progressInfo = repetitionService.getProgress(for: affirmation)
        self.todayRepetitions = affirmation.currentRepetitions
        self.totalRepetitions = progressInfo.totalRepetitions
        self.currentDay = progressInfo.currentDay

        print("SpeakAffirmationViewModel: Initial progress - \(todayRepetitions)/\(totalRepetitions), Day \(currentDay)")

        // Set up audio level monitoring
        setupAudioLevelMonitoring()
    }

    // MARK: - Public Methods

    /// Initializes the view model and prepares for recording
    public func initialize() async {
        print("SpeakAffirmationViewModel: initialize called")

        // Load current progress
        await loadProgress()

        // Pre-warm speech recognition
        await prewarmSpeechRecognition()
    }

    /// Cleans up resources when the view model is no longer needed
    public func cleanup() async {
        print("SpeakAffirmationViewModel: cleanup called")

        // Stop any ongoing recording
        if isRecording {
            print("SpeakAffirmationViewModel: Stopping recording during cleanup")
            await stopRecording()
        }

        // Stop audio monitoring
        stopMonitoringAudioLevels()
        print("SpeakAffirmationViewModel: Stopped audio monitoring")

        // Stop speech recognition if active
        await stopSpeechRecognition()
        print("SpeakAffirmationViewModel: Stopped speech recognition")

        // Clean up any other resources
        print("SpeakAffirmationViewModel: Cleanup complete")
    }

    /// Starts recording the user's spoken affirmation
    public func startRecording() {
        print("SpeakAffirmationViewModel: startRecording called")

        Task {
            do {
                // Clear previous spoken text
                spokenText = ""
                partialRecognitionText = ""

                // CRITICAL FIX: Reset the repetition recording flag for new session
                hasRecordedRepetitionThisSession = false
                print("🎙️ RECORDING START: Reset hasRecordedRepetitionThisSession to false")

                // Start recording
                try await audioService.startRecording()
                isRecording = true

                // Start monitoring audio levels
                startMonitoringAudioLevels()

                // Start speech recognition if not in debug mode
                if !debugBypassSpeechRecognition {
                    await startSpeechRecognition()
                } else {
                    print("SpeakAffirmationViewModel: Debug mode enabled, skipping speech recognition")
                }
            } catch {
                print("SpeakAffirmationViewModel: Error starting recording: \(error.localizedDescription)")
                isRecording = false
                setAlert(AlertItem(
                    title: "Recording Error",
                    message: "Could not start recording: \(error.localizedDescription)"
                ))
            }
        }
    }

    /// Stops recording and processes the spoken affirmation
    public func stopRecording() async {
        print("SpeakAffirmationViewModel: stopRecording called")

        // Prevent multiple rapid calls
        guard !isStoppingRecording else {
            print("SpeakAffirmationViewModel: Already stopping recording, ignoring call")
            return
        }

        isStoppingRecording = true
        defer { isStoppingRecording = false }

        print("SpeakAffirmationViewModel: Current state - isRecording: \(isRecording), debugMode: \(debugBypassSpeechRecognition)")
        print("SpeakAffirmationViewModel: Spoken text: \"\(spokenText)\"")
        print("SpeakAffirmationViewModel: Target text: \"\(affirmation.text)\"")

        // Add a small delay to ensure all speech is captured
        print("SpeakAffirmationViewModel: Adding 0.8 second delay before stopping recording to ensure all speech is captured")
        try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 second delay

        // Capture the current spoken text in case it changes during async operations
        let capturedSpokenText = spokenText
        print("SpeakAffirmationViewModel: Captured spoken text: \"\(capturedSpokenText)\"")

        // Stop speech recognition
        await stopSpeechRecognition()

        // Stop audio recording
        do {
            // Try to stop recording, but handle errors gracefully
            let result = try await audioService.stopRecording()
            print("SpeakAffirmationViewModel: Recording stopped successfully, result: \(result)")

            // Update UI
            isRecording = false

            // In debug mode, always count the repetition
            if debugBypassSpeechRecognition {
                print("SpeakAffirmationViewModel: Debug mode enabled, automatically counting repetition")
                // Add a small delay before recording repetition to ensure UI is ready
                try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                print("SpeakAffirmationViewModel: Delay complete, now recording repetition")
                await recordRepetition()
            } else if !capturedSpokenText.isEmpty {
                // Verify the spoken text matches the affirmation
                print("SpeakAffirmationViewModel: Verifying spoken text against target affirmation")
                let verificationResult = verifyAffirmation(
                    spokenText: capturedSpokenText,
                    targetText: affirmation.text
                )

                if verificationResult.success {
                    print("🎯 SPEECH SUCCESS: Verification successful! Similarity: \(verificationResult.similarity * 100)%")
                    print("🎯 SPEECH SUCCESS: Spoken: '\(capturedSpokenText)'")
                    print("🎯 SPEECH SUCCESS: Target: '\(affirmation.text)'")
                    print("🎯 SPEECH SUCCESS: About to record repetition...")

                    // Add a small delay before recording repetition to ensure UI is ready
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    print("🎯 SPEECH SUCCESS: Delay complete, now calling recordRepetition()")
                    await recordRepetition()
                    print("🎯 SPEECH SUCCESS: recordRepetition() call completed")
                } else {
                    print("SpeakAffirmationViewModel: Verification failed, not counting repetition")
                    print("SpeakAffirmationViewModel: Similarity: \(verificationResult.similarity * 100)%")

                    // Show a more helpful error message with the similarity percentage
                    let similarityPercent = Int(verificationResult.similarity * 100)
                    setAlert(AlertItem(
                        title: "Not Quite Right",
                        message: "Your spoken text was \(similarityPercent)% similar to the affirmation. Please try again and speak the complete affirmation clearly."
                    ))
                }
            } else {
                print("SpeakAffirmationViewModel: No spoken text detected")

                // Check if we're in simulator or speech recognition failed
                #if targetEnvironment(simulator)
                print("SpeakAffirmationViewModel: Running in simulator - showing helpful message")
                setAlert(AlertItem(
                    title: "Simulator Mode",
                    message: "Speech recognition is not available in the simulator. Enable Testing Mode to bypass speech verification."
                ))
                #else
                setAlert(AlertItem(
                    title: "No Speech Detected",
                    message: "Please speak the affirmation clearly when recording."
                ))
                #endif
            }
        } catch {
            print("SpeakAffirmationViewModel: Error stopping recording: \(error.localizedDescription)")

            // Update UI state
            isRecording = false

            // In debug mode, still count the repetition even if there was an error
            if debugBypassSpeechRecognition {
                print("SpeakAffirmationViewModel: Debug mode enabled, counting repetition despite error")
                Task {
                    // Add a small delay before recording repetition
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    await recordRepetition()
                }
            }
        }
    }

    /// Plays the recorded affirmation
    public func playRecording() async {
        print("SpeakAffirmationViewModel: playRecording called")

        do {
            isPlaying = true
            try await audioService.startPlayback()
            isPlaying = false
        } catch {
            print("SpeakAffirmationViewModel: Error playing recording: \(error.localizedDescription)")
            isPlaying = false
            setAlert(AlertItem(
                title: "Playback Error",
                message: "Could not play recording: \(error.localizedDescription)"
            ))
        }
    }

    /// Resets the repetition count for debugging purposes
    public func resetRepetitionCount() async {
        print("SpeakAffirmationViewModel: resetRepetitionCount called")

        // Reset the tracking view model if available
        if let trackingVM = trackingViewModel {
            await trackingVM.resetTodayRepetitions()

            // Update local state
            todayRepetitions = 0

            // Force UI update
            await MainActor.run {
                objectWillChange.send()
            }
        } else {
            print("SpeakAffirmationViewModel: No tracking view model available to reset")
        }
    }

    /// Forces an increment of the repetition count for debugging purposes
    public func forceIncrementRepetition() async {
        print("SpeakAffirmationViewModel: forceIncrementRepetition called")
        await recordRepetition()
    }

    // MARK: - Private Methods

    /// Records a repetition for the current affirmation
    private func recordRepetition() async {
        print("⭐️ RECORDING REPETITION: Starting process")
        print("⭐️ Current affirmation: \"\(affirmation.text)\"")
        print("⭐️ Affirmation ID: \(affirmation.id)")

        // Log current recording state for debugging
        print("⭐️ Current isRecording state: \(isRecording)")
        print("⭐️ Current hasRecordedRepetitionThisSession: \(hasRecordedRepetitionThisSession)")

        // CRITICAL FIX: Prevent duplicate repetition recording
        // Use a flag to ensure we only record one repetition per recording session
        guard !hasRecordedRepetitionThisSession else {
            print("⭐️ Already recorded repetition for this session, ignoring duplicate call")
            return
        }

        // Mark that we're recording a repetition for this session
        hasRecordedRepetitionThisSession = true

        // Use the tracking view model to perform the repetition
        if let trackingVM = trackingViewModel {
            print("⭐️ Using tracking view model to perform repetition")
            print("⭐️ Current tracking count BEFORE: \(trackingVM.todayRepetitions)")

            await trackingVM.performRepetition()

            print("⭐️ Current tracking count AFTER: \(trackingVM.todayRepetitions)")

            // Update the affirmation from the tracking view model
            if let updatedAffirmation = trackingVM.affirmation {
                affirmation = updatedAffirmation
                print("⭐️ Updated affirmation from tracking view model")
                print("⭐️ Updated affirmation count: \(updatedAffirmation.currentRepetitions)")
            }

            // Force UI update
            await MainActor.run {
                objectWillChange.send()
                print("⭐️ Sent objectWillChange notification")
            }

            // Success notification is now handled by the view's onChange handler
            await MainActor.run {
                let currentCount = trackingVM.todayRepetitions
                print("🎉 SUCCESS: Repetition recorded successfully")
                print("🎉 New count: \(currentCount) out of \(totalRepetitions)")
                print("🎉 Notification will be shown by view's onChange handler")
                print("🎉 trackingVM.todayRepetitions should trigger onChange in view")
            }
        } else {
            print("❌ ERROR: No tracking view model available")
            await MainActor.run {
                setAlert(AlertItem(
                    title: "Error",
                    message: "Unable to record repetition. Please try again."
                ))
            }
        }
    }

    /// Loads the current progress for the affirmation
    private func loadProgress() async {
        print("SpeakAffirmationViewModel: loadProgress called")

        // Get the progress info
        let progressInfo = repetitionService.getProgress(for: affirmation)

        // Update the UI
        totalRepetitions = progressInfo.totalRepetitions
        currentDay = progressInfo.currentDay

        // Update the tracking view model if available
        if let trackingVM = trackingViewModel {
            // Load data in the tracking view model to ensure it has the latest state
            await trackingVM.loadData(for: affirmation.id)
            print("SpeakAffirmationViewModel: Called loadData on trackingViewModel")
        } else {
            print("SpeakAffirmationViewModel: No trackingViewModel available to update")
        }

        // Force UI update
        objectWillChange.send()
    }

    private func prewarmSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Pre-warming speech recognition")

        // Request microphone permission first
        let micPermissionGranted = await AudioSessionManager.shared.requestMicrophonePermission()
        if !micPermissionGranted {
            print("SpeakAffirmationViewModel: Microphone permission denied during prewarm")
        }

        // Request speech recognition authorization in advance
        if SFSpeechRecognizer.authorizationStatus() == .notDetermined {
            let speechPermissionGranted = await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { status in
                    continuation.resume(returning: status == .authorized)
                }
            }
            if !speechPermissionGranted {
                print("SpeakAffirmationViewModel: Speech recognition permission denied during prewarm")
            }
        }

        // Initialize the speech recognizer if needed
        if speechRecognizer == nil {
            print("SpeakAffirmationViewModel: Initializing speech recognizer...")
            print("SpeakAffirmationViewModel: Current device locale: \(Locale.current.identifier)")
            print("SpeakAffirmationViewModel: Current device language: \(Locale.current.language.languageCode?.identifier ?? "unknown")")
            print("SpeakAffirmationViewModel: Current device region: \(Locale.current.region?.identifier ?? "unknown")")

            // Try multiple locale configurations
            let localeOptions = [
                Locale.current,
                Locale(identifier: "en-US"),
                Locale(identifier: "en-GB"),
                Locale(identifier: "en"),
                Locale(identifier: "en_US")
            ]

            for locale in localeOptions {
                print("SpeakAffirmationViewModel: Trying locale: \(locale.identifier)")
                let recognizer = SFSpeechRecognizer(locale: locale)
                print("SpeakAffirmationViewModel: Recognizer created: \(recognizer != nil)")
                print("SpeakAffirmationViewModel: Recognizer available: \(recognizer?.isAvailable ?? false)")

                if let recognizer = recognizer, recognizer.isAvailable {
                    speechRecognizer = recognizer
                    print("SpeakAffirmationViewModel: Successfully initialized with locale: \(locale.identifier)")
                    break
                }
            }

            // If still nil, try the default initializer
            if speechRecognizer == nil {
                print("SpeakAffirmationViewModel: All locales failed, trying default initializer")
                speechRecognizer = SFSpeechRecognizer()
                print("SpeakAffirmationViewModel: Default recognizer created: \(speechRecognizer != nil)")
                print("SpeakAffirmationViewModel: Default recognizer available: \(speechRecognizer?.isAvailable ?? false)")
            }

            print("SpeakAffirmationViewModel: Final speech recognizer: \(speechRecognizer?.description ?? "nil")")
            print("SpeakAffirmationViewModel: Final locale: \(speechRecognizer?.locale.identifier ?? "unknown")")
            print("SpeakAffirmationViewModel: Final availability: \(speechRecognizer?.isAvailable ?? false)")
        }

        // Pre-configure audio session (inactive for now)
        _ = await configureAudioSession(active: false)
    }

    private func setupAudioLevelMonitoring() {
        // Set up audio level monitoring from the audio service
        audioService.recordingPowerPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] level in
                self?.audioLevel = Float(level)
                self?.currentAudioLevel = level
            }
            .store(in: &cancellables)
    }

    private func startMonitoringAudioLevels() {
        // Audio level monitoring is handled by the audio service
        print("SpeakAffirmationViewModel: Audio level monitoring started")
    }

    private func stopMonitoringAudioLevels() {
        // Audio level monitoring is handled by the audio service
        print("SpeakAffirmationViewModel: Audio level monitoring stopped")
    }

    private func setAlert(_ alertItem: AlertItem) {
        self.alertItem = alertItem
    }

    // MARK: - Speech Recognition Methods

    private func startSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Starting speech recognition")

        // Check if we're running in simulator
        #if targetEnvironment(simulator)
        print("SpeakAffirmationViewModel: Running in simulator - speech recognition not available")
        await MainActor.run {
            partialRecognitionText = "Speech recognition not available in simulator"
        }
        return
        #endif

        // Check and request microphone permission first
        let micPermission = await AudioSessionManager.shared.checkMicrophonePermission()
        if !micPermission {
            print("SpeakAffirmationViewModel: Microphone permission not granted, requesting...")
            let granted = await AudioSessionManager.shared.requestMicrophonePermission()
            if !granted {
                print("SpeakAffirmationViewModel: Microphone permission denied by user")
                await MainActor.run {
                    partialRecognitionText = "Microphone permission required - check Settings"
                }
                return
            }
            print("SpeakAffirmationViewModel: Microphone permission granted!")
        }

        // Check and request speech recognition permission
        let speechAuthStatus = SFSpeechRecognizer.authorizationStatus()
        if speechAuthStatus != .authorized {
            print("SpeakAffirmationViewModel: Speech recognition not authorized (status: \(speechAuthStatus.rawValue))")

            // Request permission if not determined
            if speechAuthStatus == .notDetermined {
                print("SpeakAffirmationViewModel: Requesting speech recognition permission...")
                let granted = await withCheckedContinuation { continuation in
                    SFSpeechRecognizer.requestAuthorization { status in
                        continuation.resume(returning: status == .authorized)
                    }
                }

                if !granted {
                    print("SpeakAffirmationViewModel: Speech recognition permission denied by user")
                    await MainActor.run {
                        partialRecognitionText = "Speech recognition permission denied"
                    }
                    return
                }
                print("SpeakAffirmationViewModel: Speech recognition permission granted!")
            } else {
                // Permission was previously denied or restricted
                await MainActor.run {
                    partialRecognitionText = "Speech recognition permission required - check Settings"
                }
                return
            }
        }

        // Configure audio session for recording
        let audioSessionConfigured = await configureAudioSession(active: true)
        guard audioSessionConfigured else {
            print("SpeakAffirmationViewModel: Failed to configure audio session")
            await MainActor.run {
                partialRecognitionText = "Audio session configuration failed"
            }
            return
        }

        // Ensure speech recognizer is initialized
        if speechRecognizer == nil {
            print("SpeakAffirmationViewModel: Speech recognizer not initialized, initializing now...")
            // Try to create a speech recognizer for the current locale first
            speechRecognizer = SFSpeechRecognizer(locale: Locale.current)

            // If that fails, fall back to English (US)
            if speechRecognizer == nil || speechRecognizer?.isAvailable == false {
                print("SpeakAffirmationViewModel: Current locale not supported, falling back to en-US")
                speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
            }

            // If still nil, try the default initializer
            if speechRecognizer == nil {
                print("SpeakAffirmationViewModel: en-US not available, using default locale")
                speechRecognizer = SFSpeechRecognizer()
            }
        }

        // Set up speech recognition
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("SpeakAffirmationViewModel: Speech recognizer not available")
            print("SpeakAffirmationViewModel: Speech recognizer: \(speechRecognizer?.description ?? "nil")")
            print("SpeakAffirmationViewModel: Is available: \(speechRecognizer?.isAvailable ?? false)")
            print("SpeakAffirmationViewModel: Locale: \(speechRecognizer?.locale.identifier ?? "unknown")")
            await MainActor.run {
                partialRecognitionText = "Speech recognizer not available"
            }
            return
        }

        print("SpeakAffirmationViewModel: Speech recognizer ready - Locale: \(speechRecognizer.locale.identifier), Available: \(speechRecognizer.isAvailable)")

        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("SpeakAffirmationViewModel: Failed to create recognition request")
            await MainActor.run {
                partialRecognitionText = "Failed to create recognition request"
            }
            return
        }

        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.taskHint = .confirmation
        recognitionRequest.contextualStrings = [affirmation.text]

        // Add additional debugging for speech recognition request
        print("SpeakAffirmationViewModel: Recognition request configured:")
        print("SpeakAffirmationViewModel: - shouldReportPartialResults: \(recognitionRequest.shouldReportPartialResults)")
        print("SpeakAffirmationViewModel: - taskHint: \(recognitionRequest.taskHint.rawValue)")
        print("SpeakAffirmationViewModel: - contextualStrings: \(recognitionRequest.contextualStrings)")

        // Try to enable on-device recognition for better reliability
        recognitionRequest.requiresOnDeviceRecognition = false // Start with server-based, fallback to on-device if needed
        print("SpeakAffirmationViewModel: - requiresOnDeviceRecognition: \(recognitionRequest.requiresOnDeviceRecognition)")

        // CRITICAL FIX: Set up audio engine to feed audio data to speech recognition
        let audioEngine = AVAudioEngine()

        // IMPORTANT: Don't access inputNode until we're ready to start the engine
        // Store audio engine reference first
        self.audioEngine = audioEngine

        print("SpeakAffirmationViewModel: Setting up audio engine for speech recognition")

        // Start audio engine first, then access input node
        do {
            try audioEngine.start()
            print("SpeakAffirmationViewModel: Audio engine started successfully")

            // Now it's safe to access the input node
            let inputNode = audioEngine.inputNode
            let recordingFormat = inputNode.outputFormat(forBus: 0)

            print("SpeakAffirmationViewModel: Audio format: \(recordingFormat)")

            // Install tap on audio engine to feed data to speech recognition
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }

        } catch {
            print("SpeakAffirmationViewModel: Failed to start audio engine: \(error.localizedDescription)")
            await MainActor.run {
                partialRecognitionText = "Failed to start audio engine: \(error.localizedDescription)"
            }
            return
        }

        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }

            Task { @MainActor in
                if let result = result {
                    let recognizedText = result.bestTranscription.formattedString
                    self.partialRecognitionText = recognizedText
                    print("SpeakAffirmationViewModel: Partial recognition: \"\(recognizedText)\"")

                    // MODERN AUTO-STOP: Check for matches on both partial AND final results
                    if self.autoStopEnabled && !recognizedText.isEmpty {
                        let verificationResult = self.verifyAffirmation(
                            spokenText: recognizedText,
                            targetText: self.affirmation.text
                        )

                        // Auto-stop when similarity is high enough (modern UX)
                        if verificationResult.success {
                            print("SpeakAffirmationViewModel: Auto-stop condition met - text matches! (Similarity: \(verificationResult.similarity * 100)%)")

                            // CRITICAL FIX: Prevent auto-stop if we've already recorded a repetition
                            guard !self.hasRecordedRepetitionThisSession else {
                                print("SpeakAffirmationViewModel: Auto-stop blocked - repetition already recorded this session")
                                return
                            }

                            // Cancel any existing auto-stop task
                            self.autoStopTask?.cancel()

                            // Create a new debounced auto-stop task
                            self.autoStopTask = Task {
                                // Wait a short moment to ensure the user has finished speaking
                                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 second delay

                                // Check if task was cancelled (user continued speaking)
                                guard !Task.isCancelled else {
                                    print("SpeakAffirmationViewModel: Auto-stop cancelled - user continued speaking")
                                    return
                                }

                                // CRITICAL FIX: Double-check that we haven't already recorded a repetition
                                guard !self.hasRecordedRepetitionThisSession else {
                                    print("SpeakAffirmationViewModel: Auto-stop cancelled - repetition already recorded during delay")
                                    return
                                }

                                // Proceed with auto-stop
                                await MainActor.run {
                                    print("SpeakAffirmationViewModel: Auto-stop triggered after delay!")
                                    self.spokenText = recognizedText
                                }

                                await self.stopRecording()
                            }
                            return // Exit early to prevent further processing
                        } else {
                            // Cancel auto-stop if similarity drops below threshold
                            self.autoStopTask?.cancel()
                        }
                    }

                    // Handle final results (for cases where auto-stop didn't trigger)
                    if result.isFinal {
                        self.spokenText = recognizedText
                        print("SpeakAffirmationViewModel: Final recognition: \"\(recognizedText)\"")

                        // If auto-stop is disabled, we still need to stop manually
                        if !self.autoStopEnabled {
                            print("SpeakAffirmationViewModel: Auto-stop disabled, waiting for manual stop")
                        }
                    }
                }

                if let error = error {
                    print("SpeakAffirmationViewModel: Speech recognition error: \(error.localizedDescription)")
                    self.partialRecognitionText = "Speech recognition error: \(error.localizedDescription)"
                }
            }
        }

        print("SpeakAffirmationViewModel: Speech recognition task started with audio engine")
    }

    private func stopSpeechRecognition() async {
        print("SpeakAffirmationViewModel: Stopping speech recognition")

        // Cancel any pending auto-stop task
        autoStopTask?.cancel()
        autoStopTask = nil

        // Stop and clean up audio engine
        if let audioEngine = audioEngine {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
            print("SpeakAffirmationViewModel: Audio engine stopped and tap removed")
        }

        // Finish the recognition request
        recognitionRequest?.endAudio()

        // Cancel the recognition task
        recognitionTask?.cancel()
        recognitionTask = nil
        recognitionRequest = nil
        audioEngine = nil

        // Deactivate audio session
        _ = await configureAudioSession(active: false)

        print("SpeakAffirmationViewModel: Speech recognition stopped and cleaned up")
    }

    private func configureAudioSession(active: Bool) async -> Bool {
        do {
            let audioSession = AVAudioSession.sharedInstance()

            if active {
                // CRITICAL FIX: Use consistent audio session configuration
                // Match the AudioRecordingService configuration to avoid conflicts
                try audioSession.setCategory(.playAndRecord, mode: .spokenAudio, options: [.defaultToSpeaker, .allowBluetooth])
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                print("SpeakAffirmationViewModel: Audio session activated successfully")
            } else {
                try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
                print("SpeakAffirmationViewModel: Audio session deactivated successfully")
            }

            return true
        } catch {
            print("SpeakAffirmationViewModel: Audio session configuration error: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - Verification Methods

    private func verifyAffirmation(spokenText: String, targetText: String) -> (success: Bool, similarity: Double) {
        let similarity = calculateSimilarity(spokenText: spokenText, targetText: targetText)
        let threshold = 0.7 // 70% similarity threshold

        print("SpeakAffirmationViewModel: Verification - Similarity: \(similarity * 100)%, Threshold: \(threshold * 100)%")

        return (success: similarity >= threshold, similarity: similarity)
    }

    private func calculateSimilarity(spokenText: String, targetText: String) -> Double {
        let spokenWords = spokenText.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        let targetWords = targetText.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }

        guard !targetWords.isEmpty else { return 0.0 }

        let matchingWords = spokenWords.filter { targetWords.contains($0) }
        return Double(matchingWords.count) / Double(targetWords.count)
    }
}
