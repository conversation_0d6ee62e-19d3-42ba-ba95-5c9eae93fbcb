import Foundation
import SwiftUI
import NeuroLoopTypes
import NeuroLoopInterfaces

/// View model for managing journal functionality
@MainActor
@available(iOS 17.0, macOS 14.0, *)
public class JournalViewModel: ObservableObject {
    // MARK: - Published Properties
    
    @Published public var currentEntry: JournalEntry?
    @Published public var recentEntries: [JournalEntry] = []
    @Published public var moodStatistics: MoodStatistics?
    @Published public var isLoading = false
    @Published public var errorMessage: String?
    @Published public var showingMoodPicker = false
    @Published public var selectedDate = Date()
    
    // MARK: - Private Properties
    
    private let repository: JournalRepositoryProtocol
    private let calendar = Calendar.current
    
    // MARK: - Initializer
    
    public init(repository: JournalRepositoryProtocol) {
        self.repository = repository
        Task {
            await loadTodaysEntry()
            await loadRecentEntries()
            await loadMoodStatistics()
        }
    }
    
    // MARK: - Public Methods
    
    /// Load journal entry for today
    public func loadTodaysEntry() async {
        isLoading = true
        errorMessage = nil
        
        do {
            currentEntry = try await repository.getEntry(for: Date())
            if currentEntry == nil {
                // Create a new entry for today
                currentEntry = JournalEntry(date: Date())
            }
        } catch {
            errorMessage = "Failed to load today's entry: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// Load journal entry for selected date
    public func loadEntry(for date: Date) async {
        isLoading = true
        errorMessage = nil
        selectedDate = date
        
        do {
            currentEntry = try await repository.getEntry(for: date)
            if currentEntry == nil {
                // Create a new entry for the selected date
                currentEntry = JournalEntry(date: date)
            }
        } catch {
            errorMessage = "Failed to load entry: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// Load recent journal entries
    public func loadRecentEntries() async {
        do {
            recentEntries = try await repository.getRecentEntries(days: 30)
        } catch {
            errorMessage = "Failed to load recent entries: \(error.localizedDescription)"
        }
    }
    
    /// Load mood statistics
    public func loadMoodStatistics() async {
        let startDate = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        
        do {
            moodStatistics = try await repository.getMoodStatistics(from: startDate, to: Date())
        } catch {
            errorMessage = "Failed to load mood statistics: \(error.localizedDescription)"
        }
    }
    
    /// Save current journal entry
    public func saveEntry() async {
        guard let entry = currentEntry else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Update timestamp
            entry.updatedAt = Date()
            
            // Check if entry exists
            let existingEntry = try await repository.getEntry(for: entry.date)
            
            if existingEntry != nil {
                try await repository.updateEntry(entry)
            } else {
                try await repository.createEntry(entry)
            }
            
            // Reload data
            await loadRecentEntries()
            await loadMoodStatistics()
            
        } catch {
            errorMessage = "Failed to save entry: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// Update mood for current entry
    public func updateMood(_ mood: MoodLevel) async {
        guard let entry = currentEntry else { return }
        
        entry.mood = mood
        await saveEntry()
    }
    
    /// Update thoughts for current entry
    public func updateThoughts(_ thoughts: String) async {
        guard let entry = currentEntry else { return }
        
        entry.thoughts = thoughts
        await saveEntry()
    }
    
    /// Connect affirmation session to current entry
    public func connectAffirmationSession(_ sessionId: UUID) async {
        guard let entry = currentEntry else { return }
        
        do {
            try await repository.connectAffirmationSession(sessionId: sessionId, to: entry.id)
            
            // Update local entry
            if !entry.affirmationSessionIds.contains(sessionId) {
                entry.affirmationSessionIds.append(sessionId)
            }
            
        } catch {
            errorMessage = "Failed to connect affirmation session: \(error.localizedDescription)"
        }
    }
    
    /// Delete journal entry
    public func deleteEntry(_ entry: JournalEntry) async {
        do {
            try await repository.deleteEntry(id: entry.id)
            await loadRecentEntries()
            await loadMoodStatistics()
            
            // If deleted entry is current entry, reset it
            if currentEntry?.id == entry.id {
                currentEntry = JournalEntry(date: selectedDate)
            }
            
        } catch {
            errorMessage = "Failed to delete entry: \(error.localizedDescription)"
        }
    }
    
    /// Get mood color for UI
    public func getMoodColor(_ mood: MoodLevel) -> Color {
        switch mood {
        case .veryLow: return .red
        case .low: return .orange
        case .neutral: return .yellow
        case .high: return .green
        case .veryHigh: return .blue
        }
    }
    
    /// Check if entry exists for date
    public func hasEntry(for date: Date) -> Bool {
        return recentEntries.contains { entry in
            calendar.isDate(entry.date, inSameDayAs: date)
        }
    }
    
    /// Get entry for date from recent entries
    public func getEntry(for date: Date) -> JournalEntry? {
        return recentEntries.first { entry in
            calendar.isDate(entry.date, inSameDayAs: date)
        }
    }
    
    /// Format date for display
    public func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInYesterday(date) {
            return "Yesterday"
        } else {
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        }
    }
    
    /// Get mood trend description
    public func getMoodTrendDescription() -> String {
        guard let stats = moodStatistics else { return "No data available" }
        
        let avgMoodText = String(format: "%.1f", stats.averageMood)
        return "\(stats.moodTrend.emoji) \(stats.moodTrend.title) (Avg: \(avgMoodText)/5)"
    }
}
