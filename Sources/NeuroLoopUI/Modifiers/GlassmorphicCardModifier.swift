import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct GlassmorphicCardModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager

    public init() {}

    public func body(content: Content) -> some View {
        content
            .padding()
            .background(
                // Use current theme card background
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient ??
                        LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15, x: 0, y: 8)
            )
    }
}

extension View {
    public func glassmorphicCard() -> some View {
        modifier(GlassmorphicCardModifier())
    }
}
