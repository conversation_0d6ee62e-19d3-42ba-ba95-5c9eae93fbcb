import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

/// A collection of view modifiers for applying the blue theme styling consistently across the app

// MARK: - Blue Background Modifier

/// Applies the current theme gradient background to a view
public struct BlueBackgroundModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager

    public init() {}

    public func body(content: Content) -> some View {
        content
            .background(
                (themeManager.currentTheme.backgroundColor.asGradient ??
                 LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.backgroundColor.asColor,
                        themeManager.currentTheme.backgroundColor.asColor.opacity(0.8)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                 ))
                .ignoresSafeArea()
            )
    }
}

// MARK: - Blue Card Modifier

/// Applies the current theme card styling with shadow to a view
public struct BlueCardModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    private let cornerRadius: CGFloat

    public init(cornerRadius: CGFloat = 16) {
        self.cornerRadius = cornerRadius
    }

    public func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient ??
                        LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: themeManager.currentTheme.shadowColor.color.opacity(0.3), radius: 15, x: 0, y: 8)
            )
    }
}

// MARK: - Blue Text Modifier

/// Applies current theme text styling
public struct BlueTextModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    private let isPrimary: Bool

    public init(isPrimary: Bool = true) {
        self.isPrimary = isPrimary
    }

    public func body(content: Content) -> some View {
        content
            .foregroundColor(isPrimary ?
                themeManager.currentTheme.primaryTextColor.asColor :
                themeManager.currentTheme.secondaryTextColor.asColor)
    }
}

// MARK: - View Extensions

extension View {
    /// Applies the blue gradient background to the view
    public func blueBackground() -> some View {
        modifier(BlueBackgroundModifier())
    }

    /// Applies the blue card styling with shadow to the view
    public func blueCard(cornerRadius: CGFloat = 16) -> some View {
        modifier(BlueCardModifier(cornerRadius: cornerRadius))
    }

    /// Applies white text styling for the blue theme
    public func blueText(isPrimary: Bool = true) -> some View {
        modifier(BlueTextModifier(isPrimary: isPrimary))
    }
}

// MARK: - Preview

#if DEBUG
struct BlueThemeModifiers_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            Text("Blue Background")
                .font(.headline)
                .blueText()
                .padding()
                .blueCard()

            Text("Blue Card")
                .font(.headline)
                .blueText()
                .padding()
                .blueCard()

            Text("Secondary Text")
                .font(.subheadline)
                .blueText(isPrimary: false)
        }
        .padding()
        .blueBackground()
    }
}
#endif
