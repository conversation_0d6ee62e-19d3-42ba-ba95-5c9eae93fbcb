import Foundation
import NeuroLoopTypes
import NeuroLoopInterfaces

/// Mock journal repository for UI testing and previews
@MainActor
public final class UIJournalRepository: JournalRepositoryProtocol {
    private var entries: [JournalEntry] = []

    public init() {
        // Start with empty entries for clean user experience
        // Sample entries can be created manually for testing if needed
    }

    public func createEntry(_ entry: JournalEntry) async throws {
        entries.append(entry)
    }

    public func updateEntry(_ entry: JournalEntry) async throws {
        if let index = entries.firstIndex(where: { $0.id == entry.id }) {
            entries[index] = entry
        }
    }

    public func deleteEntry(id: UUID) async throws {
        entries.removeAll { $0.id == id }
    }

    public func getEntry(for date: Date) async throws -> JournalEntry? {
        let calendar = Calendar.current
        return entries.first { entry in
            calendar.isDate(entry.date, inSameDayAs: date)
        }
    }

    public func getAllEntries() async throws -> [JournalEntry] {
        return entries.sorted { $0.date > $1.date }
    }

    public func getEntries(from startDate: Date, to endDate: Date) async throws -> [JournalEntry] {
        return entries.filter { entry in
            entry.date >= startDate && entry.date <= endDate
        }.sorted { $0.date > $1.date }
    }

    public func getRecentEntries(days: Int) async throws -> [JournalEntry] {
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        return try await getEntries(from: startDate, to: Date())
    }

    public func getMoodStatistics(from startDate: Date, to endDate: Date) async throws -> MoodStatistics {
        let entriesInRange = try await getEntries(from: startDate, to: endDate)

        guard !entriesInRange.isEmpty else {
            return MoodStatistics(
                averageMood: 3.0,
                moodTrend: .stable,
                totalEntries: 0,
                streakDays: 0,
                moodDistribution: [:]
            )
        }

        let averageMood = Double(entriesInRange.map { $0.mood.rawValue }.reduce(0, +)) / Double(entriesInRange.count)

        // Calculate mood distribution
        var moodDistribution: [MoodLevel: Int] = [:]
        for entry in entriesInRange {
            moodDistribution[entry.mood, default: 0] += 1
        }

        // Calculate trend (simplified)
        let recentEntries = Array(entriesInRange.prefix(7))
        let olderEntries = Array(entriesInRange.dropFirst(7).prefix(7))

        let recentAverage = recentEntries.isEmpty ? 3.0 : Double(recentEntries.map { $0.mood.rawValue }.reduce(0, +)) / Double(recentEntries.count)
        let olderAverage = olderEntries.isEmpty ? 3.0 : Double(olderEntries.map { $0.mood.rawValue }.reduce(0, +)) / Double(olderEntries.count)

        let trend: MoodTrend
        if recentAverage > olderAverage + 0.3 {
            trend = .improving
        } else if recentAverage < olderAverage - 0.3 {
            trend = .declining
        } else {
            trend = .stable
        }

        // Calculate streak
        let streakDays = calculateCurrentStreak(entries: entriesInRange)

        return MoodStatistics(
            averageMood: averageMood,
            moodTrend: trend,
            totalEntries: entriesInRange.count,
            streakDays: streakDays,
            moodDistribution: moodDistribution
        )
    }

    public func connectAffirmationSession(sessionId: UUID, to entryId: UUID) async throws {
        if let index = entries.firstIndex(where: { $0.id == entryId }) {
            if !entries[index].affirmationSessionIds.contains(sessionId) {
                entries[index].affirmationSessionIds.append(sessionId)
            }
        }
    }

    public func getEntriesConnectedTo(sessionId: UUID) async throws -> [JournalEntry] {
        return entries.filter { $0.affirmationSessionIds.contains(sessionId) }
    }

    private func calculateCurrentStreak(entries: [JournalEntry]) -> Int {
        let calendar = Calendar.current
        let today = Date()
        var streakDays = 0

        for i in 0..<30 { // Check last 30 days
            let checkDate = calendar.date(byAdding: .day, value: -i, to: today) ?? today
            let hasEntry = entries.contains { entry in
                calendar.isDate(entry.date, inSameDayAs: checkDate)
            }

            if hasEntry {
                streakDays += 1
            } else if i > 0 { // Allow missing today
                break
            }
        }

        return streakDays
    }

    private func createSampleEntries() {
        let calendar = Calendar.current

        // Create entries for the last 7 days
        for i in 0..<7 {
            let date = calendar.date(byAdding: .day, value: -i, to: Date()) ?? Date()
            let mood = MoodLevel.allCases.randomElement() ?? .neutral
            let thoughts = getSampleThoughts(for: mood)

            let entry = JournalEntry(
                date: date,
                mood: mood,
                thoughts: thoughts
            )
            entries.append(entry)
        }
    }

    private func getSampleThoughts(for mood: MoodLevel) -> String {
        switch mood {
        case .veryHigh:
            return "Had an amazing day! Everything felt aligned and I accomplished so much. The affirmations really helped me stay positive."
        case .high:
            return "Feeling good today. Made progress on my goals and felt grateful for the small wins."
        case .neutral:
            return "A balanced day. Nothing particularly exciting but feeling steady and calm."
        case .low:
            return "Struggled a bit today. Need to remember to be kind to myself and trust the process."
        case .veryLow:
            return "Tough day. Feeling overwhelmed but trying to focus on one thing at a time."
        }
    }
}
