import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/// A mock implementation of the RepetitionServiceProtocol for UI development and testing
@available(iOS 17.0, macOS 14.0, *)
public actor FullMockRepetitionService: RepetitionServiceProtocol {
    // MARK: - Properties

    private var affirmations: [UUID: any AffirmationProtocol] = [:]

    // MARK: - Initialization

    public init() {}

    // MARK: - RepetitionServiceProtocol Methods

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult {
        // Get the current repetition count from MainActor context
        let currentRepetitions = await MainActor.run { affirmation.currentRepetitions }

        // Increment by exactly 1
        let newRepetitions = currentRepetitions + 1

        // Calculate the exact new progress values based on the new repetition count
        // IMPORTANT: Use exact division to ensure precise conversion between repetitions and progress
        let newTodayProgress = min(1.0, Double(newRepetitions) / 100.0)
        let newCycleProgress = min(1.0, Double(newRepetitions) / 100.0)

        print("[MockRepetitionService] Recording repetition - currentRepetitions: \(currentRepetitions) -> \(newRepetitions)")
        print("[MockRepetitionService] New todayProgress: \(newTodayProgress) (calculated from exact repetition count)")
        print("[MockRepetitionService] VERIFICATION - newRepetitions: \(newRepetitions), floor(newTodayProgress * 100): \(Int(floor(newTodayProgress * 100.0)))")

        // Create a copy of the affirmation with updated progress
        let updatedAffirmation = await MainActor.run {
            AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: newTodayProgress,
                cycleProgress: newCycleProgress
            )
        }

        print("[MockRepetitionService] Verified updated repetitions: \(await MainActor.run { updatedAffirmation.currentRepetitions })")

        // Double-check that we've incremented by exactly 1
        let updatedRepetitions = await MainActor.run { updatedAffirmation.currentRepetitions }
        if updatedRepetitions != currentRepetitions + 1 {
            print("[MockRepetitionService] WARNING: Repetition count did not increment by exactly 1!")
            print("[MockRepetitionService] Expected: \(currentRepetitions + 1), Actual: \(updatedRepetitions)")
        }

        // Store the updated affirmation (actor-safe)
        affirmations[await MainActor.run { affirmation.id }] = updatedAffirmation

        return RepetitionResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: updatedAffirmation.todayProgress >= 1.0,
            isCycleComplete: updatedAffirmation.cycleProgress >= 1.0
        )
    }

    nonisolated public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        // For the mock service, we'll return mock progress data
        // This avoids MainActor isolation issues while providing useful test data
        let totalRepetitions = 100
        let currentRepetitions = 25 // Mock value
        let todayProgress = 0.25 // Mock value
        let cycleProgress = 0.25 // Mock value

        print("[MockRepetitionService] getProgress - returning mock data: todayProgress: \(todayProgress), currentRepetitions: \(currentRepetitions)")
        let currentDay = 1
        let totalDays = 7

        return ProgressInfo(
            todayProgress: todayProgress,
            cycleProgress: cycleProgress,
            currentDay: currentDay,
            totalDays: totalDays,
            currentRepetitions: currentRepetitions,
            totalRepetitions: totalRepetitions,
            hasTodayQuotaMet: todayProgress >= 1.0,
            isCycleComplete: cycleProgress >= 1.0,
            hasActiveCycle: true
        )
    }

    public func validateStreaks() async throws -> StreakValidationResult {
        // Mock implementation that always returns success
        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: 0,
            brokenStreaks: 0
        )
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Reset the affirmation's progress
        let updatedAffirmation = await MainActor.run {
            AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: 0.0,
                cycleProgress: 0.0
            )
        }

        // Store the updated affirmation (actor-safe)
        affirmations[await MainActor.run { affirmation.id }] = updatedAffirmation

        return CycleResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation
        )
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Start a new cycle for the affirmation
        let updatedAffirmation = await MainActor.run {
            AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: 0.0,
                cycleProgress: 0.0
            )
        }

        // Store the updated affirmation (actor-safe)
        affirmations[await MainActor.run { affirmation.id }] = updatedAffirmation

        return CycleResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation
        )
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Start a session for the affirmation
        return try await startCycle(for: affirmation)
    }

    nonisolated public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 1,
            longestStreak: 1,
            completedCycles: 0,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }

    nonisolated public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        // Return mock value to avoid MainActor isolation issues
        return true
    }

    nonisolated public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return nil
    }

    nonisolated public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return false
    }
}
