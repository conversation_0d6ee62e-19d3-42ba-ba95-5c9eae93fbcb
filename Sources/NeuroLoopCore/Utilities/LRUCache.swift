import Foundation

/// Generic LRU (Least Recently Used) Cache implementation
public class LRUCache<Key: Hashable, Value> {
    private class Node {
        let key: Key
        var value: Value
        var prev: Node?
        var next: Node?
        
        init(key: Key, value: Value) {
            self.key = key
            self.value = value
        }
    }
    
    private let capacity: Int
    private var cache: [Key: Node] = [:]
    private var head: Node?
    private var tail: Node?

    public var count: Int {
        return cache.count
    }

    public init(capacity: Int) {
        self.capacity = capacity
        // LRU cache will be implemented without dummy nodes for type safety
    }
    
    public func getValue(forKey key: Key) -> Value? {
        guard let node = cache[key] else { return nil }

        // Move to front (most recently used)
        moveToFront(node)
        return node.value
    }

    public func setValue(_ value: Value, forKey key: Key) {
        if let existingNode = cache[key] {
            // Update existing node
            existingNode.value = value
            moveToFront(existingNode)
        } else {
            // Add new node
            let newNode = Node(key: key, value: value)
            cache[key] = newNode
            addToFront(newNode)

            // Remove least recently used if over capacity
            if cache.count > capacity {
                removeLeastRecentlyUsed()
            }
        }
    }

    public func removeValue(forKey key: Key) {
        guard let node = cache[key] else { return }

        cache.removeValue(forKey: key)
        removeNode(node)
    }

    public func removeAll() {
        cache.removeAll()
        head = nil
        tail = nil
    }

    // MARK: - Private Methods

    private func addToFront(_ node: Node) {
        if head == nil {
            head = node
            tail = node
        } else {
            node.next = head
            head?.prev = node
            head = node
        }
    }

    private func removeNode(_ node: Node) {
        if node === head && node === tail {
            // Only node
            head = nil
            tail = nil
        } else if node === head {
            // Head node
            head = node.next
            head?.prev = nil
        } else if node === tail {
            // Tail node
            tail = node.prev
            tail?.next = nil
        } else {
            // Middle node
            node.prev?.next = node.next
            node.next?.prev = node.prev
        }
        
        node.prev = nil
        node.next = nil
    }

    private func moveToFront(_ node: Node) {
        guard node !== head else { return }

        // Remove from current position
        if node === tail {
            tail = node.prev
            tail?.next = nil
        } else {
            node.prev?.next = node.next
            node.next?.prev = node.prev
        }

        // Add to front
        node.prev = nil
        node.next = head
        head?.prev = node
        head = node
    }

    private func removeLeastRecentlyUsed() {
        guard let lru = tail else { return }

        cache.removeValue(forKey: lru.key)
        removeNode(lru)
    }
}
