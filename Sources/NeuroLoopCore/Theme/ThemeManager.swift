import Foundation
import SwiftUI
import NeuroLoopTypes
import NeuroLoopInterfaces

@MainActor
public class ThemeManager: ObservableObject, ThemeManagingSendable, @unchecked Sendable {
    public static let shared = ThemeManager()

    @Published public var currentTheme: Theme = Theme.blue
    @Published public private(set) var builtInThemes: [Theme] = [
        Theme.blue,      // Ocean Blue (Default)
        Theme.purple,    // Royal Purple
        Theme.green,     // Forest Green
        Theme.orange,    // Sunset Orange
        Theme.roseGold,  // Rose Gold
        Theme.teal,      // Teal Breeze
        Theme.light,     // Light
        Theme.dark       // Dark
    ]
    @Published public private(set) var customThemes: [Theme] = []

    // MARK: - Performance Optimization Caches

    private var gradientCache: [String: LinearGradient] = [:]
    private var colorCache: [String: Color] = [:]
    private var lastThemeId: String = ""
    private var isUpdating = false

    // Computed property to get all available themes
    public var availableThemes: [Theme] {
        builtInThemes + customThemes
    }

    private init() {
        loadThemes()
        precomputeThemeAssets()
    }

    public func setTheme(_ theme: Theme, animated: Bool = true) {
        guard !isUpdating else { return }

        isUpdating = true
        defer { isUpdating = false }

        // Only update if theme actually changed
        guard theme.id != currentTheme.id else { return }

        print("[ThemeManager] Switching theme from \(currentTheme.id) to \(theme.id)")

        if animated {
            withAnimation(.easeInOut(duration: 0.3)) {
                self.currentTheme = theme
            }
        } else {
            self.currentTheme = theme
        }

        // Update cache for new theme
        updateCacheForTheme(theme)
        saveCurrentTheme()

        print("[ThemeManager] Theme switch completed")
    }

    // MARK: - Performance Optimization Methods

    /// Pre-compute theme assets for faster switching
    private func precomputeThemeAssets() {
        print("[ThemeManager] Pre-computing theme assets...")

        for theme in builtInThemes {
            updateCacheForTheme(theme)
        }

        print("[ThemeManager] Theme assets pre-computed for \(builtInThemes.count) themes")
    }

    /// Update cache for a specific theme
    private func updateCacheForTheme(_ theme: Theme) {
        let themeId = theme.id

        // Cache primary gradient
        if let gradient = theme.backgroundColor.asGradient {
            gradientCache["\(themeId)_background"] = gradient
        }

        // Cache card gradient
        if let cardGradient = theme.cardBackgroundColor.asGradient {
            gradientCache["\(themeId)_card"] = cardGradient
        }

        // Cache colors
        colorCache["\(themeId)_primary"] = theme.primaryTextColor.asColor
        colorCache["\(themeId)_secondary"] = theme.secondaryTextColor.asColor
        colorCache["\(themeId)_accent"] = theme.accentColor.asColor
    }

    /// Get cached gradient for current theme
    public func getCachedGradient(for type: String) -> LinearGradient? {
        return gradientCache["\(currentTheme.id)_\(type)"]
    }

    /// Get cached color for current theme
    public func getCachedColor(for type: String) -> Color? {
        return colorCache["\(currentTheme.id)_\(type)"]
    }

    // CRUD for custom themes
    public func addCustomTheme(_ theme: Theme) {
        customThemes.append(theme)
        saveThemes()
    }

    public func updateCustomTheme(_ theme: Theme) {
        if let idx = customThemes.firstIndex(where: { $0.id == theme.id }) {
            customThemes[idx] = theme
            saveThemes()
        }
    }

    public func deleteCustomTheme(_ theme: Theme) {
        customThemes.removeAll { $0.id == theme.id }
        saveThemes()
    }

    // Persistence (to be implemented in ThemePersistenceService)
    private func loadThemes() {
        customThemes = ThemePersistenceService.loadCustomThemes()
        let allThemes = builtInThemes + customThemes
        if let loaded = ThemePersistenceService.loadCurrentTheme(from: allThemes) {
            self.currentTheme = loaded
        }
    }
    private func saveThemes() {
        ThemePersistenceService.saveCustomThemes(customThemes)
    }
    private func saveCurrentTheme() {
        ThemePersistenceService.saveCurrentTheme(currentTheme)
    }
}

// MARK: - ThemeManaging Protocol Conformance

extension ThemeManager {
    /// Protocol conformance method - calls the enhanced setTheme with default animation
    public func setTheme(_ theme: Theme) {
        setTheme(theme, animated: true)
    }
}


