import Foundation
import Combine
import SwiftUI
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Base reactive view model that automatically subscribes to data updates
@MainActor
open class ReactiveViewModel: ObservableObject, @unchecked Sendable {
    
    // MARK: - Properties
    
    /// Reference to the reactive data manager
    internal let dataManager = ReactiveDataManager.shared

    /// Cancellables for Combine subscriptions
    internal var cancellables = Set<AnyCancellable>()
    
    /// Loading state
    @Published public var isLoading = false
    
    /// Error state
    @Published public var errorMessage: String? = nil
    @Published public var hasError = false
    
    // MARK: - Initialization
    
    public init() {
        setupReactiveSubscriptions()
        print("[ReactiveViewModel] Initialized with reactive subscriptions")
    }
    
    deinit {
        cancellables.removeAll()
        print("[ReactiveViewModel] Deinitialized and cleaned up subscriptions")
    }
    
    // MARK: - Reactive Subscriptions Setup
    
    /// Override this method in subclasses to set up specific reactive subscriptions
    open func setupReactiveSubscriptions() {
        // Subscribe to loading state changes
        dataManager.loadingStatePublisher
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        // Subscribe to error state changes
        dataManager.$hasError
            .receive(on: DispatchQueue.main)
            .assign(to: \.hasError, on: self)
            .store(in: &cancellables)
        
        // Subscribe to error messages
        dataManager.$lastError
            .receive(on: DispatchQueue.main)
            .map { $0?.localizedDescription }
            .assign(to: \.errorMessage, on: self)
            .store(in: &cancellables)
        
        // Subscribe to data update events
        dataManager.dataUpdatePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] event in
                self?.handleDataUpdate(event)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Update Handling
    
    /// Override this method in subclasses to handle specific data update events
    open func handleDataUpdate(_ event: ReactiveDataManager.DataUpdateEvent) {
        switch event {
        case .affirmationsLoaded(let affirmations):
            onAffirmationsLoaded(affirmations)
        case .affirmationCreated(let affirmation):
            onAffirmationCreated(affirmation)
        case .affirmationUpdated(let affirmation):
            onAffirmationUpdated(affirmation)
        case .affirmationDeleted(let id):
            onAffirmationDeleted(id)
        case .currentAffirmationChanged(let affirmation):
            onCurrentAffirmationChanged(affirmation)
        case .favoritesUpdated(let favorites):
            onFavoritesUpdated(favorites)
        case .repetitionRecorded(let id, let count):
            onRepetitionRecorded(id, count)
        case .cycleStarted(let id):
            onCycleStarted(id)
        case .error(let error):
            onError(error)
        }
    }
    
    // MARK: - Data Update Event Handlers (Override in subclasses)
    
    open func onAffirmationsLoaded(_ affirmations: [any AffirmationProtocol]) {
        // Override in subclasses
    }
    
    open func onAffirmationCreated(_ affirmation: any AffirmationProtocol) {
        // Override in subclasses
    }
    
    open func onAffirmationUpdated(_ affirmation: any AffirmationProtocol) {
        // Override in subclasses
    }
    
    open func onAffirmationDeleted(_ id: UUID) {
        // Override in subclasses
    }
    
    open func onCurrentAffirmationChanged(_ affirmation: (any AffirmationProtocol)?) {
        // Override in subclasses
    }
    
    open func onFavoritesUpdated(_ favorites: [any AffirmationProtocol]) {
        // Override in subclasses
    }
    
    open func onRepetitionRecorded(_ affirmationId: UUID, _ count: Int) {
        // Override in subclasses
    }
    
    open func onCycleStarted(_ affirmationId: UUID) {
        // Override in subclasses
    }
    
    open func onError(_ error: Error) {
        // Override in subclasses for custom error handling
        print("[ReactiveViewModel] Error received: \(error.localizedDescription)")
    }
    
    // MARK: - Convenience Methods
    
    /// Clear the current error state
    public func clearError() {
        hasError = false
        errorMessage = nil
        dataManager.clearError()
    }
    
    /// Refresh data from the data manager
    public func refreshData() async {
        await dataManager.refreshData()
    }
    
    // MARK: - Data Access Helpers
    
    /// Get all affirmations from the data manager
    public var affirmations: [any AffirmationProtocol] {
        dataManager.affirmations
    }
    
    /// Get current affirmation from the data manager
    public var currentAffirmation: (any AffirmationProtocol)? {
        dataManager.currentAffirmation
    }
    
    /// Get favorite affirmations from the data manager
    public var favoriteAffirmations: [any AffirmationProtocol] {
        dataManager.favoriteAffirmations
    }
    
    // MARK: - Data Operations
    
    /// Create a new affirmation
    public func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil
    ) async -> (any AffirmationProtocol)? {
        return await dataManager.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
    }
    
    /// Update an affirmation
    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async -> Bool {
        return await dataManager.updateAffirmation(affirmation)
    }
    
    /// Delete an affirmation
    public func deleteAffirmation(id: UUID) async -> Bool {
        return await dataManager.deleteAffirmation(id: id)
    }
    
    /// Set the current affirmation
    public func setCurrentAffirmation(_ affirmation: (any AffirmationProtocol)?) {
        dataManager.setCurrentAffirmation(affirmation)
    }
    
    /// Record a repetition
    public func recordRepetition(for affirmation: any AffirmationProtocol) async -> Bool {
        return await dataManager.recordRepetition(for: affirmation)
    }
    
    /// Start a cycle
    public func startCycle(for affirmation: any AffirmationProtocol) async -> Bool {
        return await dataManager.startCycle(for: affirmation)
    }
}

// MARK: - Reactive View Model Protocol

/// Protocol for view models that support reactive data updates
@MainActor
public protocol ReactiveViewModelProtocol: ObservableObject {
    /// Handle data update events
    func handleDataUpdate(_ event: ReactiveDataManager.DataUpdateEvent)

    /// Refresh data
    func refreshData() async

    /// Clear error state
    func clearError()
}

// MARK: - Reactive View Model Extensions

extension ReactiveViewModel: ReactiveViewModelProtocol {
    // Already implemented above
}

// MARK: - SwiftUI Integration Helpers

/// Property wrapper for reactive data binding
@propertyWrapper
public struct ReactiveData<T>: DynamicProperty {
    @StateObject private var dataManager = ReactiveDataManager.shared
    private let getter: (ReactiveDataManager) -> T

    public init(_ getter: @escaping (ReactiveDataManager) -> T) {
        self.getter = getter
    }

    public var wrappedValue: T {
        getter(dataManager)
    }

    public var projectedValue: ReactiveDataManager {
        dataManager
    }
}

// MARK: - Reactive Data Binding Helpers

// MARK: - Convenience Reactive Data Factories

/// Factory methods for common reactive data bindings
@MainActor
public enum ReactiveDataFactory {
    /// Reactive affirmations binding
    public static func affirmations() -> ReactiveData<[any AffirmationProtocol]> {
        ReactiveData { dataManager in
            dataManager.affirmations
        }
    }

    /// Reactive current affirmation binding
    public static func currentAffirmation() -> ReactiveData<(any AffirmationProtocol)?> {
        ReactiveData { dataManager in
            dataManager.currentAffirmation
        }
    }

    /// Reactive favorite affirmations binding
    public static func favoriteAffirmations() -> ReactiveData<[any AffirmationProtocol]> {
        ReactiveData { dataManager in
            dataManager.favoriteAffirmations
        }
    }

    /// Reactive loading state binding
    public static func isLoading() -> ReactiveData<Bool> {
        ReactiveData { dataManager in
            dataManager.isLoadingAffirmations
        }
    }
}
