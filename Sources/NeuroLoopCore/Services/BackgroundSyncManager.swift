import Foundation
import Combine
import Network
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Advanced background data synchronization manager with offline-first architecture
@MainActor
public final class BackgroundSyncManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - Singleton
    
    public static let shared = BackgroundSyncManager()
    
    // MARK: - Published Properties
    
    @Published public private(set) var syncStatus = SyncStatus.idle
    @Published public private(set) var isOnline = true
    @Published public private(set) var syncProgress = SyncProgress()
    @Published public private(set) var syncMetrics = SyncMetrics()
    @Published public private(set) var pendingChanges: [SyncChange] = []
    
    // MARK: - Configuration
    
    public struct Configuration {
        let syncInterval: TimeInterval
        let maxRetryAttempts: Int
        let batchSize: Int
        let conflictResolutionStrategy: ConflictResolutionStrategy
        let enableDeltaSync: Bool
        let enableCompressionSync: Bool
        let networkTimeoutInterval: TimeInterval
        
        public static let `default` = Configuration(
            syncInterval: 300.0, // 5 minutes
            maxRetryAttempts: 3,
            batchSize: 50,
            conflictResolutionStrategy: .lastWriteWins,
            enableDeltaSync: true,
            enableCompressionSync: true,
            networkTimeoutInterval: 30.0
        )
    }
    
    // MARK: - Private Properties
    
    private let config: Configuration
    private let dataManager = ReactiveDataManager.shared
    private let cacheManager = MultiLevelCacheManager.shared
    private let optimisticManager = EnhancedOptimisticUpdateManager.shared
    private var networkMonitor: NWPathMonitor
    private var syncTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private var changeTracker: ChangeTracker
    private var deltaProcessor: DeltaProcessor
    private var conflictResolver: SyncConflictResolver
    private var compressionManager: CompressionManager
    
    // MARK: - Data Structures
    
    public enum SyncStatus {
        case idle
        case preparing
        case uploading
        case downloading
        case processing
        case success
        case error(Error)
        case offline
        
        var isActive: Bool {
            switch self {
            case .idle, .success, .error, .offline:
                return false
            default:
                return true
            }
        }
    }
    
    public struct SyncProgress {
        var totalItems: Int = 0
        var processedItems: Int = 0
        var uploadedItems: Int = 0
        var downloadedItems: Int = 0
        var conflictsResolved: Int = 0
        var currentOperation: String = ""
        
        var progressPercentage: Double {
            guard totalItems > 0 else { return 0.0 }
            return Double(processedItems) / Double(totalItems)
        }
    }
    
    public struct SyncMetrics {
        var totalSyncs: Int = 0
        var successfulSyncs: Int = 0
        var failedSyncs: Int = 0
        var averageSyncTime: TimeInterval = 0.0
        var totalDataTransferred: Int = 0
        var compressionRatio: Double = 0.0
        var lastSyncDate: Date?
        var lastSyncError: Error?
        
        var successRate: Double {
            guard totalSyncs > 0 else { return 0.0 }
            return Double(successfulSyncs) / Double(totalSyncs)
        }
    }
    
    public typealias ConflictResolutionStrategy = SyncConflictResolver.ConflictResolutionStrategy
    
    // MARK: - Initialization
    
    private init(config: Configuration = .default) {
        self.config = config
        self.networkMonitor = NWPathMonitor()
        self.changeTracker = ChangeTracker()
        self.deltaProcessor = DeltaProcessor()
        self.conflictResolver = SyncConflictResolver(strategy: config.conflictResolutionStrategy)
        self.compressionManager = CompressionManager(enabled: config.enableCompressionSync)
        
        setupNetworkMonitoring()
        setupDataChangeTracking()
        setupPeriodicSync()
        
        print("[BackgroundSyncManager] Initialized with offline-first architecture")
    }
    
    // MARK: - Public API
    
    /// Start background synchronization
    public func startBackgroundSync() async {
        guard !syncStatus.isActive else {
            print("[BackgroundSyncManager] Sync already in progress")
            return
        }
        
        guard isOnline else {
            syncStatus = .offline
            print("[BackgroundSyncManager] Cannot sync - offline")
            return
        }
        
        let startTime = Date()
        syncStatus = .preparing
        syncMetrics.totalSyncs += 1
        
        do {
            // Prepare sync data
            await prepareSyncData()
            
            // Perform bidirectional sync
            try await performBidirectionalSync()
            
            // Update metrics
            let duration = Date().timeIntervalSince(startTime)
            updateSyncMetrics(duration: duration, success: true)
            
            syncStatus = .success
            syncMetrics.lastSyncDate = Date()
            
            print("[BackgroundSyncManager] Background sync completed successfully")
            
        } catch {
            updateSyncMetrics(duration: Date().timeIntervalSince(startTime), success: false)
            syncStatus = .error(error)
            syncMetrics.lastSyncError = error
            
            print("[BackgroundSyncManager] Background sync failed: \(error)")
        }
    }
    
    /// Force immediate sync
    public func forceSyncNow() async {
        await startBackgroundSync()
    }
    
    /// Add change to pending sync queue
    public func addPendingChange(_ change: SyncChange) async {
        pendingChanges.append(change)
        changeTracker.trackChange(change)
        
        // Trigger immediate sync if critical change
        if change.priority == .critical {
            await startBackgroundSync()
        }
        
        print("[BackgroundSyncManager] Added pending change: \(change.id)")
    }
    
    /// Get sync status report
    public func getSyncReport() -> String {
        return """
        [BackgroundSyncManager] Sync Report:
        - Status: \(syncStatus)
        - Online: \(isOnline)
        - Pending Changes: \(pendingChanges.count)
        - Total Syncs: \(syncMetrics.totalSyncs)
        - Success Rate: \(String(format: "%.2f", syncMetrics.successRate * 100))%
        - Average Sync Time: \(String(format: "%.2f", syncMetrics.averageSyncTime))s
        - Last Sync: \(syncMetrics.lastSyncDate?.description ?? "Never")
        - Data Transferred: \(formatBytes(syncMetrics.totalDataTransferred))
        - Compression Ratio: \(String(format: "%.2f", syncMetrics.compressionRatio))%
        """
    }
    
    // MARK: - Private Implementation
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                let wasOnline = self?.isOnline ?? false
                self?.isOnline = path.status == .satisfied
                
                if !wasOnline && self?.isOnline == true {
                    // Just came online - trigger sync
                    await self?.startBackgroundSync()
                }
            }
        }
        
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor.start(queue: queue)
    }
    
    private func setupDataChangeTracking() {
        // Track data changes from ReactiveDataManager
        dataManager.dataUpdatePublisher
            .sink { [weak self] event in
                Task { @MainActor in
                    await self?.handleDataUpdate(event)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupPeriodicSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: config.syncInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.startBackgroundSync()
            }
        }
    }
    
    private func prepareSyncData() async {
        syncProgress.currentOperation = "Preparing sync data"
        syncProgress.totalItems = pendingChanges.count
        syncProgress.processedItems = 0
        
        // Group changes by type for efficient processing
        let groupedChanges = Dictionary(grouping: pendingChanges) { $0.type.rawValue }

        // Process delta changes if enabled
        if config.enableDeltaSync {
            await deltaProcessor.processDeltaChanges(groupedChanges)
        }
        
        // Compress data if enabled
        if config.enableCompressionSync {
            await compressionManager.compressChanges(pendingChanges)
        }
    }
    
    private func performBidirectionalSync() async throws {
        // Upload local changes
        try await uploadLocalChanges()
        
        // Download remote changes
        await downloadRemoteChanges()
        
        // Resolve conflicts
        await resolveConflicts()
        
        // Clean up processed changes
        await cleanupProcessedChanges()
    }
    
    private func uploadLocalChanges() async throws {
        syncStatus = .uploading
        syncProgress.currentOperation = "Uploading local changes"
        
        let batches = pendingChanges.chunked(into: config.batchSize)
        
        for (index, batch) in batches.enumerated() {
            do {
                try await uploadBatch(batch)
                syncProgress.uploadedItems += batch.count
                syncProgress.processedItems = (index + 1) * config.batchSize

            } catch {
                print("[BackgroundSyncManager] Failed to upload batch \(index): \(error)")
                throw error
            }
        }
    }
    
    private func downloadRemoteChanges() async {
        syncStatus = .downloading
        syncProgress.currentOperation = "Downloading remote changes"
        
        // Implementation for downloading remote changes
        // This would integrate with your backend service
        print("[BackgroundSyncManager] Downloading remote changes...")
    }
    
    private func resolveConflicts() async {
        syncStatus = .processing
        syncProgress.currentOperation = "Resolving conflicts"
        
        let conflicts = await detectConflicts()
        
        for conflict in conflicts {
            let resolved = await conflictResolver.resolveConflict(conflict)
            if resolved {
                syncProgress.conflictsResolved += 1
            }
        }
    }
    
    private func cleanupProcessedChanges() async {
        // Remove successfully processed changes
        pendingChanges.removeAll { change in
            change.status == .synced
        }
        
        changeTracker.cleanupProcessedChanges()
    }
    
    private func uploadBatch(_ batch: [SyncChange]) async throws {
        // Implementation for uploading a batch of changes
        // This would integrate with your backend service
        for change in batch {
            change.status = .syncing
            // Simulate upload
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            change.status = .synced
        }
    }
    
    private func detectConflicts() async -> [SyncConflict] {
        // Implementation for conflict detection
        // This would compare local and remote versions
        return []
    }
    
    private func handleDataUpdate(_ event: ReactiveDataManager.DataUpdateEvent) async {
        let change = createSyncChange(from: event)
        await addPendingChange(change)
    }
    
    private func createSyncChange(from event: ReactiveDataManager.DataUpdateEvent) -> SyncChange {
        switch event {
        case .affirmationCreated(let affirmation):
            return SyncChange(
                id: UUID(),
                type: .create,
                entityId: affirmation.id,
                entityType: "Affirmation",
                data: affirmation,
                timestamp: Date(),
                priority: .normal
            )
        case .affirmationUpdated(let affirmation):
            return SyncChange(
                id: UUID(),
                type: .update,
                entityId: affirmation.id,
                entityType: "Affirmation",
                data: affirmation,
                timestamp: Date(),
                priority: .normal
            )
        case .affirmationDeleted(let id):
            return SyncChange(
                id: UUID(),
                type: .delete,
                entityId: id,
                entityType: "Affirmation",
                data: nil,
                timestamp: Date(),
                priority: .normal
            )
        default:
            return SyncChange(
                id: UUID(),
                type: .update,
                entityId: UUID(),
                entityType: "Unknown",
                data: nil,
                timestamp: Date(),
                priority: .low
            )
        }
    }
    
    private func updateSyncMetrics(duration: TimeInterval, success: Bool) {
        if success {
            syncMetrics.successfulSyncs += 1
        } else {
            syncMetrics.failedSyncs += 1
        }
        
        // Update average sync time
        let totalSyncs = syncMetrics.totalSyncs
        let currentAverage = syncMetrics.averageSyncTime
        syncMetrics.averageSyncTime = (currentAverage * Double(totalSyncs - 1) + duration) / Double(totalSyncs)
    }
    
    private func formatBytes(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    deinit {
        networkMonitor.cancel()
        syncTimer?.invalidate()
    }
}
