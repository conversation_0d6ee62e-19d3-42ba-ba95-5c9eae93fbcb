import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Advanced conflict resolution system for optimistic updates
@MainActor
public final class ConflictResolver: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public enum ConflictResolutionStrategy {
        case lastWriteWins
        case firstWriteWins
        case merge
        case userPrompt
        case fieldLevelMerge
        case timestampBased
    }
    
    public struct ConflictDetectionResult {
        let hasConflict: Bool
        let conflictedFields: [String]
        let conflictType: ConflictType
        let severity: ConflictSeverity
    }
    
    public enum ConflictType {
        case dataConflict
        case versionConflict
        case concurrentModification
        case deletionConflict
    }
    
    public enum ConflictSeverity {
        case low
        case medium
        case high
        case critical
    }
    
    // MARK: - Properties
    
    private let strategy: ConflictResolutionStrategy
    private var conflictHistory: [ConflictRecord] = []
    private var conflictStatistics = ConflictStatistics()
    
    // MARK: - Conflict Record

    public struct ConflictRecord {
        let id: UUID
        let timestamp: Date
        let affirmationId: UUID
        let conflictType: ConflictType
        let resolutionStrategy: ConflictResolutionStrategy
        let resolved: Bool
    }
    
    // MARK: - Statistics
    
    public struct ConflictStatistics {
        var totalConflicts: Int = 0
        var resolvedConflicts: Int = 0
        var unresolvedConflicts: Int = 0
        var conflictsByType: [ConflictType: Int] = [:]
        var conflictsByStrategy: [ConflictResolutionStrategy: Int] = [:]
        
        var resolutionRate: Double {
            guard totalConflicts > 0 else { return 0.0 }
            return Double(resolvedConflicts) / Double(totalConflicts)
        }
    }
    
    // MARK: - Initialization
    
    public init(strategy: ConflictResolutionStrategy) {
        self.strategy = strategy
        print("[ConflictResolver] Initialized with strategy: \(strategy)")
    }
    
    // MARK: - Conflict Detection
    
    /// Detect conflicts between current and original affirmation
    public func detectConflict(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol
    ) async -> Bool {
        let result = await performConflictDetection(current: current, original: original)
        
        if result.hasConflict {
            recordConflict(
                affirmationId: current.id,
                type: result.conflictType,
                severity: result.severity
            )
        }
        
        return result.hasConflict
    }
    
    /// Check for conflicts with incoming data update
    public func checkForConflicts(with affirmation: any AffirmationProtocol) async {
        // Implementation for real-time conflict checking
        print("[ConflictResolver] Checking for conflicts with: \(affirmation.id)")
    }
    
    // MARK: - Conflict Resolution
    
    /// Resolve conflict between current and original affirmation
    public func resolveConflict(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol,
        strategy: ConflictResolutionStrategy? = nil
    ) async -> any AffirmationProtocol {
        let resolutionStrategy = strategy ?? self.strategy
        
        let resolved = await performConflictResolution(
            current: current,
            original: original,
            strategy: resolutionStrategy
        )
        
        recordResolution(
            affirmationId: current.id,
            strategy: resolutionStrategy,
            success: true
        )
        
        return resolved
    }
    
    // MARK: - Private Implementation
    
    private func performConflictDetection(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol
    ) async -> ConflictDetectionResult {
        var conflictedFields: [String] = []
        var conflictType: ConflictType = .dataConflict
        var severity: ConflictSeverity = .low
        
        // Check for data conflicts
        if current.text != original.text {
            conflictedFields.append("text")
            severity = .medium
        }
        
        if current.category != original.category {
            conflictedFields.append("category")
            severity = .medium
        }
        
        if current.isFavorite != original.isFavorite {
            conflictedFields.append("isFavorite")
        }
        
        if current.currentRepetitions != original.currentRepetitions {
            conflictedFields.append("currentRepetitions")
            conflictType = .concurrentModification
            severity = .high
        }
        
        if current.updatedAt != original.updatedAt {
            conflictType = .versionConflict
            if severity == .low {
                severity = .medium
            }
        }
        
        let hasConflict = !conflictedFields.isEmpty
        
        return ConflictDetectionResult(
            hasConflict: hasConflict,
            conflictedFields: conflictedFields,
            conflictType: conflictType,
            severity: severity
        )
    }
    
    private func performConflictResolution(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol,
        strategy: ConflictResolutionStrategy
    ) async -> any AffirmationProtocol {
        switch strategy {
        case .lastWriteWins:
            return current
            
        case .firstWriteWins:
            return original
            
        case .merge:
            return await performMergeResolution(current: current, original: original)
            
        case .fieldLevelMerge:
            return await performFieldLevelMerge(current: current, original: original)
            
        case .timestampBased:
            return await performTimestampBasedResolution(current: current, original: original)
            
        case .userPrompt:
            // For now, fall back to last write wins
            // In a real implementation, this would prompt the user
            return current
        }
    }
    
    private func performMergeResolution(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol
    ) async -> any AffirmationProtocol {
        // Create a merged affirmation with intelligent field selection
        return MergedAffirmation(
            id: current.id,
            text: current.text, // Prefer current text changes
            category: current.category, // Prefer current category changes
            isFavorite: current.isFavorite || original.isFavorite, // Union of favorites
            recordingURL: current.recordingURL ?? original.recordingURL,
            currentRepetitions: max(current.currentRepetitions, original.currentRepetitions),
            completedCycles: max(current.completedCycles, original.completedCycles),
            energyLevel: (current.energyLevel + original.energyLevel) / 2.0, // Average energy
            notes: mergeNotes(current.notes, original.notes),
            updatedAt: max(current.updatedAt, original.updatedAt)
        )
    }
    
    private func performFieldLevelMerge(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol
    ) async -> any AffirmationProtocol {
        // Perform field-by-field intelligent merging
        return MergedAffirmation(
            id: current.id,
            text: selectBestText(current.text, original.text),
            category: current.category, // Assume current category is intentional
            isFavorite: current.isFavorite, // Prefer current favorite status
            recordingURL: current.recordingURL ?? original.recordingURL,
            currentRepetitions: max(current.currentRepetitions, original.currentRepetitions),
            completedCycles: max(current.completedCycles, original.completedCycles),
            energyLevel: current.energyLevel > 0 ? current.energyLevel : original.energyLevel,
            notes: mergeNotes(current.notes, original.notes),
            updatedAt: Date() // Set to current time for merged result
        )
    }
    
    private func performTimestampBasedResolution(
        current: any AffirmationProtocol,
        original: any AffirmationProtocol
    ) async -> any AffirmationProtocol {
        // Use the most recently updated affirmation
        return current.updatedAt > original.updatedAt ? current : original
    }
    
    // MARK: - Helper Methods
    
    private func selectBestText(_ current: String, _ original: String) -> String {
        // Prefer longer, more descriptive text
        return current.count > original.count ? current : original
    }
    
    private func mergeNotes(_ currentNotes: String?, _ originalNotes: String?) -> String? {
        switch (currentNotes, originalNotes) {
        case (let current?, let original?):
            return "\(original)\n---\n\(current)"
        case (let current?, nil):
            return current
        case (nil, let original?):
            return original
        case (nil, nil):
            return nil
        }
    }
    
    private func recordConflict(
        affirmationId: UUID,
        type: ConflictType,
        severity: ConflictSeverity
    ) {
        let record = ConflictRecord(
            id: UUID(),
            timestamp: Date(),
            affirmationId: affirmationId,
            conflictType: type,
            resolutionStrategy: strategy,
            resolved: false
        )
        
        conflictHistory.append(record)
        conflictStatistics.totalConflicts += 1
        conflictStatistics.conflictsByType[type, default: 0] += 1
        
        print("[ConflictResolver] Conflict detected: \(type) for affirmation \(affirmationId)")
    }
    
    private func recordResolution(
        affirmationId: UUID,
        strategy: ConflictResolutionStrategy,
        success: Bool
    ) {
        if success {
            conflictStatistics.resolvedConflicts += 1
        } else {
            conflictStatistics.unresolvedConflicts += 1
        }
        
        conflictStatistics.conflictsByStrategy[strategy, default: 0] += 1
        
        print("[ConflictResolver] Conflict resolved using \(strategy) for affirmation \(affirmationId)")
    }
    
    // MARK: - Public API
    
    /// Get conflict statistics
    public func getStatistics() -> ConflictStatistics {
        return conflictStatistics
    }
    
    /// Get conflict history
    public func getConflictHistory() -> [ConflictRecord] {
        return conflictHistory
    }
}

// MARK: - Merged Affirmation Implementation

/// Merged affirmation result from conflict resolution
private struct MergedAffirmation: AffirmationProtocol {
    let id: UUID
    let text: String
    let category: AffirmationCategory
    let isFavorite: Bool
    let hasRecording: Bool
    let todayProgress: Double = 0.0
    let cycleProgress: Double = 0.0
    let recordingURL: URL?
    let completedCycles: Int
    let currentRepetitions: Int
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double
    let moodRating: Int? = nil
    let notes: String?
    let playCount: Int = 0
    let hasActiveCycle: Bool = false
    let currentCycleDay: Int = 0
    let cycleStartDate: Date? = nil
    let dailyProgress: [Date: Int] = [:]
    let isCurrentCycleComplete: Bool = false
    let hasTodayQuotaMet: Bool = false
    let canPerformRepetition: Bool = true
    let createdAt: Date = Date()
    let updatedAt: Date
    var longestStreak: Int = 0

    init(
        id: UUID,
        text: String,
        category: AffirmationCategory,
        isFavorite: Bool,
        recordingURL: URL?,
        currentRepetitions: Int,
        completedCycles: Int,
        energyLevel: Double,
        notes: String?,
        updatedAt: Date
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.isFavorite = isFavorite
        self.recordingURL = recordingURL
        self.hasRecording = recordingURL != nil
        self.currentRepetitions = currentRepetitions
        self.completedCycles = completedCycles
        self.energyLevel = energyLevel
        self.notes = notes
        self.updatedAt = updatedAt
    }

    func recordRepetition() throws {
        // No-op for merged affirmation
    }

    func updateEnergyLevel(_ level: Double) {
        // No-op for merged affirmation
    }

    func recordMood(_ rating: Int, notes: String?) {
        // No-op for merged affirmation
    }
}
