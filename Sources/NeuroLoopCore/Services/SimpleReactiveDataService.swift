import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Simple reactive data service for Phase 2 implementation
@MainActor
public final class SimpleReactiveDataService: ObservableObject {
    
    // MARK: - Singleton
    
    public static let shared = SimpleReactiveDataService()
    
    // MARK: - Published Properties
    
    /// All affirmations with real-time updates
    @Published public private(set) var affirmations: [any AffirmationProtocol] = []
    
    /// Current active affirmation
    @Published public private(set) var currentAffirmation: (any AffirmationProtocol)? = nil
    
    /// Loading state
    @Published public private(set) var isLoading = false
    
    // MARK: - Private Properties
    
    private var repository: AffirmationRepositoryProtocol?
    
    // MARK: - Initialization
    
    private init() {
        print("[SimpleReactiveDataService] Initialized")
    }
    
    // MARK: - Repository Setup
    
    public func setRepository(_ repository: AffirmationRepositoryProtocol) {
        self.repository = repository
        print("[SimpleReactiveDataService] Repository set")
        
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Data Loading
    
    /// Load initial data from repository
    public func loadInitialData() async {
        guard let repository = repository else {
            print("[SimpleReactiveDataService] No repository set")
            return
        }
        
        isLoading = true
        
        do {
            // Load all affirmations
            let loadedAffirmations = try await repository.fetchAffirmations()
            affirmations = loadedAffirmations
            
            // Load current affirmation
            currentAffirmation = try await repository.fetchCurrentAffirmation()
            
            print("[SimpleReactiveDataService] Loaded \(loadedAffirmations.count) affirmations")
            
        } catch {
            print("[SimpleReactiveDataService] Error loading data: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - Data Operations
    
    /// Create a new affirmation
    public func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil
    ) async -> (any AffirmationProtocol)? {
        guard let repository = repository else { return nil }
        
        do {
            let newAffirmation = try await repository.createAffirmation(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
            
            // Update local state
            affirmations.append(newAffirmation)
            
            print("[SimpleReactiveDataService] Created affirmation: \(newAffirmation.id)")
            return newAffirmation
            
        } catch {
            print("[SimpleReactiveDataService] Error creating affirmation: \(error)")
            return nil
        }
    }
    
    /// Update an affirmation
    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async -> Bool {
        guard let repository = repository else { return false }
        
        do {
            try await repository.updateAffirmation(affirmation)
            
            // Update local state
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = affirmation
            }
            
            // Update current affirmation if it's the same
            if currentAffirmation?.id == affirmation.id {
                currentAffirmation = affirmation
            }
            
            print("[SimpleReactiveDataService] Updated affirmation: \(affirmation.id)")
            return true
            
        } catch {
            print("[SimpleReactiveDataService] Error updating affirmation: \(error)")
            return false
        }
    }
    
    /// Delete an affirmation
    public func deleteAffirmation(id: UUID) async -> Bool {
        guard let repository = repository else { return false }
        
        do {
            try await repository.deleteAffirmation(id: id)
            
            // Update local state
            affirmations.removeAll { $0.id == id }
            
            // Clear current affirmation if it was deleted
            if currentAffirmation?.id == id {
                currentAffirmation = nil
            }
            
            print("[SimpleReactiveDataService] Deleted affirmation: \(id)")
            return true
            
        } catch {
            print("[SimpleReactiveDataService] Error deleting affirmation: \(error)")
            return false
        }
    }
    
    /// Set the current active affirmation
    public func setCurrentAffirmation(_ affirmation: (any AffirmationProtocol)?) {
        currentAffirmation = affirmation
        
        if let affirmation = affirmation {
            print("[SimpleReactiveDataService] Current affirmation set to: \(affirmation.id)")
        } else {
            print("[SimpleReactiveDataService] Current affirmation cleared")
        }
    }
    
    /// Record a repetition
    public func recordRepetition(for affirmation: any AffirmationProtocol) async -> Bool {
        guard let repository = repository else { return false }
        
        do {
            try await repository.recordRepetition(for: affirmation)
            
            // Update local state
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = affirmation
            }
            
            // Update current affirmation if it's the same
            if currentAffirmation?.id == affirmation.id {
                currentAffirmation = affirmation
            }
            
            print("[SimpleReactiveDataService] Recorded repetition for: \(affirmation.id)")
            return true
            
        } catch {
            print("[SimpleReactiveDataService] Error recording repetition: \(error)")
            return false
        }
    }
    
    /// Refresh all data
    public func refreshData() async {
        await loadInitialData()
    }
}
