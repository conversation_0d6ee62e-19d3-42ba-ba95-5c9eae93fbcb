import Foundation
import NeuroLoopInterfaces

/// Represents a data change that needs to be synchronized
public class SyncChange: Identifiable, @unchecked Sendable {
    
    // MARK: - Properties
    
    public let id: UUID
    public let type: ChangeType
    public let entityId: UUID
    public let entityType: String
    public let data: Any?
    public let timestamp: Date
    public let priority: Priority
    public var status: Status = .pending
    public var retryCount: Int = 0
    public var lastAttempt: Date?
    public var errorMessage: String?
    
    // MARK: - Enums
    
    public enum ChangeType: String, CaseIterable, Codable {
        case create = "CREATE"
        case update = "UPDATE"
        case delete = "DELETE"
        case merge = "MERGE"
    }
    
    public enum Priority: Int, CaseIterable, Comparable {
        case low = 0
        case normal = 1
        case high = 2
        case critical = 3
        
        public static func < (lhs: Priority, rhs: Priority) -> Bool {
            lhs.rawValue < rhs.rawValue
        }
    }
    
    public enum Status: String, CaseIterable {
        case pending = "PENDING"
        case syncing = "SYNCING"
        case synced = "SYNCED"
        case failed = "FAILED"
        case conflict = "CONFLICT"
    }
    
    // MARK: - Initialization
    
    public init(
        id: UUID = UUID(),
        type: ChangeType,
        entityId: UUID,
        entityType: String,
        data: Any?,
        timestamp: Date = Date(),
        priority: Priority = .normal
    ) {
        self.id = id
        self.type = type
        self.entityId = entityId
        self.entityType = entityType
        self.data = data
        self.timestamp = timestamp
        self.priority = priority
    }
    
    // MARK: - Methods
    
    /// Mark the change as failed with error message
    public func markAsFailed(error: String) {
        status = .failed
        errorMessage = error
        lastAttempt = Date()
        retryCount += 1
    }
    
    /// Mark the change as successfully synced
    public func markAsSynced() {
        status = .synced
        lastAttempt = Date()
        errorMessage = nil
    }
    
    /// Mark the change as having a conflict
    public func markAsConflict(message: String) {
        status = .conflict
        errorMessage = message
        lastAttempt = Date()
    }
    
    /// Check if the change can be retried
    public func canRetry(maxRetries: Int = 3) -> Bool {
        return retryCount < maxRetries && status == .failed
    }
    
    /// Get age of the change in seconds
    public var ageInSeconds: TimeInterval {
        return Date().timeIntervalSince(timestamp)
    }
    
    /// Check if the change is stale (older than threshold)
    public func isStale(threshold: TimeInterval = 3600) -> Bool {
        return ageInSeconds > threshold
    }
    
    /// Create a delta representation of the change
    public func createDelta() -> SyncDelta? {
        guard let data = data else { return nil }
        
        return SyncDelta(
            changeId: id,
            entityId: entityId,
            entityType: entityType,
            changeType: type,
            fieldChanges: extractFieldChanges(from: data),
            timestamp: timestamp
        )
    }
    
    private func extractFieldChanges(from data: Any) -> [String: Any] {
        // Implementation for extracting field-level changes
        // This would use reflection or protocol-based field extraction
        var changes: [String: Any] = [:]

        // For now, return basic change information
        // In a real implementation, this would extract specific fields
        changes["entityId"] = entityId.uuidString
        changes["entityType"] = entityType
        changes["changeType"] = type.rawValue
        changes["timestamp"] = timestamp

        return changes
    }
}

// MARK: - SyncDelta

/// Represents a delta change for efficient synchronization
public struct SyncDelta: Codable, Identifiable {
    public let id = UUID()
    public let changeId: UUID
    public let entityId: UUID
    public let entityType: String
    public let changeType: SyncChange.ChangeType
    public let fieldChanges: [String: CodableValue]
    public let timestamp: Date
    
    init(
        changeId: UUID,
        entityId: UUID,
        entityType: String,
        changeType: SyncChange.ChangeType,
        fieldChanges: [String: Any],
        timestamp: Date
    ) {
        self.changeId = changeId
        self.entityId = entityId
        self.entityType = entityType
        self.changeType = changeType
        self.timestamp = timestamp
        
        // Convert Any values to CodableValue
        self.fieldChanges = fieldChanges.compactMapValues { value in
            CodableValue(value)
        }
    }
}

// MARK: - CodableValue

/// Wrapper for making Any values Codable
public enum CodableValue: Codable {
    case string(String)
    case int(Int)
    case double(Double)
    case bool(Bool)
    case date(Date)
    case null
    
    init?(_ value: Any) {
        switch value {
        case let string as String:
            self = .string(string)
        case let int as Int:
            self = .int(int)
        case let double as Double:
            self = .double(double)
        case let bool as Bool:
            self = .bool(bool)
        case let date as Date:
            self = .date(date)
        case Optional<Any>.none:
            self = .null
        default:
            return nil
        }
    }
    
    var value: Any? {
        switch self {
        case .string(let string):
            return string
        case .int(let int):
            return int
        case .double(let double):
            return double
        case .bool(let bool):
            return bool
        case .date(let date):
            return date
        case .null:
            return nil
        }
    }
}

// MARK: - SyncConflict

/// Represents a synchronization conflict
public struct SyncConflict: Identifiable {
    public let id = UUID()
    public let changeId: UUID
    public let entityId: UUID
    public let entityType: String
    public let localVersion: Any
    public let remoteVersion: Any
    public let conflictType: ConflictType
    public let detectedAt: Date
    public var resolved: Bool = false
    public var resolution: Any?
    
    public enum ConflictType {
        case dataConflict
        case versionConflict
        case deletionConflict
        case creationConflict
    }
    
    public init(
        changeId: UUID,
        entityId: UUID,
        entityType: String,
        localVersion: Any,
        remoteVersion: Any,
        conflictType: ConflictType
    ) {
        self.changeId = changeId
        self.entityId = entityId
        self.entityType = entityType
        self.localVersion = localVersion
        self.remoteVersion = remoteVersion
        self.conflictType = conflictType
        self.detectedAt = Date()
    }
}

// MARK: - Extensions

extension Array where Element == SyncChange {
    /// Group changes by entity type
    func groupedByEntityType() -> [String: [SyncChange]] {
        return Dictionary(grouping: self) { $0.entityType }
    }
    
    /// Filter changes by status
    func filtered(by status: SyncChange.Status) -> [SyncChange] {
        return filter { $0.status == status }
    }
    
    /// Sort changes by priority and timestamp
    func sortedByPriority() -> [SyncChange] {
        return sorted { lhs, rhs in
            if lhs.priority != rhs.priority {
                return lhs.priority > rhs.priority
            }
            return lhs.timestamp < rhs.timestamp
        }
    }
    
    /// Get changes that can be retried
    func retriableChanges(maxRetries: Int = 3) -> [SyncChange] {
        return filter { $0.canRetry(maxRetries: maxRetries) }
    }
    
    /// Get stale changes
    func staleChanges(threshold: TimeInterval = 3600) -> [SyncChange] {
        return filter { $0.isStale(threshold: threshold) }
    }
    
    /// Split into chunks of specified size
    func chunked(into size: Int) -> [[SyncChange]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

extension SyncChange: CustomStringConvertible {
    public var description: String {
        return """
        SyncChange(
            id: \(id),
            type: \(type.rawValue),
            entityType: \(entityType),
            entityId: \(entityId),
            status: \(status.rawValue),
            priority: \(priority),
            timestamp: \(timestamp),
            retryCount: \(retryCount)
        )
        """
    }
}

extension SyncChange: Equatable {
    public static func == (lhs: SyncChange, rhs: SyncChange) -> Bool {
        return lhs.id == rhs.id
    }
}

extension SyncChange: Hashable {
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
