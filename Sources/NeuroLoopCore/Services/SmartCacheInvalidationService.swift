import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

#if os(iOS)
import UIKit
#endif

/// Smart cache invalidation service with event-driven and dependency-based invalidation
@MainActor
public final class SmartCacheInvalidationService: ObservableObject {
    
    // MARK: - Singleton
    
    public static let shared = SmartCacheInvalidationService()
    
    // MARK: - Properties
    
    private let cacheManager: MultiLevelCacheManager
    private var cancellables = Set<AnyCancellable>()
    private var dependencyGraph: [String: Set<String>] = [:]
    private var invalidationRules: [InvalidationRule] = []
    
    @Published public private(set) var invalidationStats = InvalidationStatistics()
    
    // MARK: - Invalidation Rule
    
    public struct InvalidationRule {
        let trigger: InvalidationTrigger
        let targets: [String] // Cache key patterns
        let delay: TimeInterval? // Optional delay before invalidation
        let condition: (() -> Bool)? // Optional condition check
        
        public init(
            trigger: InvalidationTrigger,
            targets: [String],
            delay: TimeInterval? = nil,
            condition: (() -> Bool)? = nil
        ) {
            self.trigger = trigger
            self.targets = targets
            self.delay = delay
            self.condition = condition
        }
    }
    
    // MARK: - Invalidation Trigger
    
    public enum InvalidationTrigger {
        case dataChanged(String) // Specific data type changed
        case timeExpired(TimeInterval) // Time-based expiration
        case userAction(String) // User performed specific action
        case systemEvent(String) // System event occurred
        case dependencyChanged(String) // Dependency changed
        case memoryPressure // System memory pressure
        case appBackground // App went to background
        case custom(String) // Custom trigger
    }
    
    // MARK: - Statistics
    
    public struct InvalidationStatistics {
        var totalInvalidations: Int = 0
        var ruleBasedInvalidations: Int = 0
        var dependencyInvalidations: Int = 0
        var timeBasedInvalidations: Int = 0
        var memoryPressureInvalidations: Int = 0
        var lastInvalidationDate: Date?
        
        var averageInvalidationsPerDay: Double {
            guard let lastDate = lastInvalidationDate else { return 0.0 }
            let daysSinceFirst = Date().timeIntervalSince(lastDate) / 86400
            guard daysSinceFirst > 0 else { return 0.0 }
            return Double(totalInvalidations) / daysSinceFirst
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        self.cacheManager = MultiLevelCacheManager.shared
        
        setupDefaultRules()
        setupEventListeners()
        setupMemoryPressureMonitoring()
        
        print("[SmartCacheInvalidationService] Initialized with smart invalidation rules")
    }
    
    // MARK: - Public API
    
    /// Add a cache dependency relationship
    public func addDependency(from dependent: String, to dependency: String) {
        if dependencyGraph[dependency] == nil {
            dependencyGraph[dependency] = Set<String>()
        }
        dependencyGraph[dependency]?.insert(dependent)
        
        print("[SmartCacheInvalidationService] Added dependency: \(dependent) depends on \(dependency)")
    }
    
    /// Add a custom invalidation rule
    public func addRule(_ rule: InvalidationRule) {
        invalidationRules.append(rule)
        print("[SmartCacheInvalidationService] Added invalidation rule for trigger: \(rule.trigger)")
    }
    
    /// Trigger invalidation for specific data change
    public func invalidateForDataChange(_ dataType: String) async {
        await processInvalidation(trigger: .dataChanged(dataType))
        invalidationStats.ruleBasedInvalidations += 1
        updateStats()
    }
    
    /// Trigger invalidation for user action
    public func invalidateForUserAction(_ action: String) async {
        await processInvalidation(trigger: .userAction(action))
        invalidationStats.ruleBasedInvalidations += 1
        updateStats()
    }
    
    /// Trigger invalidation for system event
    public func invalidateForSystemEvent(_ event: String) async {
        await processInvalidation(trigger: .systemEvent(event))
        invalidationStats.ruleBasedInvalidations += 1
        updateStats()
    }
    
    /// Invalidate based on dependency changes
    public func invalidateDependencies(for key: String) async {
        guard let dependents = dependencyGraph[key] else { return }
        
        print("[SmartCacheInvalidationService] Invalidating \(dependents.count) dependents of \(key)")
        
        for dependent in dependents {
            await cacheManager.remove(forKey: dependent)
            
            // Recursively invalidate dependencies
            await invalidateDependencies(for: dependent)
        }
        
        invalidationStats.dependencyInvalidations += dependents.count
        updateStats()
    }
    
    /// Perform time-based invalidation cleanup
    public func performTimeBasedInvalidation() async {
        let expiredKeys = await identifyExpiredKeys()
        
        for key in expiredKeys {
            await cacheManager.remove(forKey: key)
        }
        
        invalidationStats.timeBasedInvalidations += expiredKeys.count
        updateStats()
        
        print("[SmartCacheInvalidationService] Performed time-based invalidation for \(expiredKeys.count) keys")
    }
    
    /// Handle memory pressure by invalidating low-priority cache entries
    public func handleMemoryPressure() async {
        let lowPriorityKeys = await identifyLowPriorityKeys()
        
        for key in lowPriorityKeys {
            await cacheManager.remove(forKey: key)
        }
        
        invalidationStats.memoryPressureInvalidations += lowPriorityKeys.count
        updateStats()
        
        print("[SmartCacheInvalidationService] Handled memory pressure, invalidated \(lowPriorityKeys.count) low-priority entries")
    }
    
    // MARK: - Private Methods
    
    private func processInvalidation(trigger: InvalidationTrigger) async {
        let matchingRules = invalidationRules.filter { rule in
            switch (rule.trigger, trigger) {
            case (.dataChanged(let ruleData), .dataChanged(let triggerData)):
                return ruleData == triggerData
            case (.userAction(let ruleAction), .userAction(let triggerAction)):
                return ruleAction == triggerAction
            case (.systemEvent(let ruleEvent), .systemEvent(let triggerEvent)):
                return ruleEvent == triggerEvent
            case (.memoryPressure, .memoryPressure):
                return true
            case (.appBackground, .appBackground):
                return true
            default:
                return false
            }
        }
        
        for rule in matchingRules {
            // Check condition if present
            if let condition = rule.condition, !condition() {
                continue
            }
            
            // Apply delay if specified
            if let delay = rule.delay {
                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
            
            // Invalidate targets
            for target in rule.targets {
                if target.contains("*") {
                    await invalidatePattern(target)
                } else {
                    await cacheManager.remove(forKey: target)
                }
            }
        }
    }
    
    private func invalidatePattern(_ pattern: String) async {
        // Simple pattern matching for cache keys
        // In a real implementation, this would be more sophisticated
        let regex = pattern.replacingOccurrences(of: "*", with: ".*")
        
        // This is a simplified implementation
        // In practice, you'd need to track all cache keys to match against patterns
        print("[SmartCacheInvalidationService] Invalidating pattern: \(pattern)")
    }
    
    private func identifyExpiredKeys() async -> [String] {
        // This would typically query the cache manager for expired keys
        // Simplified implementation
        return []
    }
    
    private func identifyLowPriorityKeys() async -> [String] {
        // This would identify keys based on access patterns, size, etc.
        // Simplified implementation
        return []
    }
    
    private func setupDefaultRules() {
        // Affirmation data changes
        addRule(InvalidationRule(
            trigger: .dataChanged("affirmation"),
            targets: ["affirmation_*", "category_*", "user_stats"]
        ))
        
        // User preferences changes
        addRule(InvalidationRule(
            trigger: .dataChanged("preferences"),
            targets: ["user_preferences", "theme_*", "settings_*"]
        ))
        
        // Repetition recorded
        addRule(InvalidationRule(
            trigger: .userAction("repetition_recorded"),
            targets: ["analytics_*", "progress_*", "streak_*"]
        ))
        
        // App background
        addRule(InvalidationRule(
            trigger: .appBackground,
            targets: ["temporary_*"],
            delay: 300 // 5 minutes after going to background
        ))
        
        // Memory pressure
        addRule(InvalidationRule(
            trigger: .memoryPressure,
            targets: ["large_data_*", "images_*", "audio_*"]
        ))
    }
    
    private func setupEventListeners() {
        // Listen for data change notifications
        NotificationCenter.default.publisher(for: Notification.Name("AffirmationDataChanged"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.invalidateForDataChange("affirmation")
                }
            }
            .store(in: &cancellables)
        
        // Listen for user preference changes
        NotificationCenter.default.publisher(for: Notification.Name("UserPreferencesChanged"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.invalidateForDataChange("preferences")
                }
            }
            .store(in: &cancellables)
        
        // Listen for app lifecycle events
        #if os(iOS)
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.invalidateForSystemEvent("app_background")
                }
            }
            .store(in: &cancellables)
        #endif
    }
    
    private func setupMemoryPressureMonitoring() {
        #if os(iOS)
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleMemoryPressure()
                }
            }
            .store(in: &cancellables)
        #endif
    }
    
    private func updateStats() {
        invalidationStats.totalInvalidations += 1
        invalidationStats.lastInvalidationDate = Date()
    }
}
