import CloudKit
import Combine
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import StoreKit
import SwiftData

// Internal dependencies: ReactiveDataManager, CachedAffirmationRepository

/// Async-optimized service factory with loading states and background initialization
@MainActor
public final class AsyncServiceFactory: ObservableObject, @unchecked Sendable {
    // MARK: - Shared Instance
    
    public static let shared = AsyncServiceFactory()
    
    // MARK: - Loading States
    
    @Published public private(set) var isInitializing = false
    @Published public private(set) var initializationProgress: Double = 0.0
    @Published public private(set) var currentInitializationStep = ""
    @Published public private(set) var isReady = false
    
    // MARK: - Private Properties
    
    private var affirmationRepository: AffirmationRepositoryProtocol?
    private var affirmationService: AffirmationService?
    private var repetitionService: RepetitionService?
    private var streakService: StreakService?
    private var audioRecordingService: AudioRecordingService?
    private var syncService: CloudKitSyncService?
    private var purchaseManager: PurchaseManagerProtocol?
    private var dataExportService: DataExportServiceProtocol?
    private var journalRepository: JournalRepositoryProtocol?

    // Simple reactive data properties
    @Published public private(set) var reactiveAffirmations: [any AffirmationProtocol] = []
    @Published public private(set) var reactiveCurrentAffirmation: (any AffirmationProtocol)? = nil

    // Advanced caching services
    private var multiLevelCacheManager: MultiLevelCacheManager?
    private var cacheWarmingService: CacheWarmingService?
    private var cacheInvalidationService: SmartCacheInvalidationService?
    private var cachePerformanceMonitor: CachePerformanceMonitor?
    
    private var initializationTask: Task<Void, Never>?
    private let initializationSteps = [
        "Initializing data layer...",
        "Setting up affirmation services...",
        "Configuring audio services...",
        "Preparing background services...",
        "Finalizing setup..."
    ]
    
    // MARK: - Initialization
    
    private init() {
        print("[AsyncServiceFactory] Initializing optimized service factory")
    }
    
    // MARK: - Public Async Initialization
    
    /// Initialize all services asynchronously with progress reporting
    public func initialize() async {
        guard initializationTask == nil else {
            await initializationTask?.value
            return
        }
        
        initializationTask = Task { @MainActor in
            await performInitialization()
        }
        
        await initializationTask?.value
    }
    
    /// Initialize critical services only (for faster app launch)
    public func initializeCriticalServices() async {
        guard !isReady else { return }
        
        isInitializing = true
        currentInitializationStep = "Loading essential services..."
        initializationProgress = 0.0
        
        do {
            // Initialize only critical services for immediate app functionality
            let repository = try await getAffirmationRepository()
            initializationProgress = 0.2

            // Setup reactive data with repository
            currentInitializationStep = "Setting up reactive data..."
            await loadReactiveData(from: repository)

            // Initialize advanced caching system
            currentInitializationStep = "Initializing advanced caching..."
            await initializeAdvancedCaching()
            initializationProgress = 0.4

            _ = try await getAffirmationService()
            initializationProgress = 0.6

            _ = try await getRepetitionService()
            initializationProgress = 0.8

            _ = await getAudioRecordingService()
            initializationProgress = 1.0

            currentInitializationStep = "Ready!"
            isReady = true
            
            print("[AsyncServiceFactory] Critical services initialized successfully")
            
            // Initialize non-critical services in background
            Task {
                await initializeNonCriticalServices()
            }
            
        } catch {
            print("[AsyncServiceFactory] Error initializing critical services: \(error)")
            currentInitializationStep = "Initialization failed"
        }
        
        isInitializing = false
    }
    
    // MARK: - Private Implementation
    
    private func performInitialization() async {
        isInitializing = true
        initializationProgress = 0.0
        
        let stepProgress = 1.0 / Double(initializationSteps.count)
        
        for (index, step) in initializationSteps.enumerated() {
            currentInitializationStep = step
            
            switch index {
            case 0:
                do {
                    let repository = try await getAffirmationRepository()
                    // Setup reactive data with repository
                    await loadReactiveData(from: repository)
                } catch {
                    print("[AsyncServiceFactory] Repository initialization failed: \(error)")
                }
                
            case 1:
                do {
                    _ = try await getAffirmationService()
                    _ = try await getRepetitionService()
                    _ = try await getStreakService()
                } catch {
                    print("[AsyncServiceFactory] Service initialization failed: \(error)")
                }
                
            case 2:
                _ = await getAudioRecordingService()
                
            case 3:
                await initializeNonCriticalServices()
                
            case 4:
                // Final setup
                break
            default:
                // Handle any additional steps
                break
            }
            
            initializationProgress = Double(index + 1) * stepProgress
            
            // Small delay to show progress (remove in production if needed)
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        }
        
        currentInitializationStep = "Ready!"
        isReady = true
        isInitializing = false
        
        print("[AsyncServiceFactory] Full initialization completed")
    }
    
    private func initializeNonCriticalServices() async {
        // Initialize services that aren't needed for immediate app functionality
        _ = getSyncService()
        
        if #available(iOS 17.0, macOS 14.0, *) {
            _ = getPurchaseManager()
            _ = getDataExportService()
        }
        
        _ = getJournalRepository()
        
        print("[AsyncServiceFactory] Non-critical services initialized")
    }

    // MARK: - Reactive Data Management

    /// Load reactive data from repository
    private func loadReactiveData(from repository: AffirmationRepositoryProtocol) async {
        do {
            // Load affirmations reactively
            let affirmations = try await repository.fetchAffirmations()
            reactiveAffirmations = affirmations

            // Load current affirmation
            reactiveCurrentAffirmation = try await repository.fetchCurrentAffirmation()

            print("[AsyncServiceFactory] Loaded \(affirmations.count) affirmations reactively")

        } catch {
            print("[AsyncServiceFactory] Error loading reactive data: \(error)")
        }
    }

    /// Update reactive affirmations
    public func updateReactiveAffirmations(_ affirmations: [any AffirmationProtocol]) {
        reactiveAffirmations = affirmations
    }

    /// Set reactive current affirmation
    public func setReactiveCurrentAffirmation(_ affirmation: (any AffirmationProtocol)?) {
        reactiveCurrentAffirmation = affirmation
    }

    // MARK: - Advanced Caching Management

    /// Initialize advanced caching system
    private func initializeAdvancedCaching() async {
        // Initialize multi-level cache manager
        multiLevelCacheManager = MultiLevelCacheManager.shared

        // Initialize cache warming service
        cacheWarmingService = CacheWarmingService.shared

        // Initialize smart cache invalidation
        cacheInvalidationService = SmartCacheInvalidationService.shared

        // Initialize performance monitor
        cachePerformanceMonitor = CachePerformanceMonitor.shared

        // Start performance monitoring
        cachePerformanceMonitor?.startMonitoring()

        // Warm cache with initial data
        if !reactiveAffirmations.isEmpty {
            await cacheWarmingService?.warmAffirmations(reactiveAffirmations)
        }

        print("[AsyncServiceFactory] Advanced caching system initialized")
    }

    /// Get multi-level cache manager
    public func getMultiLevelCacheManager() -> MultiLevelCacheManager {
        return multiLevelCacheManager ?? MultiLevelCacheManager.shared
    }

    /// Get cache warming service
    public func getCacheWarmingService() -> CacheWarmingService {
        return cacheWarmingService ?? CacheWarmingService.shared
    }

    /// Get cache invalidation service
    public func getCacheInvalidationService() -> SmartCacheInvalidationService {
        return cacheInvalidationService ?? SmartCacheInvalidationService.shared
    }

    /// Get cache performance monitor
    public func getCachePerformanceMonitor() -> CachePerformanceMonitor {
        return cachePerformanceMonitor ?? CachePerformanceMonitor.shared
    }
    
    // MARK: - Service Getters (Async Optimized)
    
    /// Get the affirmation repository (async with caching)
    public func getAffirmationRepository() async throws -> AffirmationRepositoryProtocol {
        if let repo = affirmationRepository {
            return repo
        }

        // Create underlying repository
        let underlyingRepo = await withCheckedContinuation { continuation in
            Task {
                let repository = MemoryStorageService()
                continuation.resume(returning: repository)
            }
        }

        // Temporarily use underlying repository directly to avoid module issues
        // TODO: Re-enable caching when NeuroLoopInterfaces module is properly configured
        // let cachedRepo = CachedAffirmationRepository(
        //     repository: underlyingRepo,
        //     maxCacheSize: 50
        // )

        affirmationRepository = underlyingRepo
        print("[AsyncServiceFactory] Created affirmation repository (caching temporarily disabled)")
        return underlyingRepo
    }
    
    /// Get the affirmation service (async)
    public func getAffirmationService() async throws -> AffirmationService {
        if let service = affirmationService {
            return service
        }
        
        let repository = try await getAffirmationRepository()
        let service = AffirmationService(repository: repository)
        affirmationService = service
        return service
    }
    
    /// Get the repetition service (async)
    public func getRepetitionService() async throws -> RepetitionService {
        if let service = repetitionService {
            return service
        }
        
        let affirmationService = try await getAffirmationService()
        let service = RepetitionService(affirmationService: affirmationService)
        repetitionService = service
        return service
    }
    
    /// Get the streak service (async)
    public func getStreakService() async throws -> StreakService {
        if let service = streakService {
            return service
        }
        
        let repository = try await getAffirmationRepository()
        let service = StreakService(repository: repository)
        streakService = service
        return service
    }
    
    /// Get the audio recording service (async)
    public func getAudioRecordingService() async -> AudioRecordingService {
        if let service = audioRecordingService {
            return service
        }
        
        // Pre-warm audio service in background
        let service = await withCheckedContinuation { continuation in
            Task {
                #if os(iOS)
                    let audioService = AudioRecordingService(
                        audioFileManager: NeuroLoopInterfaces.AudioFileManager.shared)
                #else
                    let audioService = AudioRecordingService()
                #endif
                continuation.resume(returning: audioService)
            }
        }
        
        audioRecordingService = service
        return service
    }
    
    // MARK: - Synchronous Getters (for backward compatibility)
    
    /// Get services synchronously (throws if not initialized)
    public func getServicesSync() throws -> (
        AffirmationService, RepetitionService, StreakService, AudioRecordingService
    ) {
        guard isReady,
              let affirmationService = affirmationService,
              let repetitionService = repetitionService,
              let streakService = streakService,
              let audioRecordingService = audioRecordingService else {
            throw ServiceFactoryError.notInitialized
        }
        
        return (affirmationService, repetitionService, streakService, audioRecordingService)
    }
    
    // MARK: - Non-Critical Services (Sync)
    
    public func getSyncService() -> CloudKitSyncService? {
        if let service = syncService {
            return service
        }
        #if DEBUG
            if !ServiceFactory.cloudKitAvailable {
                return nil
            }
        #endif
        let service = CloudKitSyncService.shared()
        syncService = service
        return service
    }
    
    @available(iOS 17.0, macOS 14.0, *)
    public func getPurchaseManager() -> PurchaseManagerSendable {
        if let manager = purchaseManager as? PurchaseManagerSendable {
            return manager
        }
        let manager = PurchaseManager.shared
        purchaseManager = manager
        return manager
    }
    
    @available(iOS 17.0, macOS 14.0, *)
    public func getDataExportService() -> DataExportServiceProtocol {
        if let service = dataExportService {
            return service
        }
        #if DEBUG
            if !ServiceFactory.cloudKitAvailable {
                let service = MockDataExportService()
                dataExportService = service
                return service
            }
        #endif
        let service = CloudKitDataExportService()
        dataExportService = service
        return service
    }
    
    public func getJournalRepository() -> JournalRepositoryProtocol {
        if let repository = journalRepository {
            return repository
        }
        
        let repository = EmptyJournalRepository()
        journalRepository = repository
        return repository
    }
}

// MARK: - Error Types

public enum ServiceFactoryError: Error, LocalizedError {
    case notInitialized
    case initializationFailed(Error)
    
    public var errorDescription: String? {
        switch self {
        case .notInitialized:
            return "Services not initialized. Call initialize() first."
        case .initializationFailed(let error):
            return "Service initialization failed: \(error.localizedDescription)"
        }
    }
}
