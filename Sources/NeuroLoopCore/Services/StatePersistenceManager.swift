import Foundation
import SwiftUI

/// Manages persistence of view state data
@MainActor
public final class StatePersistenceManager: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public struct Configuration {
        let storageLocation: StorageLocation
        let encryptionEnabled: Bool
        let compressionEnabled: Bool
        let backupEnabled: Bool
        let maxBackupCount: Int
        
        public static let `default` = Configuration(
            storageLocation: .documents,
            encryptionEnabled: false,
            compressionEnabled: true,
            backupEnabled: true,
            maxBackupCount: 5
        )
    }
    
    public enum StorageLocation {
        case documents
        case applicationSupport
        case cache
        case userDefaults
        
        var url: URL? {
            let fileManager = FileManager.default
            switch self {
            case .documents:
                return fileManager.urls(for: .documentDirectory, in: .userDomainMask).first
            case .applicationSupport:
                return fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first
            case .cache:
                return fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first
            case .userDefaults:
                return nil // Special case for UserDefaults
            }
        }
    }
    
    // MARK: - Properties
    
    private let config: Configuration
    private let enabled: Bool
    private var persistenceStatistics = PersistenceStatistics()
    
    // MARK: - Statistics
    
    public struct PersistenceStatistics {
        var totalSaves: Int = 0
        var totalLoads: Int = 0
        var successfulSaves: Int = 0
        var successfulLoads: Int = 0
        var averageSaveTime: TimeInterval = 0.0
        var averageLoadTime: TimeInterval = 0.0
        var totalDataSaved: Int = 0
        var totalDataLoaded: Int = 0
        var compressionSavings: Int = 0
        
        var saveSuccessRate: Double {
            guard totalSaves > 0 else { return 0.0 }
            return Double(successfulSaves) / Double(totalSaves)
        }
        
        var loadSuccessRate: Double {
            guard totalLoads > 0 else { return 0.0 }
            return Double(successfulLoads) / Double(totalLoads)
        }
    }
    
    // MARK: - File Names
    
    private let stateFileName = "viewstate.json"
    private let backupPrefix = "viewstate_backup_"
    private let compressionExtension = ".compressed"
    
    // MARK: - Initialization
    
    public init(enabled: Bool = true, config: Configuration = .default) {
        self.enabled = enabled
        self.config = config
        
        if enabled {
            setupStorageDirectory()
        }
        
        print("[StatePersistenceManager] Initialized with storage: \(config.storageLocation), enabled: \(enabled)")
    }
    
    // MARK: - Public API
    
    /// Save state to persistent storage
    public func saveState<T: Codable>(_ state: T) async {
        guard enabled else { return }
        
        let startTime = Date()
        persistenceStatistics.totalSaves += 1
        
        do {
            let data = try await encodeState(state)
            let finalData = try await processDataForSaving(data)
            
            try await writeDataToStorage(finalData)
            
            if config.backupEnabled {
                try await createBackup(finalData)
            }
            
            let saveTime = Date().timeIntervalSince(startTime)
            recordSuccessfulSave(dataSize: finalData.count, saveTime: saveTime)
            
            print("[StatePersistenceManager] State saved successfully (\(finalData.count) bytes)")
            
        } catch {
            print("[StatePersistenceManager] Failed to save state: \(error)")
        }
    }
    
    /// Load state from persistent storage
    public func loadState<T: Codable>(_ type: T.Type) async -> T? {
        guard enabled else { return nil }
        
        let startTime = Date()
        persistenceStatistics.totalLoads += 1
        
        do {
            let data = try await readDataFromStorage()
            let processedData = try await processDataForLoading(data)
            let state = try await decodeState(type, from: processedData)
            
            let loadTime = Date().timeIntervalSince(startTime)
            recordSuccessfulLoad(dataSize: data.count, loadTime: loadTime)
            
            print("[StatePersistenceManager] State loaded successfully (\(data.count) bytes)")
            return state
            
        } catch {
            print("[StatePersistenceManager] Failed to load state: \(error)")
            
            // Try to load from backup
            if config.backupEnabled {
                return await loadFromBackup(type)
            }
            
            return nil
        }
    }
    
    /// Clear all persisted state
    public func clearState() async {
        guard enabled else { return }
        
        do {
            try await deleteStateFile()
            try await clearBackups()
            print("[StatePersistenceManager] State cleared successfully")
        } catch {
            print("[StatePersistenceManager] Failed to clear state: \(error)")
        }
    }
    
    /// Get persistence statistics
    public func getStatistics() -> PersistenceStatistics {
        return persistenceStatistics
    }
    
    /// Check if state exists
    public func stateExists() async -> Bool {
        guard enabled else { return false }
        
        if config.storageLocation == .userDefaults {
            return UserDefaults.standard.data(forKey: stateFileName) != nil
        } else {
            guard let url = getStateFileURL() else { return false }
            return FileManager.default.fileExists(atPath: url.path)
        }
    }
    
    /// Get state file size
    public func getStateFileSize() async -> Int {
        guard enabled, await stateExists() else { return 0 }
        
        if config.storageLocation == .userDefaults {
            return UserDefaults.standard.data(forKey: stateFileName)?.count ?? 0
        } else {
            guard let url = getStateFileURL() else { return 0 }
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
                return attributes[.size] as? Int ?? 0
            } catch {
                return 0
            }
        }
    }
    
    // MARK: - Private Implementation
    
    private func setupStorageDirectory() {
        guard config.storageLocation != .userDefaults,
              let baseURL = config.storageLocation.url else { return }
        
        let appDirectory = baseURL.appendingPathComponent("NeuroLoopApp")
        
        do {
            try FileManager.default.createDirectory(
                at: appDirectory,
                withIntermediateDirectories: true,
                attributes: nil
            )
        } catch {
            print("[StatePersistenceManager] Failed to create storage directory: \(error)")
        }
    }
    
    private func encodeState<T: Codable>(_ state: T) async throws -> Data {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        return try encoder.encode(state)
    }
    
    private func decodeState<T: Codable>(_ type: T.Type, from data: Data) async throws -> T {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(type, from: data)
    }
    
    private func processDataForSaving(_ data: Data) async throws -> Data {
        var processedData = data
        
        // Apply compression if enabled
        if config.compressionEnabled {
            let compressionManager = CompressionManager()
            if let compressedData = await compressionManager.compressData(data),
               compressedData.compressed {
                processedData = compressedData.dataToUse
                persistenceStatistics.compressionSavings += data.count - processedData.count
            }
        }
        
        // Apply encryption if enabled
        if config.encryptionEnabled {
            processedData = try await encryptData(processedData)
        }
        
        return processedData
    }
    
    private func processDataForLoading(_ data: Data) async throws -> Data {
        var processedData = data
        
        // Apply decryption if enabled
        if config.encryptionEnabled {
            processedData = try await decryptData(processedData)
        }
        
        // Apply decompression if enabled
        if config.compressionEnabled {
            let compressionManager = CompressionManager()
            let compressedData = CompressedData(
                originalData: data,
                compressedData: processedData,
                compressed: true
            )
            if let decompressedData = await compressionManager.decompressData(compressedData) {
                processedData = decompressedData
            }
        }
        
        return processedData
    }
    
    private func writeDataToStorage(_ data: Data) async throws {
        if config.storageLocation == .userDefaults {
            UserDefaults.standard.set(data, forKey: stateFileName)
        } else {
            guard let url = getStateFileURL() else {
                throw PersistenceError.invalidStorageLocation
            }
            try data.write(to: url)
        }
    }
    
    private func readDataFromStorage() async throws -> Data {
        if config.storageLocation == .userDefaults {
            guard let data = UserDefaults.standard.data(forKey: stateFileName) else {
                throw PersistenceError.fileNotFound
            }
            return data
        } else {
            guard let url = getStateFileURL() else {
                throw PersistenceError.invalidStorageLocation
            }
            return try Data(contentsOf: url)
        }
    }
    
    private func deleteStateFile() async throws {
        if config.storageLocation == .userDefaults {
            UserDefaults.standard.removeObject(forKey: stateFileName)
        } else {
            guard let url = getStateFileURL() else { return }
            try FileManager.default.removeItem(at: url)
        }
    }
    
    private func createBackup(_ data: Data) async throws {
        guard config.storageLocation != .userDefaults else { return }
        
        let timestamp = ISO8601DateFormatter().string(from: Date())
        let backupFileName = "\(backupPrefix)\(timestamp).json"
        
        guard let baseURL = config.storageLocation.url else { return }
        let backupURL = baseURL
            .appendingPathComponent("NeuroLoopApp")
            .appendingPathComponent(backupFileName)
        
        try data.write(to: backupURL)
        
        // Clean up old backups
        try await cleanupOldBackups()
    }
    
    private func cleanupOldBackups() async throws {
        guard let baseURL = config.storageLocation.url else { return }
        let appDirectory = baseURL.appendingPathComponent("NeuroLoopApp")
        
        let fileManager = FileManager.default
        let files = try fileManager.contentsOfDirectory(at: appDirectory, includingPropertiesForKeys: [.creationDateKey])
        
        let backupFiles = files.filter { $0.lastPathComponent.hasPrefix(backupPrefix) }
            .sorted { file1, file2 in
                let date1 = try? file1.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                let date2 = try? file2.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                return date1! > date2!
            }
        
        // Remove excess backups
        if backupFiles.count > config.maxBackupCount {
            for file in backupFiles.dropFirst(config.maxBackupCount) {
                try fileManager.removeItem(at: file)
            }
        }
    }
    
    private func clearBackups() async throws {
        guard let baseURL = config.storageLocation.url else { return }
        let appDirectory = baseURL.appendingPathComponent("NeuroLoopApp")
        
        let fileManager = FileManager.default
        let files = try fileManager.contentsOfDirectory(at: appDirectory, includingPropertiesForKeys: nil)
        
        for file in files where file.lastPathComponent.hasPrefix(backupPrefix) {
            try fileManager.removeItem(at: file)
        }
    }
    
    private func loadFromBackup<T: Codable>(_ type: T.Type) async -> T? {
        guard let baseURL = config.storageLocation.url else { return nil }
        let appDirectory = baseURL.appendingPathComponent("NeuroLoopApp")
        
        do {
            let fileManager = FileManager.default
            let files = try fileManager.contentsOfDirectory(at: appDirectory, includingPropertiesForKeys: [.creationDateKey])
            
            let backupFiles = files.filter { $0.lastPathComponent.hasPrefix(backupPrefix) }
                .sorted { file1, file2 in
                    let date1 = try? file1.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    let date2 = try? file2.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    return date1! > date2!
                }
            
            // Try to load from most recent backup
            for backupFile in backupFiles {
                do {
                    let data = try Data(contentsOf: backupFile)
                    let processedData = try await processDataForLoading(data)
                    let state = try await decodeState(type, from: processedData)
                    
                    print("[StatePersistenceManager] Loaded state from backup: \(backupFile.lastPathComponent)")
                    return state
                } catch {
                    print("[StatePersistenceManager] Failed to load from backup \(backupFile.lastPathComponent): \(error)")
                    continue
                }
            }
        } catch {
            print("[StatePersistenceManager] Failed to access backup directory: \(error)")
        }
        
        return nil
    }
    
    private func getStateFileURL() -> URL? {
        guard let baseURL = config.storageLocation.url else { return nil }
        return baseURL
            .appendingPathComponent("NeuroLoopApp")
            .appendingPathComponent(stateFileName)
    }
    
    private func encryptData(_ data: Data) async throws -> Data {
        // Placeholder for encryption implementation
        // In a real implementation, this would use CryptoKit or similar
        return data
    }
    
    private func decryptData(_ data: Data) async throws -> Data {
        // Placeholder for decryption implementation
        // In a real implementation, this would use CryptoKit or similar
        return data
    }
    
    private func recordSuccessfulSave(dataSize: Int, saveTime: TimeInterval) {
        persistenceStatistics.successfulSaves += 1
        persistenceStatistics.totalDataSaved += dataSize
        
        let totalSaves = persistenceStatistics.totalSaves
        let currentAverage = persistenceStatistics.averageSaveTime
        persistenceStatistics.averageSaveTime = (currentAverage * Double(totalSaves - 1) + saveTime) / Double(totalSaves)
    }
    
    private func recordSuccessfulLoad(dataSize: Int, loadTime: TimeInterval) {
        persistenceStatistics.successfulLoads += 1
        persistenceStatistics.totalDataLoaded += dataSize
        
        let totalLoads = persistenceStatistics.totalLoads
        let currentAverage = persistenceStatistics.averageLoadTime
        persistenceStatistics.averageLoadTime = (currentAverage * Double(totalLoads - 1) + loadTime) / Double(totalLoads)
    }
}

// MARK: - Persistence Errors

enum PersistenceError: Error {
    case invalidStorageLocation
    case fileNotFound
    case encryptionFailed
    case decryptionFailed
    case compressionFailed
    case decompressionFailed
}
