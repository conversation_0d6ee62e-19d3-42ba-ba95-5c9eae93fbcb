import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Enhanced optimistic update manager with advanced conflict resolution, retry mechanisms, and batch operations
@MainActor
public final class EnhancedOptimisticUpdateManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - Singleton
    
    public static let shared = EnhancedOptimisticUpdateManager()
    
    // MARK: - Properties
    
    private let dataManager = ReactiveDataManager.shared
    private let cacheManager = MultiLevelCacheManager.shared
    private var pendingOperations: [UUID: EnhancedPendingOperation] = [:]
    private var operationQueue: OperationQueue = OperationQueue()
    private var retryManager: RetryManager
    private var conflictResolver: ConflictResolver
    private var batchProcessor: BatchProcessor
    private var cancellables = Set<AnyCancellable>()
    
    /// Published state for enhanced monitoring
    @Published public private(set) var hasPendingOperations = false
    @Published public private(set) var pendingOperationCount = 0
    @Published public private(set) var operationStats = OperationStatistics()
    @Published public private(set) var batchOperations: [BatchOperation] = []
    
    // MARK: - Configuration
    
    public struct Configuration {
        let maxRetryAttempts: Int
        let baseRetryDelay: TimeInterval
        let maxConcurrentOperations: Int
        let batchTimeout: TimeInterval
        let conflictResolutionStrategy: ConflictResolutionStrategy
        
        public static let `default` = Configuration(
            maxRetryAttempts: 3,
            baseRetryDelay: 1.0,
            maxConcurrentOperations: 5,
            batchTimeout: 2.0,
            conflictResolutionStrategy: .lastWriteWins
        )
    }
    
    private let config: Configuration
    
    // MARK: - Enhanced Operation Types
    
    public enum OperationPriority: Int, CaseIterable {
        case low = 0
        case normal = 1
        case high = 2
        case critical = 3
        
        var weight: Double {
            switch self {
            case .low: return 0.25
            case .normal: return 1.0
            case .high: return 2.0
            case .critical: return 4.0
            }
        }
    }
    
    public typealias ConflictResolutionStrategy = ConflictResolver.ConflictResolutionStrategy
    
    private struct EnhancedPendingOperation {
        let id: UUID
        let operation: OperationType
        let priority: OperationPriority
        let timestamp: Date
        let retryCount: Int
        let originalState: Any?
        let dependencies: [UUID]
        
        enum OperationType {
            case createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?, tempId: UUID)
            case updateAffirmation(any AffirmationProtocol, originalState: any AffirmationProtocol)
            case deleteAffirmation(UUID, originalAffirmation: any AffirmationProtocol)
            case recordRepetition(UUID, originalCount: Int)
            case startCycle(UUID, originalState: any AffirmationProtocol)
            case batchOperation([OperationType])
        }
    }
    
    // MARK: - Statistics
    
    public struct OperationStatistics {
        var totalOperations: Int = 0
        var successfulOperations: Int = 0
        var failedOperations: Int = 0
        var retriedOperations: Int = 0
        var conflictsResolved: Int = 0
        var batchOperationsProcessed: Int = 0
        var averageOperationTime: TimeInterval = 0.0
        
        var successRate: Double {
            guard totalOperations > 0 else { return 0.0 }
            return Double(successfulOperations) / Double(totalOperations)
        }
        
        var retryRate: Double {
            guard totalOperations > 0 else { return 0.0 }
            return Double(retriedOperations) / Double(totalOperations)
        }
    }
    
    // MARK: - Initialization
    
    private init(config: Configuration = .default) {
        self.config = config
        self.retryManager = RetryManager(config: config)
        self.conflictResolver = ConflictResolver(strategy: config.conflictResolutionStrategy)
        self.batchProcessor = BatchProcessor(timeout: config.batchTimeout)
        
        setupOperationQueue()
        setupBatchProcessing()
        setupConflictDetection()
        
        print("[EnhancedOptimisticUpdateManager] Initialized with enhanced capabilities")
    }
    
    // MARK: - Enhanced Public API
    
    /// Create affirmation with enhanced optimistic update
    public func createAffirmationOptimistically(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil,
        priority: OperationPriority = .normal
    ) async -> (any AffirmationProtocol)? {
        let tempId = UUID()
        let operation = EnhancedPendingOperation.OperationType.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL,
            tempId: tempId
        )
        
        return await executeOptimisticOperation(
            id: tempId,
            operation: operation,
            priority: priority
        ) { [weak self] in
            await self?.performCreateAffirmation(text: text, category: category, recordingURL: recordingURL, tempId: tempId)
        }
    }
    
    /// Batch create multiple affirmations
    public func batchCreateAffirmationsOptimistically(
        affirmations: [(text: String, category: AffirmationCategory, recordingURL: URL?)],
        priority: OperationPriority = .normal
    ) async -> [any AffirmationProtocol] {
        let batchId = UUID()
        let operations = affirmations.map { affirmation in
            EnhancedPendingOperation.OperationType.createAffirmation(
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                tempId: UUID()
            )
        }
        
        let batchOperation = EnhancedPendingOperation.OperationType.batchOperation(operations)
        
        return await executeBatchOperation(
            id: batchId,
            operation: batchOperation,
            priority: priority
        )
    }
    
    /// Update affirmation with conflict detection
    public func updateAffirmationOptimistically(
        _ affirmation: any AffirmationProtocol,
        priority: OperationPriority = .normal
    ) async -> Bool {
        guard let originalAffirmation = dataManager.affirmations.first(where: { $0.id == affirmation.id }) else {
            return false
        }
        
        // Check for conflicts
        if await conflictResolver.detectConflict(current: affirmation, original: originalAffirmation) {
            let resolvedAffirmation = await conflictResolver.resolveConflict(
                current: affirmation,
                original: originalAffirmation,
                strategy: config.conflictResolutionStrategy
            )
            
            operationStats.conflictsResolved += 1
            
            return await performUpdateWithResolution(resolvedAffirmation, originalAffirmation, priority)
        }
        
        return await performUpdateWithResolution(affirmation, originalAffirmation, priority)
    }
    
    // MARK: - Private Implementation
    
    private func setupOperationQueue() {
        operationQueue.maxConcurrentOperationCount = config.maxConcurrentOperations
        operationQueue.qualityOfService = .userInitiated
    }
    
    private func setupBatchProcessing() {
        batchProcessor.onBatchReady = { [weak self] batch in
            await self?.processBatch(batch)
        }
    }
    
    private func setupConflictDetection() {
        // Set up real-time conflict detection
        dataManager.dataUpdatePublisher
            .sink { [weak self] event in
                Task { @MainActor in
                    await self?.handleDataUpdate(event)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Operation Execution

    private func executeOptimisticOperation<T>(
        id: UUID,
        operation: EnhancedPendingOperation.OperationType,
        priority: OperationPriority,
        execution: @escaping () async -> T?
    ) async -> T? {
        let pendingOp = EnhancedPendingOperation(
            id: id,
            operation: operation,
            priority: priority,
            timestamp: Date(),
            retryCount: 0,
            originalState: nil,
            dependencies: []
        )

        await addPendingOperation(pendingOp)

        // Execute optimistic UI update first
        await performOptimisticUIUpdate(for: operation)

        // Execute actual operation with retry logic
        return await retryManager.executeWithRetry(
            operation: execution,
            maxAttempts: config.maxRetryAttempts,
            baseDelay: config.baseRetryDelay
        ) { [weak self] success, result in
            await self?.handleOperationResult(id: id, success: success, result: result)
        }
    }

    private func executeBatchOperation(
        id: UUID,
        operation: EnhancedPendingOperation.OperationType,
        priority: OperationPriority
    ) async -> [any AffirmationProtocol] {
        let batchOp = BatchOperation(
            id: id,
            operations: [],
            priority: priority,
            timestamp: Date()
        )

        batchOperations.append(batchOp)

        return await batchProcessor.processBatch(batchOp)
    }

    private func performOptimisticUIUpdate(for operation: EnhancedPendingOperation.OperationType) async {
        switch operation {
        case .createAffirmation(let text, let category, let recordingURL, let tempId):
            let tempAffirmation = createTemporaryAffirmation(
                id: tempId,
                text: text,
                category: category,
                recordingURL: recordingURL
            )
            await dataManager.addTemporaryAffirmation(tempAffirmation)

        case .updateAffirmation(let affirmation, _):
            await dataManager.updateAffirmationOptimistically(affirmation)

        case .deleteAffirmation(let id, _):
            await dataManager.removeAffirmationOptimistically(id: id)

        case .recordRepetition(let id, _):
            await dataManager.incrementRepetitionCountOptimistically(for: id)

        case .startCycle(let id, _):
            await dataManager.startCycleOptimistically(for: id)

        case .batchOperation(let operations):
            for op in operations {
                await performOptimisticUIUpdate(for: op)
            }
        }
    }

    private func performCreateAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?,
        tempId: UUID
    ) async -> (any AffirmationProtocol)? {
        let result = await dataManager.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )

        if let actualAffirmation = result {
            await dataManager.replaceTemporaryAffirmation(
                tempId: tempId,
                with: actualAffirmation
            )
        }

        return result
    }

    private func performUpdateWithResolution(
        _ affirmation: any AffirmationProtocol,
        _ originalAffirmation: any AffirmationProtocol,
        _ priority: OperationPriority
    ) async -> Bool {
        let operation = EnhancedPendingOperation.OperationType.updateAffirmation(
            affirmation,
            originalState: originalAffirmation
        )

        return await executeOptimisticOperation(
            id: affirmation.id,
            operation: operation,
            priority: priority
        ) { [weak self] in
            await self?.dataManager.updateAffirmation(affirmation)
        } != nil
    }

    // MARK: - Operation Management

    private func addPendingOperation(_ operation: EnhancedPendingOperation) async {
        pendingOperations[operation.id] = operation
        updatePendingState()
        operationStats.totalOperations += 1
    }

    private func removePendingOperation(id: UUID) {
        pendingOperations.removeValue(forKey: id)
        updatePendingState()
    }

    private func updatePendingState() {
        pendingOperationCount = pendingOperations.count
        hasPendingOperations = !pendingOperations.isEmpty
    }

    private func handleOperationResult(id: UUID, success: Bool, result: Any?) async {
        defer {
            removePendingOperation(id: id)
        }

        if success {
            operationStats.successfulOperations += 1
            print("[EnhancedOptimisticUpdateManager] Operation succeeded: \(id)")
        } else {
            operationStats.failedOperations += 1
            await performRollback(for: id)
            print("[EnhancedOptimisticUpdateManager] Operation failed, rolled back: \(id)")
        }
    }

    private func performRollback(for operationId: UUID) async {
        guard let operation = pendingOperations[operationId] else { return }

        switch operation.operation {
        case .createAffirmation(_, _, _, let tempId):
            await dataManager.removeAffirmationOptimistically(id: tempId)

        case .updateAffirmation(_, let originalState):
            await dataManager.rollbackAffirmationChange(originalState)

        case .deleteAffirmation(let _, let originalAffirmation):
            await dataManager.rollbackAffirmationChange(originalAffirmation)

        case .recordRepetition(let id, let originalCount):
            await dataManager.rollbackRepetitionCount(for: id, to: originalCount)

        case .startCycle(let _, let originalState):
            await dataManager.rollbackAffirmationChange(originalState)

        case .batchOperation(let operations):
            for op in operations.reversed() {
                // Rollback in reverse order
                await performRollbackForOperation(op)
            }
        }
    }

    private func performRollbackForOperation(_ operation: EnhancedPendingOperation.OperationType) async {
        // Implementation for individual operation rollback
        switch operation {
        case .createAffirmation(_, _, _, let tempId):
            await dataManager.removeAffirmationOptimistically(id: tempId)
        default:
            break // Handle other cases as needed
        }
    }

    private func handleDataUpdate(_ event: ReactiveDataManager.DataUpdateEvent) async {
        // Handle real-time data updates for conflict detection
        switch event {
        case .affirmationUpdated(let affirmation):
            await conflictResolver.checkForConflicts(with: affirmation)
        default:
            break
        }
    }

    // MARK: - Helper Methods

    private func createTemporaryAffirmation(
        id: UUID,
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?
    ) -> any AffirmationProtocol {
        return TemporaryAffirmation(
            id: id,
            text: text,
            category: category,
            recordingURL: recordingURL
        )
    }

    private func processBatch(_ batch: BatchOperation) async {
        operationStats.batchOperationsProcessed += 1
        // Implementation for batch processing
        print("[EnhancedOptimisticUpdateManager] Processing batch: \(batch.id)")
    }
}

// MARK: - Temporary Affirmation Implementation

/// Temporary affirmation for optimistic updates
private struct TemporaryAffirmation: AffirmationProtocol {
    let id: UUID
    let text: String
    let category: AffirmationCategory
    let isFavorite: Bool = false
    let hasRecording: Bool
    let todayProgress: Double = 0.0
    let cycleProgress: Double = 0.0
    let recordingURL: URL?
    let completedCycles: Int = 0
    let currentRepetitions: Int = 0
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double = 0.0
    let moodRating: Int? = nil
    let notes: String? = nil
    let playCount: Int = 0
    let hasActiveCycle: Bool = false
    let currentCycleDay: Int = 0
    let cycleStartDate: Date? = nil
    let dailyProgress: [Date: Int] = [:]
    let isCurrentCycleComplete: Bool = false
    let hasTodayQuotaMet: Bool = false
    let canPerformRepetition: Bool = true
    let createdAt: Date = Date()
    let updatedAt: Date = Date()
    var longestStreak: Int = 0

    init(id: UUID, text: String, category: AffirmationCategory, recordingURL: URL?) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.hasRecording = recordingURL != nil
    }

    func recordRepetition() throws {
        // No-op for temporary affirmation
    }

    func updateEnergyLevel(_ level: Double) {
        // No-op for temporary affirmation
    }

    func recordMood(_ rating: Int, notes: String?) {
        // No-op for temporary affirmation
    }
}
