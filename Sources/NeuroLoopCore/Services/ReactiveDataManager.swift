import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Reactive data manager that provides real-time updates across the app
@MainActor
public final class ReactiveDataManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - Singleton
    
    public static let shared = ReactiveDataManager()
    
    // MARK: - Published Properties
    
    /// All affirmations with real-time updates
    @Published public private(set) var affirmations: [any AffirmationProtocol] = []
    
    /// Current active affirmation
    @Published public private(set) var currentAffirmation: (any AffirmationProtocol)? = nil
    
    /// Favorite affirmations
    @Published public private(set) var favoriteAffirmations: [any AffirmationProtocol] = []
    
    /// Loading states for different operations
    @Published public private(set) var isLoadingAffirmations = false
    @Published public private(set) var isCreatingAffirmation = false
    @Published public private(set) var isUpdatingAffirmation = false
    @Published public private(set) var isDeletingAffirmation = false
    
    /// Error states
    @Published public private(set) var lastError: Error? = nil
    @Published public private(set) var hasError = false
    
    // MARK: - Private Properties
    
    private var repository: AffirmationRepositoryProtocol?
    private var cancellables = Set<AnyCancellable>()
    private let dataUpdateSubject = PassthroughSubject<DataUpdateEvent, Never>()
    
    // MARK: - Data Update Events
    
    public enum DataUpdateEvent {
        case affirmationsLoaded([any AffirmationProtocol])
        case affirmationCreated(any AffirmationProtocol)
        case affirmationUpdated(any AffirmationProtocol)
        case affirmationDeleted(UUID)
        case currentAffirmationChanged((any AffirmationProtocol)?)
        case favoritesUpdated([any AffirmationProtocol])
        case repetitionRecorded(UUID, Int)
        case cycleStarted(UUID)
        case error(Error)
    }
    
    // MARK: - Publishers
    
    /// Publisher for data update events
    public var dataUpdatePublisher: AnyPublisher<DataUpdateEvent, Never> {
        dataUpdateSubject.eraseToAnyPublisher()
    }
    
    /// Publisher for affirmation changes
    public var affirmationsPublisher: AnyPublisher<[any AffirmationProtocol], Never> {
        $affirmations.eraseToAnyPublisher()
    }
    
    /// Publisher for current affirmation changes
    public var currentAffirmationPublisher: AnyPublisher<(any AffirmationProtocol)?, Never> {
        $currentAffirmation.eraseToAnyPublisher()
    }
    
    /// Publisher for loading state changes
    public var loadingStatePublisher: AnyPublisher<Bool, Never> {
        Publishers.CombineLatest4(
            $isLoadingAffirmations,
            $isCreatingAffirmation,
            $isUpdatingAffirmation,
            $isDeletingAffirmation
        )
        .map { loading1, loading2, loading3, loading4 in
            loading1 || loading2 || loading3 || loading4
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    private init() {
        setupDataUpdateHandling()
        print("[ReactiveDataManager] Initialized with reactive data synchronization")
    }
    
    // MARK: - Repository Setup
    
    public func setRepository(_ repository: AffirmationRepositoryProtocol) {
        self.repository = repository
        print("[ReactiveDataManager] Repository set, starting initial data load")
        
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Data Loading
    
    /// Load initial data from repository
    public func loadInitialData() async {
        guard let repository = repository else {
            print("[ReactiveDataManager] No repository set, cannot load data")
            return
        }
        
        isLoadingAffirmations = true
        
        do {
            // Load all affirmations
            let loadedAffirmations = try await repository.fetchAffirmations()
            
            // Update state
            affirmations = loadedAffirmations
            
            // Load current affirmation
            currentAffirmation = try await repository.fetchCurrentAffirmation()
            
            // Load favorites
            favoriteAffirmations = try await repository.fetchFavoriteAffirmations()
            
            // Emit update event
            dataUpdateSubject.send(.affirmationsLoaded(loadedAffirmations))
            
            print("[ReactiveDataManager] Initial data loaded: \(loadedAffirmations.count) affirmations")
            
        } catch {
            handleError(error)
        }
        
        isLoadingAffirmations = false
    }
    
    /// Refresh all data
    public func refreshData() async {
        print("[ReactiveDataManager] Refreshing all data")
        await loadInitialData()
    }
    
    // MARK: - Affirmation Operations
    
    /// Create a new affirmation with reactive updates
    public func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil
    ) async -> (any AffirmationProtocol)? {
        guard let repository = repository else { return nil }
        
        isCreatingAffirmation = true
        
        do {
            let newAffirmation = try await repository.createAffirmation(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
            
            // Update local state
            affirmations.append(newAffirmation)
            
            // Emit update event
            dataUpdateSubject.send(.affirmationCreated(newAffirmation))
            
            print("[ReactiveDataManager] Created affirmation: \(newAffirmation.id)")
            
            isCreatingAffirmation = false
            return newAffirmation
            
        } catch {
            handleError(error)
            isCreatingAffirmation = false
            return nil
        }
    }
    
    /// Update an affirmation with reactive updates
    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async -> Bool {
        guard let repository = repository else { return false }
        
        isUpdatingAffirmation = true
        
        do {
            try await repository.updateAffirmation(affirmation)
            
            // Update local state
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = affirmation
            }
            
            // Update current affirmation if it's the same
            if currentAffirmation?.id == affirmation.id {
                currentAffirmation = affirmation
            }
            
            // Update favorites if needed
            if affirmation.isFavorite {
                if !favoriteAffirmations.contains(where: { $0.id == affirmation.id }) {
                    favoriteAffirmations.append(affirmation)
                }
            } else {
                favoriteAffirmations.removeAll { $0.id == affirmation.id }
            }
            
            // Emit update event
            dataUpdateSubject.send(.affirmationUpdated(affirmation))
            
            print("[ReactiveDataManager] Updated affirmation: \(affirmation.id)")
            
            isUpdatingAffirmation = false
            return true
            
        } catch {
            handleError(error)
            isUpdatingAffirmation = false
            return false
        }
    }
    
    /// Delete an affirmation with reactive updates
    public func deleteAffirmation(id: UUID) async -> Bool {
        guard let repository = repository else { return false }
        
        isDeletingAffirmation = true
        
        do {
            try await repository.deleteAffirmation(id: id)
            
            // Update local state
            affirmations.removeAll { $0.id == id }
            favoriteAffirmations.removeAll { $0.id == id }
            
            // Clear current affirmation if it was deleted
            if currentAffirmation?.id == id {
                currentAffirmation = nil
            }
            
            // Emit update event
            dataUpdateSubject.send(.affirmationDeleted(id))
            
            print("[ReactiveDataManager] Deleted affirmation: \(id)")
            
            isDeletingAffirmation = false
            return true
            
        } catch {
            handleError(error)
            isDeletingAffirmation = false
            return false
        }
    }
    
    // MARK: - Current Affirmation Management
    
    /// Set the current active affirmation
    public func setCurrentAffirmation(_ affirmation: (any AffirmationProtocol)?) {
        currentAffirmation = affirmation
        dataUpdateSubject.send(.currentAffirmationChanged(affirmation))
        
        if let affirmation = affirmation {
            print("[ReactiveDataManager] Current affirmation set to: \(affirmation.id)")
        } else {
            print("[ReactiveDataManager] Current affirmation cleared")
        }
    }
    
    // MARK: - Progress Operations
    
    /// Record a repetition with reactive updates
    public func recordRepetition(for affirmation: any AffirmationProtocol) async -> Bool {
        guard let repository = repository else { return false }
        
        do {
            try await repository.recordRepetition(for: affirmation)
            
            // Update local state - the affirmation object should be updated by the repository
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = affirmation
            }
            
            // Update current affirmation if it's the same
            if currentAffirmation?.id == affirmation.id {
                currentAffirmation = affirmation
            }
            
            // Emit update event
            dataUpdateSubject.send(.repetitionRecorded(affirmation.id, affirmation.currentRepetitions))
            
            print("[ReactiveDataManager] Recorded repetition for: \(affirmation.id)")
            return true
            
        } catch {
            handleError(error)
            return false
        }
    }
    
    /// Start a cycle with reactive updates
    public func startCycle(for affirmation: any AffirmationProtocol) async -> Bool {
        guard let repository = repository else { return false }
        
        do {
            try await repository.startCycle(for: affirmation)
            
            // Update local state
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = affirmation
            }
            
            // Update current affirmation if it's the same
            if currentAffirmation?.id == affirmation.id {
                currentAffirmation = affirmation
            }
            
            // Emit update event
            dataUpdateSubject.send(.cycleStarted(affirmation.id))
            
            print("[ReactiveDataManager] Started cycle for: \(affirmation.id)")
            return true
            
        } catch {
            handleError(error)
            return false
        }
    }
    
    // MARK: - Error Handling
    
    private func handleError(_ error: Error) {
        lastError = error
        hasError = true
        dataUpdateSubject.send(.error(error))
        
        print("[ReactiveDataManager] Error: \(error.localizedDescription)")
        
        // Auto-clear error after 5 seconds
        Task {
            try? await Task.sleep(nanoseconds: 5_000_000_000)
            hasError = false
            lastError = nil
        }
    }
    
    /// Clear the current error state
    public func clearError() {
        hasError = false
        lastError = nil
    }
    
    // MARK: - Optimistic Update Support

    /// Add temporary affirmation for optimistic updates
    internal func addTemporaryAffirmation(_ affirmation: any AffirmationProtocol) async {
        affirmations.append(affirmation)
        dataUpdateSubject.send(.affirmationCreated(affirmation))
    }

    /// Update affirmation optimistically
    internal func updateAffirmationOptimistically(_ affirmation: any AffirmationProtocol) async {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            affirmations[index] = affirmation
        }

        if currentAffirmation?.id == affirmation.id {
            currentAffirmation = affirmation
        }

        dataUpdateSubject.send(.affirmationUpdated(affirmation))
    }

    /// Remove affirmation optimistically
    internal func removeAffirmationOptimistically(id: UUID) async {
        affirmations.removeAll { $0.id == id }
        favoriteAffirmations.removeAll { $0.id == id }

        if currentAffirmation?.id == id {
            currentAffirmation = nil
        }

        dataUpdateSubject.send(.affirmationDeleted(id))
    }

    /// Increment repetition count optimistically
    internal func incrementRepetitionCountOptimistically(for id: UUID) async {
        // This would need to be implemented based on your affirmation model
        // For now, we'll emit the event
        if let affirmation = affirmations.first(where: { $0.id == id }) {
            dataUpdateSubject.send(.repetitionRecorded(id, affirmation.currentRepetitions + 1))
        }
    }

    /// Start cycle optimistically
    internal func startCycleOptimistically(for id: UUID) async {
        dataUpdateSubject.send(.cycleStarted(id))
    }

    /// Replace temporary affirmation with actual one
    internal func replaceTemporaryAffirmation(tempId: UUID, with actualAffirmation: any AffirmationProtocol) async {
        if let index = affirmations.firstIndex(where: { $0.id == tempId }) {
            affirmations[index] = actualAffirmation
            dataUpdateSubject.send(.affirmationUpdated(actualAffirmation))
        }
    }

    /// Rollback affirmation changes
    internal func rollbackAffirmationChange(_ originalAffirmation: any AffirmationProtocol) async {
        if let index = affirmations.firstIndex(where: { $0.id == originalAffirmation.id }) {
            affirmations[index] = originalAffirmation
        } else {
            // If it was deleted optimistically, add it back
            affirmations.append(originalAffirmation)
        }

        dataUpdateSubject.send(.affirmationUpdated(originalAffirmation))
    }

    /// Rollback repetition count
    internal func rollbackRepetitionCount(for id: UUID, to count: Int) async {
        dataUpdateSubject.send(.repetitionRecorded(id, count))
    }

    // MARK: - Data Update Handling

    private func setupDataUpdateHandling() {
        // Set up any additional reactive handling here
        print("[ReactiveDataManager] Data update handling configured")
    }
}
