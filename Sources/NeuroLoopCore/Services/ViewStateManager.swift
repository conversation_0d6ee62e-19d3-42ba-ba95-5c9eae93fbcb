import Foundation
import Combine
import SwiftUI
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Advanced view state management system with centralized state, persistence, and undo/redo
@MainActor
public final class ViewStateManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - Singleton
    
    public static let shared = ViewStateManager()
    
    // MARK: - Published Properties
    
    @Published public private(set) var appState = AppState()
    @Published public private(set) var viewStates: [String: ViewState] = [:]
    @Published public private(set) var stateHistory: [StateSnapshot] = []
    @Published public private(set) var canUndo = false
    @Published public private(set) var canRedo = false
    @Published public private(set) var stateMetrics = StateMetrics()
    
    // MARK: - Configuration
    
    public struct Configuration {
        let maxHistorySize: Int
        let persistenceEnabled: Bool
        let autoSaveInterval: TimeInterval
        let compressionEnabled: Bool
        let analyticsEnabled: Bool
        
        public static let `default` = Configuration(
            maxHistorySize: 50,
            persistenceEnabled: true,
            autoSaveInterval: 30.0, // 30 seconds
            compressionEnabled: true,
            analyticsEnabled: true
        )
    }
    
    // MARK: - Private Properties
    
    private let config: Configuration
    private var cancellables = Set<AnyCancellable>()
    private var currentHistoryIndex = -1
    private var autoSaveTimer: Timer?
    private let persistenceManager: StatePersistenceManager
    private let stateAnalytics: StateAnalytics
    private let compressionManager = CompressionManager()
    
    // MARK: - State Structures
    
    public struct AppState: Codable {
        var selectedTab: String = "home"
        var currentAffirmationId: UUID?
        var isOnboardingComplete: Bool = false
        var lastActiveDate: Date = Date()
        var sessionState: SessionState = SessionState()
        var preferences: UserPreferences = UserPreferences()
        var navigationStack: [String] = []
        
        struct SessionState: Codable {
            var isActive: Bool = false
            var startTime: Date?
            var currentProgress: Int = 0
            var sessionType: String = "practice"
        }
        
        struct UserPreferences: Codable {
            var selectedTheme: String = "blue"
            var notificationsEnabled: Bool = true
            var hapticFeedbackEnabled: Bool = true
            var autoSaveEnabled: Bool = true
        }
    }
    
    public struct ViewState: Codable, Identifiable {
        public let id = UUID()
        let viewId: String
        var data: [String: CodableValue]
        var lastUpdated: Date
        var isActive: Bool
        var isDirty: Bool
        
        init(viewId: String, data: [String: CodableValue] = [:]) {
            self.viewId = viewId
            self.data = data
            self.lastUpdated = Date()
            self.isActive = false
            self.isDirty = false
        }
    }
    
    public struct StateSnapshot: Codable, Identifiable {
        public let id = UUID()
        let timestamp: Date
        let appState: AppState
        let viewStates: [String: ViewState]
        let description: String
        let changeType: ChangeType
        
        public enum ChangeType: String, Codable {
            case navigation = "navigation"
            case dataUpdate = "data_update"
            case userAction = "user_action"
            case systemUpdate = "system_update"
            case manual = "manual"
        }
    }
    
    public struct StateMetrics: Codable {
        var totalStateChanges: Int = 0
        var undoOperations: Int = 0
        var redoOperations: Int = 0
        var persistenceOperations: Int = 0
        var averageStateSize: Double = 0.0
        var compressionRatio: Double = 0.0
        var lastMetricsUpdate: Date = Date()
        
        var undoRedoRatio: Double {
            guard undoOperations > 0 else { return 0.0 }
            return Double(redoOperations) / Double(undoOperations)
        }
    }
    
    // MARK: - Initialization
    
    private init(config: Configuration = .default) {
        self.config = config
        self.persistenceManager = StatePersistenceManager(enabled: config.persistenceEnabled)
        self.stateAnalytics = StateAnalytics(enabled: config.analyticsEnabled)
        
        setupAutoSave()
        setupStateMonitoring()
        loadPersistedState()
        
        print("[ViewStateManager] Initialized with centralized state management")
    }
    
    // MARK: - Public API
    
    /// Update app-level state
    public func updateAppState<T>(_ keyPath: WritableKeyPath<AppState, T>, value: T, description: String = "App state update") {
        let oldState = appState
        appState[keyPath: keyPath] = value
        
        createSnapshot(description: description, changeType: .systemUpdate)
        stateMetrics.totalStateChanges += 1
        
        print("[ViewStateManager] Updated app state: \(description)")
    }
    
    /// Update view-specific state
    public func updateViewState(viewId: String, key: String, value: Any, description: String = "View state update") {
        guard let codableValue = CodableValue(value) else {
            print("[ViewStateManager] Cannot encode value for key: \(key)")
            return
        }
        
        var viewState = viewStates[viewId] ?? ViewState(viewId: viewId)
        viewState.data[key] = codableValue
        viewState.lastUpdated = Date()
        viewState.isDirty = true
        
        viewStates[viewId] = viewState
        
        createSnapshot(description: description, changeType: .dataUpdate)
        stateMetrics.totalStateChanges += 1
        
        print("[ViewStateManager] Updated view state for \(viewId): \(key)")
    }
    
    /// Get view state value
    public func getViewState<T>(viewId: String, key: String, type: T.Type) -> T? {
        guard let viewState = viewStates[viewId],
              let codableValue = viewState.data[key] else {
            return nil
        }
        
        return codableValue.value as? T
    }
    
    /// Set view as active
    public func setViewActive(viewId: String, isActive: Bool) {
        var viewState = viewStates[viewId] ?? ViewState(viewId: viewId)
        viewState.isActive = isActive
        viewState.lastUpdated = Date()
        
        viewStates[viewId] = viewState
        
        if isActive {
            stateAnalytics.recordViewActivation(viewId: viewId)
        }
    }
    
    /// Create manual snapshot
    public func createSnapshot(description: String, changeType: StateSnapshot.ChangeType = .manual) {
        let snapshot = StateSnapshot(
            timestamp: Date(),
            appState: appState,
            viewStates: viewStates,
            description: description,
            changeType: changeType
        )
        
        addToHistory(snapshot)
        updateUndoRedoState()
    }
    
    /// Undo last state change
    public func undo() -> Bool {
        guard canUndo, currentHistoryIndex > 0 else { return false }
        
        currentHistoryIndex -= 1
        let snapshot = stateHistory[currentHistoryIndex]
        
        restoreFromSnapshot(snapshot)
        stateMetrics.undoOperations += 1
        updateUndoRedoState()
        
        print("[ViewStateManager] Undo: \(snapshot.description)")
        return true
    }
    
    /// Redo last undone change
    public func redo() -> Bool {
        guard canRedo, currentHistoryIndex < stateHistory.count - 1 else { return false }
        
        currentHistoryIndex += 1
        let snapshot = stateHistory[currentHistoryIndex]
        
        restoreFromSnapshot(snapshot)
        stateMetrics.redoOperations += 1
        updateUndoRedoState()
        
        print("[ViewStateManager] Redo: \(snapshot.description)")
        return true
    }
    
    /// Clear all view states
    public func clearViewStates() {
        viewStates.removeAll()
        createSnapshot(description: "Cleared all view states", changeType: .systemUpdate)
        print("[ViewStateManager] Cleared all view states")
    }
    
    /// Get state size in bytes
    public func getStateSize() -> Int {
        do {
            let encoder = JSONEncoder()
            let appStateData = try encoder.encode(appState)
            let viewStatesData = try encoder.encode(viewStates)
            return appStateData.count + viewStatesData.count
        } catch {
            return 0
        }
    }
    
    /// Get compressed state size
    public func getCompressedStateSize() async -> Int {
        do {
            let encoder = JSONEncoder()
            let appStateData = try encoder.encode(appState)
            let viewStatesData = try encoder.encode(viewStates)
            let totalData = appStateData + viewStatesData
            
            if let compressedData = await compressionManager.compressData(totalData) {
                return compressedData.dataToUse.count
            }
        } catch {
            print("[ViewStateManager] Error calculating compressed size: \(error)")
        }
        return 0
    }
    
    /// Get state analytics
    public func getStateAnalytics() -> StateAnalytics.Analytics {
        return stateAnalytics.getAnalytics()
    }
    
    // MARK: - Private Implementation
    
    private func setupAutoSave() {
        guard config.persistenceEnabled else { return }
        
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: config.autoSaveInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.saveState()
            }
        }
    }
    
    private func setupStateMonitoring() {
        // Monitor app state changes
        $appState
            .dropFirst()
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleStateChange()
                }
            }
            .store(in: &cancellables)
        
        // Monitor view state changes
        $viewStates
            .dropFirst()
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleStateChange()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleStateChange() async {
        stateAnalytics.recordStateChange()
        
        // Update metrics
        let stateSize = getStateSize()
        let compressedSize = await getCompressedStateSize()
        
        updateStateMetrics(stateSize: stateSize, compressedSize: compressedSize)
    }
    
    private func addToHistory(_ snapshot: StateSnapshot) {
        // Remove any snapshots after current index (for redo functionality)
        if currentHistoryIndex < stateHistory.count - 1 {
            stateHistory.removeSubrange((currentHistoryIndex + 1)...)
        }
        
        stateHistory.append(snapshot)
        currentHistoryIndex = stateHistory.count - 1
        
        // Maintain history size limit
        while stateHistory.count > config.maxHistorySize {
            stateHistory.removeFirst()
            currentHistoryIndex -= 1
        }
    }
    
    private func updateUndoRedoState() {
        canUndo = currentHistoryIndex > 0
        canRedo = currentHistoryIndex < stateHistory.count - 1
    }
    
    private func restoreFromSnapshot(_ snapshot: StateSnapshot) {
        appState = snapshot.appState
        viewStates = snapshot.viewStates
    }
    
    private func updateStateMetrics(stateSize: Int, compressedSize: Int) {
        let totalChanges = stateMetrics.totalStateChanges
        let currentAverage = stateMetrics.averageStateSize
        
        stateMetrics.averageStateSize = (currentAverage * Double(totalChanges - 1) + Double(stateSize)) / Double(totalChanges)
        
        if stateSize > 0 {
            stateMetrics.compressionRatio = Double(compressedSize) / Double(stateSize)
        }
        
        stateMetrics.lastMetricsUpdate = Date()
    }
    
    private func loadPersistedState() {
        Task {
            if let persistedState = await persistenceManager.loadState(PersistedState.self) {
                appState = persistedState.appState
                viewStates = persistedState.viewStates
                print("[ViewStateManager] Loaded persisted state")
            }
        }
    }
    
    private func saveState() async {
        guard config.persistenceEnabled else { return }
        
        let stateData = PersistedState(appState: appState, viewStates: viewStates)
        await persistenceManager.saveState(stateData)
        stateMetrics.persistenceOperations += 1
        
        print("[ViewStateManager] State saved to persistence")
    }
    
    deinit {
        autoSaveTimer?.invalidate()
    }
}

// MARK: - Supporting Types

/// Persisted state structure
private struct PersistedState: Codable {
    let appState: ViewStateManager.AppState
    let viewStates: [String: ViewStateManager.ViewState]
}
