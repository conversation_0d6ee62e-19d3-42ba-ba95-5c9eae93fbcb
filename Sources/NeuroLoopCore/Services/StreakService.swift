import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

/// Represents a milestone in the user's streak journey
public struct StreakMilestone: Identifiable, Equatable {
    public let id = UUID()
    public let type: MilestoneType
    public let value: Int
    public let isAchieved: Bool

    public enum MilestoneType: Equatable {
        case currentStreak
        case longestStreak
        case completedCycles
        case upcomingStreak
    }
}

@MainActor
public final class StreakService: StreakServiceProtocol {
    // MARK: - Properties

    private let repository: any AffirmationRepositoryProtocol & Sendable

    // MARK: - Initialization

    public init(repository: any AffirmationRepositoryProtocol & Sendable) {
        self.repository = repository
    }

    // MARK: - Public Methods

    /// Validate all active streaks and deactivate any that have missed a day
    nonisolated public func validateStreaks() async throws -> StreakValidationResult {
        // Capture repository reference to avoid actor isolation issues
        let repository = await MainActor.run { self.repository }

        // Fetch affirmations
        let affirmations = try await repository.fetchAffirmations()

        var validatedCount = 0
        var deactivatedCount = 0

        let calendar = Calendar.current

        // Process each affirmation
        for affirmation in affirmations {
            // Check if the affirmation has an active cycle
            let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
            guard hasActiveCycle else { continue }

            // Skip validation if the affirmation doesn't have an active cycle
            let cycleStartDate = await MainActor.run { affirmation.cycleStartDate }
            guard cycleStartDate != nil else { continue }

            // Check if the streak is still valid
            let isValid = await validateStreak(for: affirmation)

            if isValid {
                validatedCount += 1
            } else {
                // Deactivate the streak if it's not valid
                // This is a placeholder implementation since we can't modify the affirmation directly
                // In a real implementation, we would update the affirmation's cycle status
                deactivatedCount += 1
            }
        }

        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: validatedCount,
            brokenStreaks: deactivatedCount
        )
    }

    /// Get streak statistics for all affirmations
    nonisolated public func getStreakStatistics() async throws -> StreakStatistics {
        // Capture repository reference to avoid actor isolation issues
        let repository = await MainActor.run { self.repository }

        // Fetch affirmations
        let affirmations = try await repository.fetchAffirmations()

        var statistics = StreakStatistics()
        statistics.totalAffirmations = affirmations.count

        // Count active streaks
        var activeStreaksCount = 0
        var totalCompletedCycles = 0

        // Process each affirmation to get active streaks and completed cycles
        for affirmation in affirmations {
            let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
            if hasActiveCycle {
                activeStreaksCount += 1
            }

            let completedCycles = await MainActor.run { affirmation.completedCycles }
            totalCompletedCycles += completedCycles
        }

        statistics.activeStreaks = activeStreaksCount
        statistics.completedCycles = totalCompletedCycles

        // Find the longest current streak
        var longestStreakDays = 0
        var longestStreakId: UUID? = nil

        for affirmation in affirmations {
            let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
            if hasActiveCycle {
                let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
                if currentCycleDay > longestStreakDays {
                    longestStreakDays = currentCycleDay
                    longestStreakId = await MainActor.run { affirmation.id }
                }
            }
        }

        if longestStreakDays > 0 {
            statistics.longestCurrentStreak = longestStreakDays
            statistics.longestStreakAffirmationId = longestStreakId
        }

        // Calculate the average streak length
        if activeStreaksCount > 0 {
            var totalDays = 0
            for affirmation in affirmations {
                let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
                if hasActiveCycle {
                    let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
                    totalDays += currentCycleDay
                }
            }

            statistics.averageStreakLength = Double(totalDays) / Double(activeStreaksCount)
        }

        return statistics
    }

    /// Get streak calendar data for an affirmation
    nonisolated public func getStreakCalendarData(
        for affirmation: any AffirmationProtocol, numberOfDays: Int = 30
    ) async -> [StreakCalendarDay] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        var calendarDays: [StreakCalendarDay] = []

        // Get the daily progress for the affirmation
        let dailyProgress = await MainActor.run { affirmation.dailyProgress }

        // Create calendar days for the specified number of days
        for dayOffset in 0..<numberOfDays {
            guard let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) else {
                continue
            }

            let startOfDay = calendar.startOfDay(for: date)
            let repetitions = dailyProgress[startOfDay] ?? 0
            let isComplete = repetitions >= AffirmationConstants.DAILY_REPETITIONS

            let calendarDay = StreakCalendarDay(
                date: startOfDay,
                repetitions: repetitions,
                isComplete: isComplete,
                progress: Double(repetitions) / Double(AffirmationConstants.DAILY_REPETITIONS)
            )

            calendarDays.append(calendarDay)
        }

        return calendarDays
    }

    /// Check if a streak is at risk of being broken
    nonisolated public func isStreakAtRisk(for affirmation: any AffirmationProtocol) async -> Bool {
        // Check if the affirmation has an active cycle
        let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
        guard hasActiveCycle else {
            return false
        }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let dailyProgress = await MainActor.run { affirmation.dailyProgress }

        // Check if today's quota has been met
        let todayRepetitions = dailyProgress[today] ?? 0
        let hasTodayQuotaMet = todayRepetitions >= AffirmationConstants.DAILY_REPETITIONS
        if hasTodayQuotaMet {
            return false
        }

        // Check if yesterday's quota was met
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            return false
        }

        let yesterdayCount = dailyProgress[yesterday] ?? 0
        let yesterdayQuotaMet = yesterdayCount >= AffirmationConstants.DAILY_REPETITIONS

        // If yesterday's quota wasn't met, the streak is at high risk
        if !yesterdayQuotaMet {
            return true
        }

        // Check how much time is left in the day
        let now = Date()
        guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: today) else {
            return false
        }

        let timeLeftInDay = endOfDay.timeIntervalSince(now)
        let hoursLeftInDay = timeLeftInDay / 3600

        // Calculate risk level based on time left and progress
        let progress = Double(todayRepetitions) / Double(AffirmationConstants.DAILY_REPETITIONS)

        // High risk if less than 3 hours left and less than 50% progress
        if hoursLeftInDay < 3 && progress < 0.5 {
            return true
        }

        // Medium risk if less than 6 hours left and less than 75% progress
        if hoursLeftInDay < 6 && progress < 0.75 {
            return true
        }

        return false
    }

    nonisolated public func updateStreak(for affirmation: any AffirmationProtocol & Sendable)
        async throws
    {
        // Get current streak info
        let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
        let longestStreak = await MainActor.run { affirmation.longestStreak }

        // Update longest streak if current streak is longer
        if currentCycleDay > longestStreak {
            // Create a mutable copy of the affirmation
            var updatedAffirmation = affirmation

            // Update the longest streak on main actor
            let finalAffirmation = updatedAffirmation
            updatedAffirmation = await MainActor.run {
                var mutableAffirmation = finalAffirmation
                mutableAffirmation.longestStreak = currentCycleDay
                return mutableAffirmation
            }

            // Save the updated affirmation
            try await repository.updateAffirmation(updatedAffirmation)
        }

        // Validate streaks after a repetition
        do {
            _ = try await validateStreaks()
        } catch {
            print("Error validating streaks: \(error)")
            // Continue execution even if validation fails
        }
    }

    nonisolated public func getStreakInfo(for affirmation: any AffirmationProtocol & Sendable) async
        -> StreakInfo
    {
        let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
        let completedCycles = await MainActor.run { affirmation.completedCycles }
        let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
        let cycleStartDate = await MainActor.run { affirmation.cycleStartDate }
        let lastRepetitionDate = await MainActor.run { affirmation.lastRepetitionDate }
        let longestStreak = await MainActor.run { affirmation.longestStreak }

        return StreakInfo(
            currentStreak: currentCycleDay,
            longestStreak: longestStreak,
            completedCycles: completedCycles,
            hasActiveCycle: hasActiveCycle,
            cycleStartDate: cycleStartDate,
            lastRepetitionDate: lastRepetitionDate
        )
    }

    nonisolated public func getStreakCalendar(for affirmation: any AffirmationProtocol & Sendable)
        async -> [StreakCalendarDay]
    {
        return await getStreakCalendarData(for: affirmation)
    }

    nonisolated public func validateStreak(for affirmation: any AffirmationProtocol & Sendable)
        async -> Bool
    {
        // First check if the streak is at risk
        let isAtRisk = await isStreakAtRisk(for: affirmation)
        if isAtRisk {
            return false
        }

        // Get current streak info
        let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
        let longestStreak = await MainActor.run { affirmation.longestStreak }
        let dailyProgress = await MainActor.run { affirmation.dailyProgress }
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        // Check if today's quota has been met
        let todayRepetitions = dailyProgress[today] ?? 0
        let hasTodayQuotaMet = todayRepetitions >= AffirmationConstants.DAILY_REPETITIONS

        // If today's quota is met, the streak is valid
        if hasTodayQuotaMet {
            // Check if this is a new milestone
            if currentCycleDay > longestStreak {
                // Create a mutable copy of the affirmation
                var updatedAffirmation = affirmation

                // Update the longest streak on main actor
                let finalAffirmation = updatedAffirmation
                updatedAffirmation = await MainActor.run {
                    var mutableAffirmation = finalAffirmation
                    mutableAffirmation.longestStreak = currentCycleDay
                    return mutableAffirmation
                }

                // Save the updated affirmation
                try? await repository.updateAffirmation(updatedAffirmation)
            }
            return true
        }

        // Check if we're still within the grace period
        let now = Date()
        guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: today) else {
            return false
        }

        let timeLeftInDay = endOfDay.timeIntervalSince(now)
        let hoursLeftInDay = timeLeftInDay / 3600

        // If we have more than 3 hours left in the day, the streak is still valid
        return hoursLeftInDay >= 3
    }

    /// Get streak milestone information
    nonisolated public func getStreakMilestones(for affirmation: any AffirmationProtocol & Sendable)
        async -> [StreakMilestone]
    {
        let currentCycleDay = await MainActor.run { affirmation.currentCycleDay }
        let longestStreak = await MainActor.run { affirmation.longestStreak }
        let completedCycles = await MainActor.run { affirmation.completedCycles }

        var milestones: [StreakMilestone] = []

        // Add current streak milestone
        if currentCycleDay > 0 {
            milestones.append(
                StreakMilestone(
                    type: .currentStreak,
                    value: currentCycleDay,
                    isAchieved: true
                ))
        }

        // Add longest streak milestone
        if longestStreak > 0 {
            milestones.append(
                StreakMilestone(
                    type: .longestStreak,
                    value: longestStreak,
                    isAchieved: true
                ))
        }

        // Add completed cycles milestone
        if completedCycles > 0 {
            milestones.append(
                StreakMilestone(
                    type: .completedCycles,
                    value: completedCycles,
                    isAchieved: true
                ))
        }

        // Add upcoming milestones
        let nextStreakMilestone = ((currentCycleDay / 7) + 1) * 7
        if nextStreakMilestone > currentCycleDay {
            milestones.append(
                StreakMilestone(
                    type: .upcomingStreak,
                    value: nextStreakMilestone,
                    isAchieved: false
                ))
        }

        return milestones
    }

    /// Attempt to recover a broken streak
    nonisolated public func attemptStreakRecovery(for affirmation: any AffirmationProtocol & Sendable) async throws -> Bool {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let dailyProgress = await MainActor.run { affirmation.dailyProgress }
        
        // Check if we can recover the streak
        // We can recover if:
        // 1. The streak was broken yesterday
        // 2. We haven't missed more than one day
        // 3. We complete today's quota
        
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today),
              let dayBeforeYesterday = calendar.date(byAdding: .day, value: -2, to: today) else {
            return false
        }
        
        let yesterdayCount = dailyProgress[yesterday] ?? 0
        let dayBeforeYesterdayCount = dailyProgress[dayBeforeYesterday] ?? 0
        
        // If we missed more than one day, we can't recover
        if dayBeforeYesterdayCount < AffirmationConstants.DAILY_REPETITIONS {
            return false
        }
        
        // If yesterday's quota was met, no need to recover
        if yesterdayCount >= AffirmationConstants.DAILY_REPETITIONS {
            return false
        }
        
        // Check if today's quota has been met
        let todayRepetitions = dailyProgress[today] ?? 0
        let hasTodayQuotaMet = todayRepetitions >= AffirmationConstants.DAILY_REPETITIONS
        
        // If today's quota is met, we can recover the streak
        if hasTodayQuotaMet {
            // Create a mutable copy of the affirmation
            let updatedAffirmation = await MainActor.run {
                let mutableAffirmation = affirmation
                // Update the streak by recording a repetition
                // This will automatically update the streak state
                var modifiedAffirmation = mutableAffirmation
                try? modifiedAffirmation.recordRepetition()
                return modifiedAffirmation
            }

            // Save the updated affirmation
            try await repository.updateAffirmation(updatedAffirmation)
            return true
        }
        
        return false
    }

    nonisolated public func resetStreak(for affirmation: any AffirmationProtocol & Sendable)
        async throws
    {
        // TODO: Implement resetStreak
    }

    // MARK: - Private Helper Methods

    /// Calculate the current streak length for an affirmation
    private func calculateCurrentStreak(for affirmation: any AffirmationProtocol) async -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let dailyProgress = await MainActor.run { affirmation.dailyProgress }

        var streakLength = 0
        var currentDate = today

        // Count consecutive days with completed quotas
        while true {
            let repetitions = dailyProgress[currentDate] ?? 0
            let isComplete = repetitions >= AffirmationConstants.DAILY_REPETITIONS

            if isComplete {
                streakLength += 1
                // Move to previous day
                guard let previousDay = calendar.date(byAdding: .day, value: -1, to: currentDate)
                else {
                    break
                }
                currentDate = previousDay
            } else {
                break
            }
        }

        return streakLength
    }

    /// Update the longest streak for an affirmation if the current streak is longer
    private func updateLongestStreak(for affirmation: any AffirmationProtocol) async throws {
        let currentStreak = await calculateCurrentStreak(for: affirmation)
        let longestStreak = await MainActor.run { affirmation.longestStreak }

        if currentStreak > longestStreak {
            let updatedAffirmation = await MainActor.run {
                var mutableAffirmation = affirmation
                mutableAffirmation.longestStreak = currentStreak
                return mutableAffirmation
            }
            try await repository.updateAffirmation(updatedAffirmation)
        }
    }
}

// MARK: - Helper Types

// Remove definitions of StreakValidationResult and StreakStatistics (now in NeuroLoopInterfaces)

// Using StreakCalendarDay from NeuroLoopInterfaces
