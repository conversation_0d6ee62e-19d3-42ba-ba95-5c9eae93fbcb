import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Advanced multi-level cache manager with memory (L1) and disk (L2) caching
@MainActor
public final class MultiLevelCacheManager: ObservableObject {
    
    // MARK: - Singleton
    
    public static let shared = MultiLevelCacheManager()
    
    // MARK: - Cache Levels
    
    /// L1 Cache: In-memory LRU cache for instant access
    private var memoryCache: [String: CacheEntry] = [:]
    private var accessOrder: [String] = [] // Track access order for LRU behavior
    
    /// L2 Cache: Disk-based persistent cache
    private let diskCache: DiskCache
    
    /// Cache configuration
    private let config: CacheConfiguration
    
    // MARK: - Performance Monitoring
    
    @Published public private(set) var cacheStats = CacheStatistics()
    
    // MARK: - Cache Entry
    
    private struct CacheEntry {
        let data: Data
        let timestamp: Date
        let accessCount: Int
        let expirationDate: Date?
        
        var isExpired: Bool {
            guard let expiration = expirationDate else { return false }
            return Date() > expiration
        }
        
        func incrementAccess() -> CacheEntry {
            return CacheEntry(
                data: data,
                timestamp: timestamp,
                accessCount: accessCount + 1,
                expirationDate: expirationDate
            )
        }
    }
    
    // MARK: - Configuration
    
    public struct CacheConfiguration {
        let memoryCapacity: Int
        let diskCapacity: Int
        let defaultTTL: TimeInterval
        let compressionEnabled: Bool
        
        public static let `default` = CacheConfiguration(
            memoryCapacity: 100,
            diskCapacity: 1000,
            defaultTTL: 3600, // 1 hour
            compressionEnabled: true
        )
    }
    
    // MARK: - Statistics
    
    public struct CacheStatistics {
        var memoryHits: Int = 0
        var diskHits: Int = 0
        var misses: Int = 0
        var evictions: Int = 0
        var compressionSavings: Int = 0
        
        var totalRequests: Int {
            memoryHits + diskHits + misses
        }
        
        var hitRatio: Double {
            guard totalRequests > 0 else { return 0.0 }
            return Double(memoryHits + diskHits) / Double(totalRequests)
        }
        
        var memoryHitRatio: Double {
            guard totalRequests > 0 else { return 0.0 }
            return Double(memoryHits) / Double(totalRequests)
        }
    }
    
    // MARK: - Simple LRU Cache Implementation

    /// Get value from memory cache and update access order
    private func getMemoryCacheValue(forKey key: String) -> CacheEntry? {
        guard let value = memoryCache[key] else { return nil }

        // Move to front of access order
        accessOrder.removeAll { $0 == key }
        accessOrder.append(key)

        return value
    }

    /// Set value in memory cache with LRU eviction
    private func setMemoryCacheValue(_ value: CacheEntry, forKey key: String) {
        // Remove existing entry if present
        if memoryCache[key] != nil {
            accessOrder.removeAll { $0 == key }
        }

        // Add new entry
        memoryCache[key] = value
        accessOrder.append(key)

        // Enforce capacity limit
        while memoryCache.count > config.memoryCapacity {
            if let oldestKey = accessOrder.first {
                memoryCache.removeValue(forKey: oldestKey)
                accessOrder.removeFirst()
                cacheStats.evictions += 1
            }
        }
    }

    /// Remove value from memory cache
    private func removeMemoryCacheValue(forKey key: String) {
        memoryCache.removeValue(forKey: key)
        accessOrder.removeAll { $0 == key }
    }

    /// Clear all memory cache
    private func clearMemoryCache() {
        memoryCache.removeAll()
        accessOrder.removeAll()
    }

    // MARK: - Initialization

    private init() {
        self.config = .default
        self.diskCache = DiskCache(capacity: config.diskCapacity)

        setupCacheMonitoring()
        print("[MultiLevelCacheManager] Initialized with memory: \(config.memoryCapacity), disk: \(config.diskCapacity)")
    }
    
    // MARK: - Cache Operations
    
    /// Store data in cache with optional TTL
    public func store<T: Codable>(_ object: T, forKey key: String, ttl: TimeInterval? = nil) async {
        do {
            let data = try JSONEncoder().encode(object)
            let compressedData = config.compressionEnabled ? compress(data) : data
            
            let expirationDate = ttl.map { Date().addingTimeInterval($0) } ?? Date().addingTimeInterval(config.defaultTTL)
            
            let entry = CacheEntry(
                data: compressedData,
                timestamp: Date(),
                accessCount: 1,
                expirationDate: expirationDate
            )
            
            // Store in L1 (memory)
            setMemoryCacheValue(entry, forKey: key)
            
            // Store in L2 (disk) asynchronously
            Task {
                await diskCache.store(compressedData, forKey: key, expirationDate: expirationDate)
            }
            
            // Update statistics
            if config.compressionEnabled && compressedData.count < data.count {
                cacheStats.compressionSavings += data.count - compressedData.count
            }
            
            print("[MultiLevelCacheManager] Stored \(key) in L1+L2 cache")
            
        } catch {
            print("[MultiLevelCacheManager] Error storing \(key): \(error)")
        }
    }
    
    /// Retrieve data from cache
    public func retrieve<T: Codable>(_ type: T.Type, forKey key: String) async -> T? {
        // Try L1 cache first
        if let entry = getMemoryCacheValue(forKey: key) {
            if !entry.isExpired {
                cacheStats.memoryHits += 1

                // Update access count
                let updatedEntry = entry.incrementAccess()
                setMemoryCacheValue(updatedEntry, forKey: key)

                return await decodeEntry(entry, type: type)
            } else {
                // Remove expired entry
                removeMemoryCacheValue(forKey: key)
            }
        }
        
        // Try L2 cache
        if let diskData = await diskCache.retrieve(forKey: key) {
            cacheStats.diskHits += 1
            
            // Promote to L1 cache
            let entry = CacheEntry(
                data: diskData,
                timestamp: Date(),
                accessCount: 1,
                expirationDate: Date().addingTimeInterval(config.defaultTTL)
            )
            setMemoryCacheValue(entry, forKey: key)
            
            print("[MultiLevelCacheManager] Cache hit in L2, promoted to L1: \(key)")
            return await decodeEntry(entry, type: type)
        }
        
        // Cache miss
        cacheStats.misses += 1
        print("[MultiLevelCacheManager] Cache miss for: \(key)")
        return nil
    }
    
    /// Remove data from cache
    public func remove(forKey key: String) async {
        removeMemoryCacheValue(forKey: key)
        await diskCache.remove(forKey: key)
        print("[MultiLevelCacheManager] Removed \(key) from cache")
    }
    
    /// Clear all cache levels
    public func clearAll() async {
        clearMemoryCache()
        await diskCache.clearAll()
        cacheStats = CacheStatistics()
        print("[MultiLevelCacheManager] Cleared all cache levels")
    }
    
    /// Warm cache with frequently accessed data
    public func warmCache(with keys: [String]) async {
        print("[MultiLevelCacheManager] Warming cache with \(keys.count) keys")
        // Implementation will be added in CacheWarmingService
    }
    
    // MARK: - Private Helpers
    
    private func compress(_ data: Data) -> Data {
        // Simple compression using NSData compression
        do {
            return try (data as NSData).compressed(using: .lzfse) as Data
        } catch {
            print("[MultiLevelCacheManager] Compression failed: \(error)")
            return data
        }
    }
    
    private func decompress(_ data: Data) -> Data {
        do {
            return try (data as NSData).decompressed(using: .lzfse) as Data
        } catch {
            print("[MultiLevelCacheManager] Decompression failed: \(error)")
            return data
        }
    }
    
    private func decodeEntry<T: Codable>(_ entry: CacheEntry, type: T.Type) async -> T? {
        do {
            let decompressedData = config.compressionEnabled ? decompress(entry.data) : entry.data
            return try JSONDecoder().decode(type, from: decompressedData)
        } catch {
            print("[MultiLevelCacheManager] Error decoding \(type): \(error)")
            return nil
        }
    }
    
    private func setupCacheMonitoring() {
        // Set up periodic cache cleanup and monitoring
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performMaintenance()
            }
        }
    }
    
    private func performMaintenance() async {
        // Remove expired entries and optimize cache
        await diskCache.cleanupExpired()
        print("[MultiLevelCacheManager] Performed cache maintenance")
    }
}
