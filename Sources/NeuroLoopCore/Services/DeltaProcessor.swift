import Foundation
import NeuroLoopInterfaces

/// Processes delta changes for efficient synchronization
@MainActor
public final class DeltaProcessor: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public struct Configuration {
        let enableFieldLevelDeltas: Bool
        let enableCompression: Bool
        let maxDeltaSize: Int
        let batchSize: Int
        
        public static let `default` = Configuration(
            enableFieldLevelDeltas: true,
            enableCompression: true,
            maxDeltaSize: 1024 * 1024, // 1MB
            batchSize: 50
        )
    }
    
    // MARK: - Properties
    
    private let config: Configuration
    private var deltaCache: [UUID: ProcessedDelta] = [:]
    private var processingStatistics = ProcessingStatistics()
    
    // MARK: - Statistics
    
    public struct ProcessingStatistics {
        var totalDeltas: Int = 0
        var processedDeltas: Int = 0
        var compressedDeltas: Int = 0
        var averageCompressionRatio: Double = 0.0
        var averageProcessingTime: TimeInterval = 0.0
        var totalDataProcessed: Int = 0
        var totalDataCompressed: Int = 0
        
        var compressionEfficiency: Double {
            guard totalDataProcessed > 0 else { return 0.0 }
            return Double(totalDataCompressed) / Double(totalDataProcessed)
        }
    }
    
    // MARK: - Data Structures
    
    private struct ProcessedDelta {
        let id: UUID
        let originalSize: Int
        let compressedSize: Int
        let fieldCount: Int
        let processingTime: TimeInterval
        let createdAt: Date
    }
    
    // MARK: - Initialization
    
    public init(config: Configuration = .default) {
        self.config = config
        print("[DeltaProcessor] Initialized with field-level deltas: \(config.enableFieldLevelDeltas)")
    }
    
    // MARK: - Public API
    
    /// Process delta changes for synchronization
    public func processDeltaChanges(_ groupedChanges: [String: [SyncChange]]) async {
        let startTime = Date()
        
        for (entityType, changes) in groupedChanges {
            await processEntityChanges(entityType: entityType, changes: changes)
        }
        
        let processingTime = Date().timeIntervalSince(startTime)
        updateProcessingStatistics(processingTime: processingTime)
        
        print("[DeltaProcessor] Processed \(groupedChanges.values.flatMap { $0 }.count) changes in \(String(format: "%.3f", processingTime))s")
    }
    
    /// Create delta for a single change
    public func createDelta(for change: SyncChange) async -> SyncDelta? {
        let startTime = Date()
        
        guard let delta = change.createDelta() else {
            return nil
        }
        
        let processedDelta = await processSingleDelta(delta)
        
        let processingTime = Date().timeIntervalSince(startTime)
        recordDeltaProcessing(delta: processedDelta, processingTime: processingTime)
        
        return processedDelta
    }
    
    /// Create batch of deltas
    public func createDeltaBatch(_ changes: [SyncChange]) async -> [SyncDelta] {
        let startTime = Date()
        var deltas: [SyncDelta] = []
        
        for change in changes {
            if let delta = await createDelta(for: change) {
                deltas.append(delta)
            }
        }
        
        let processingTime = Date().timeIntervalSince(startTime)
        print("[DeltaProcessor] Created \(deltas.count) deltas in \(String(format: "%.3f", processingTime))s")
        
        return deltas
    }
    
    /// Apply delta to existing data
    public func applyDelta(_ delta: SyncDelta, to existingData: Any) async -> Any? {
        guard config.enableFieldLevelDeltas else {
            return existingData
        }
        
        // Implementation for applying field-level deltas
        if var affirmation = existingData as? any AffirmationProtocol {
            return await applyDeltaToAffirmation(delta, to: affirmation)
        }
        
        return existingData
    }
    
    /// Merge multiple deltas for the same entity
    public func mergeDeltas(_ deltas: [SyncDelta]) async -> SyncDelta? {
        guard !deltas.isEmpty else { return nil }
        guard deltas.allSatisfy({ $0.entityId == deltas.first?.entityId }) else {
            print("[DeltaProcessor] Cannot merge deltas for different entities")
            return nil
        }
        
        let mergedDelta = await performDeltaMerge(deltas)
        print("[DeltaProcessor] Merged \(deltas.count) deltas into single delta")
        
        return mergedDelta
    }
    
    /// Get processing statistics
    public func getStatistics() -> ProcessingStatistics {
        return processingStatistics
    }
    
    /// Clear delta cache
    public func clearCache() {
        deltaCache.removeAll()
        print("[DeltaProcessor] Delta cache cleared")
    }
    
    // MARK: - Private Implementation
    
    private func processEntityChanges(entityType: String, changes: [SyncChange]) async {
        let batches = changes.chunked(into: config.batchSize)
        
        for batch in batches {
            await processBatch(batch, entityType: entityType)
        }
    }
    
    private func processBatch(_ changes: [SyncChange], entityType: String) async {
        let startTime = Date()
        
        // Group changes by entity ID for potential merging
        let groupedByEntity = Dictionary(grouping: changes) { $0.entityId }
        
        for (entityId, entityChanges) in groupedByEntity {
            await processEntitySpecificChanges(entityId: entityId, changes: entityChanges, entityType: entityType)
        }
        
        let processingTime = Date().timeIntervalSince(startTime)
        print("[DeltaProcessor] Processed batch of \(changes.count) \(entityType) changes in \(String(format: "%.3f", processingTime))s")
    }
    
    private func processEntitySpecificChanges(entityId: UUID, changes: [SyncChange], entityType: String) async {
        // Sort changes by timestamp to ensure proper order
        let sortedChanges = changes.sorted { $0.timestamp < $1.timestamp }
        
        // Create deltas for each change
        var deltas: [SyncDelta] = []
        for change in sortedChanges {
            if let delta = change.createDelta() {
                let processedDelta = await processSingleDelta(delta)
                deltas.append(processedDelta)
            }
        }
        
        // Attempt to merge deltas if beneficial
        if deltas.count > 1 {
            if let mergedDelta = await mergeDeltas(deltas) {
                // Use merged delta instead of individual deltas
                print("[DeltaProcessor] Merged \(deltas.count) deltas for entity \(entityId)")
            }
        }
    }
    
    private func processSingleDelta(_ delta: SyncDelta) async -> SyncDelta {
        var processedDelta = delta
        
        if config.enableFieldLevelDeltas {
            processedDelta = await optimizeFieldLevelDelta(delta)
        }
        
        if config.enableCompression {
            processedDelta = await compressDelta(processedDelta)
        }
        
        return processedDelta
    }
    
    private func optimizeFieldLevelDelta(_ delta: SyncDelta) async -> SyncDelta {
        // Remove unchanged fields and optimize field representations
        var optimizedFields: [String: CodableValue] = [:]
        
        for (field, value) in delta.fieldChanges {
            // Only include fields that have actually changed
            if await isFieldChanged(field: field, value: value, for: delta.entityId) {
                optimizedFields[field] = value
            }
        }
        
        return SyncDelta(
            changeId: delta.changeId,
            entityId: delta.entityId,
            entityType: delta.entityType,
            changeType: delta.changeType,
            fieldChanges: optimizedFields,
            timestamp: delta.timestamp
        )
    }
    
    private func compressDelta(_ delta: SyncDelta) async -> SyncDelta {
        // Compress the delta if it's large enough to benefit
        do {
            let encoder = JSONEncoder()
            let originalData = try encoder.encode(delta)
            
            if originalData.count > 1024 { // Only compress if larger than 1KB
                if let compressedData = originalData.compressed() {
                    let compressionRatio = Double(compressedData.count) / Double(originalData.count)
                    
                    processingStatistics.compressedDeltas += 1
                    updateCompressionStatistics(
                        originalSize: originalData.count,
                        compressedSize: compressedData.count
                    )
                    
                    print("[DeltaProcessor] Compressed delta \(delta.id): \(originalData.count) -> \(compressedData.count) bytes (\(String(format: "%.1f", compressionRatio * 100))%)")
                }
            }
        } catch {
            print("[DeltaProcessor] Failed to compress delta: \(error)")
        }
        
        return delta
    }
    
    private func applyDeltaToAffirmation(_ delta: SyncDelta, to affirmation: any AffirmationProtocol) async -> any AffirmationProtocol {
        // Create a mutable copy and apply field changes
        var mutableAffirmation = affirmation
        
        for (field, value) in delta.fieldChanges {
            await applyFieldChange(field: field, value: value, to: &mutableAffirmation)
        }
        
        return mutableAffirmation
    }
    
    private func applyFieldChange(field: String, value: CodableValue, to affirmation: inout any AffirmationProtocol) async {
        // Apply individual field changes
        switch field {
        case "text":
            if case .string(let text) = value {
                // Note: This would require a mutable protocol or specific implementation
                print("[DeltaProcessor] Would update text to: \(text)")
            }
        case "isFavorite":
            if case .bool(let favorite) = value {
                print("[DeltaProcessor] Would update isFavorite to: \(favorite)")
            }
        case "currentRepetitions":
            if case .int(let repetitions) = value {
                print("[DeltaProcessor] Would update currentRepetitions to: \(repetitions)")
            }
        default:
            print("[DeltaProcessor] Unknown field: \(field)")
        }
    }
    
    private func performDeltaMerge(_ deltas: [SyncDelta]) async -> SyncDelta? {
        guard let firstDelta = deltas.first else { return nil }
        
        var mergedFields: [String: CodableValue] = [:]
        
        // Merge fields from all deltas, with later deltas taking precedence
        for delta in deltas {
            for (field, value) in delta.fieldChanges {
                mergedFields[field] = value
            }
        }
        
        return SyncDelta(
            changeId: firstDelta.changeId,
            entityId: firstDelta.entityId,
            entityType: firstDelta.entityType,
            changeType: deltas.last?.changeType ?? firstDelta.changeType,
            fieldChanges: mergedFields,
            timestamp: deltas.last?.timestamp ?? firstDelta.timestamp
        )
    }
    
    private func isFieldChanged(field: String, value: CodableValue, for entityId: UUID) async -> Bool {
        // Check if field has actually changed from previous value
        // This would require access to previous state or change history
        return true // Simplified implementation
    }
    
    private func recordDeltaProcessing(delta: SyncDelta, processingTime: TimeInterval) {
        let processedDelta = ProcessedDelta(
            id: delta.id,
            originalSize: 0, // Would be calculated from actual data
            compressedSize: 0, // Would be calculated from compressed data
            fieldCount: delta.fieldChanges.count,
            processingTime: processingTime,
            createdAt: Date()
        )
        
        deltaCache[delta.id] = processedDelta
        processingStatistics.processedDeltas += 1
    }
    
    private func updateProcessingStatistics(processingTime: TimeInterval) {
        let totalProcessed = processingStatistics.processedDeltas
        let currentAverage = processingStatistics.averageProcessingTime
        
        processingStatistics.averageProcessingTime = (currentAverage * Double(totalProcessed - 1) + processingTime) / Double(totalProcessed)
    }
    
    private func updateCompressionStatistics(originalSize: Int, compressedSize: Int) {
        processingStatistics.totalDataProcessed += originalSize
        processingStatistics.totalDataCompressed += compressedSize
        
        let compressionRatio = Double(compressedSize) / Double(originalSize)
        let totalCompressed = processingStatistics.compressedDeltas
        let currentAverage = processingStatistics.averageCompressionRatio
        
        processingStatistics.averageCompressionRatio = (currentAverage * Double(totalCompressed - 1) + compressionRatio) / Double(totalCompressed)
    }
}
