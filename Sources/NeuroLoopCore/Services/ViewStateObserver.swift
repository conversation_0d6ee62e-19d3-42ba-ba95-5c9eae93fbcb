import Foundation
import SwiftUI
import Combine

/// Observable view state manager for SwiftUI integration
@MainActor
public final class ViewStateObserver: ObservableObject {
    
    // MARK: - Properties
    
    public let viewId: String
    private let stateManager = ViewStateManager.shared
    private var cancellables = Set<AnyCancellable>()
    private var activationTime: Date?
    
    // MARK: - Published Properties
    
    @Published public private(set) var isActive = false
    @Published public private(set) var isDirty = false
    @Published public private(set) var lastUpdated = Date()
    
    // MARK: - Initialization
    
    public init(viewId: String) {
        self.viewId = viewId
        setupStateObservation()
        print("[ViewStateObserver] Initialized for view: \(viewId)")
    }
    
    // MARK: - Public API
    
    /// Set a state value
    public func setState<T>(_ key: String, value: T) {
        stateManager.updateViewState(
            viewId: viewId,
            key: key,
            value: value,
            description: "Updated \(key) in \(viewId)"
        )
        
        isDirty = true
        lastUpdated = Date()
    }
    
    /// Get a state value
    public func getState<T>(_ key: String, type: T.Type) -> T? {
        return stateManager.getViewState(viewId: viewId, key: key, type: type)
    }
    
    /// Get a state value with default
    public func getState<T>(_ key: String, default defaultValue: T) -> T {
        return getState(key, type: T.self) ?? defaultValue
    }
    
    /// Mark view as active
    public func activate() {
        guard !isActive else { return }
        
        isActive = true
        activationTime = Date()
        stateManager.setViewActive(viewId: viewId, isActive: true)
        
        print("[ViewStateObserver] Activated view: \(viewId)")
    }
    
    /// Mark view as inactive
    public func deactivate() {
        guard isActive else { return }
        
        isActive = false
        
        if let activationTime = activationTime {
            let duration = Date().timeIntervalSince(activationTime)
            // Record deactivation through the state manager
            stateManager.createSnapshot(description: "View deactivated: \(viewId) (duration: \(String(format: "%.2f", duration))s)")
        }
        
        stateManager.setViewActive(viewId: viewId, isActive: false)
        self.activationTime = nil
        
        print("[ViewStateObserver] Deactivated view: \(viewId)")
    }
    
    /// Clear all state for this view
    public func clearState() {
        // Clear state through the state manager
        stateManager.updateViewState(
            viewId: viewId,
            key: "_cleared",
            value: true,
            description: "Cleared state for \(viewId)"
        )

        isDirty = false
        lastUpdated = Date()

        print("[ViewStateObserver] Cleared state for view: \(viewId)")
    }
    
    /// Create a snapshot of current state
    public func createSnapshot(description: String = "Manual snapshot") {
        stateManager.createSnapshot(description: "\(description) - \(viewId)")
    }
    
    // MARK: - Private Implementation
    
    private func setupStateObservation() {
        // Observe changes to this view's state
        stateManager.$viewStates
            .map { $0[self.viewId] }
            .removeDuplicates { oldState, newState in
                oldState?.lastUpdated == newState?.lastUpdated
            }
            .sink { [weak self] viewState in
                self?.handleStateChange(viewState)
            }
            .store(in: &cancellables)
    }
    
    private func handleStateChange(_ viewState: ViewStateManager.ViewState?) {
        if let viewState = viewState {
            isDirty = viewState.isDirty
            lastUpdated = viewState.lastUpdated
        }
    }
    
    deinit {
        Task { @MainActor in
            deactivate()
        }
        print("[ViewStateObserver] Deinitialized for view: \(viewId)")
    }
}

// MARK: - SwiftUI Integration

/// Property wrapper for automatic view state management
@propertyWrapper
public struct ViewState<T>: DynamicProperty {
    
    @StateObject private var observer: ViewStateObserver
    private let key: String
    private let defaultValue: T
    
    public var wrappedValue: T {
        get {
            observer.getState(key, default: defaultValue)
        }
        nonmutating set {
            observer.setState(key, value: newValue)
        }
    }
    
    public var projectedValue: ViewStateObserver {
        observer
    }
    
    public init(viewId: String, key: String, default defaultValue: T) {
        self._observer = StateObject(wrappedValue: ViewStateObserver(viewId: viewId))
        self.key = key
        self.defaultValue = defaultValue
    }
}

// MARK: - View Modifiers

/// View modifier for automatic view lifecycle management
public struct ViewStateLifecycle: ViewModifier {
    
    @StateObject private var observer: ViewStateObserver
    
    public init(viewId: String) {
        self._observer = StateObject(wrappedValue: ViewStateObserver(viewId: viewId))
    }
    
    public func body(content: Content) -> some View {
        content
            .onAppear {
                observer.activate()
            }
            .onDisappear {
                observer.deactivate()
            }
            .environmentObject(observer)
    }
}

/// View modifier for state persistence
public struct StatePersistence: ViewModifier {
    
    @EnvironmentObject private var observer: ViewStateObserver
    private let keys: [String]
    
    public init(keys: [String]) {
        self.keys = keys
    }
    
    public func body(content: Content) -> some View {
        content
            .onReceive(observer.$isDirty) { isDirty in
                if isDirty {
                    // Trigger persistence for specified keys
                    observer.createSnapshot(description: "Auto-save for keys: \(keys.joined(separator: ", "))")
                }
            }
    }
}

// MARK: - Extensions

extension View {
    /// Add view state lifecycle management
    public func viewStateLifecycle(viewId: String) -> some View {
        modifier(ViewStateLifecycle(viewId: viewId))
    }
    
    /// Add state persistence for specific keys
    public func statePersistence(keys: [String]) -> some View {
        modifier(StatePersistence(keys: keys))
    }
    
    /// Add both lifecycle and persistence
    public func managedViewState(viewId: String, persistKeys: [String] = []) -> some View {
        self
            .viewStateLifecycle(viewId: viewId)
            .statePersistence(keys: persistKeys)
    }
}

// MARK: - State Binding Helpers

/// Helper for creating bindings to view state
public struct ViewStateBinding<T> {
    
    private let observer: ViewStateObserver
    private let key: String
    private let defaultValue: T
    
    public init(observer: ViewStateObserver, key: String, default defaultValue: T) {
        self.observer = observer
        self.key = key
        self.defaultValue = defaultValue
    }
    
    public var binding: Binding<T> {
        Binding(
            get: {
                observer.getState(key, default: defaultValue)
            },
            set: { newValue in
                observer.setState(key, value: newValue)
            }
        )
    }
}

extension ViewStateObserver {
    /// Create a binding for a state value
    public func binding<T>(for key: String, default defaultValue: T) -> Binding<T> {
        ViewStateBinding(observer: self, key: key, default: defaultValue).binding
    }
    
    /// Create multiple bindings at once
    public func bindings<T>(for keys: [String], type: T.Type, default defaultValue: T) -> [String: Binding<T>] {
        var bindings: [String: Binding<T>] = [:]
        
        for key in keys {
            bindings[key] = binding(for: key, default: defaultValue)
        }
        
        return bindings
    }
}

// MARK: - State Validation

/// Protocol for validating state values
public protocol StateValidator {
    associatedtype Value
    func validate(_ value: Value) -> Bool
    func errorMessage(for value: Value) -> String?
}

/// Basic state validator implementations
public struct RangeValidator<T: Comparable>: StateValidator {
    public typealias Value = T
    
    private let range: ClosedRange<T>
    
    public init(range: ClosedRange<T>) {
        self.range = range
    }
    
    public func validate(_ value: T) -> Bool {
        return range.contains(value)
    }
    
    public func errorMessage(for value: T) -> String? {
        return validate(value) ? nil : "Value \(value) is outside range \(range)"
    }
}

public struct NonEmptyStringValidator: StateValidator {
    public typealias Value = String
    
    public init() {}
    
    public func validate(_ value: String) -> Bool {
        return !value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    public func errorMessage(for value: String) -> String? {
        return validate(value) ? nil : "String cannot be empty"
    }
}

extension ViewStateObserver {
    /// Set state with validation
    public func setValidatedState<T, V: StateValidator>(_ key: String, value: T, validator: V) -> Bool where V.Value == T {
        guard validator.validate(value) else {
            if let errorMessage = validator.errorMessage(for: value) {
                print("[ViewStateObserver] Validation failed for \(key): \(errorMessage)")
            }
            return false
        }
        
        setState(key, value: value)
        return true
    }
}
