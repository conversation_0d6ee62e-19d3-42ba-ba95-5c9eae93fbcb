import Foundation
import NeuroLoopInterfaces

/// Tracks data changes for synchronization
@MainActor
public final class ChangeTracker: @unchecked Sendable {
    
    // MARK: - Properties
    
    private var trackedChanges: [UUID: SyncChange] = [:]
    private var entityVersions: [UUID: Int] = [:]
    private var changeHistory: [ChangeHistoryEntry] = []
    private let maxHistorySize = 1000
    
    // MARK: - Statistics
    
    public struct TrackingStatistics {
        var totalChangesTracked: Int = 0
        var pendingChanges: Int = 0
        var syncedChanges: Int = 0
        var failedChanges: Int = 0
        var conflictedChanges: Int = 0
        var averageChangeAge: TimeInterval = 0.0
        
        var changeDistribution: [SyncChange.ChangeType: Int] = [:]
        var priorityDistribution: [SyncChange.Priority: Int] = [:]
    }
    
    private var statistics = TrackingStatistics()
    
    // MARK: - Change History

    public struct ChangeHistoryEntry {
        let timestamp: Date
        let changeId: UUID
        let entityId: UUID
        let changeType: SyncChange.ChangeType
        let status: SyncChange.Status
    }
    
    // MARK: - Public API
    
    /// Track a new change
    public func trackChange(_ change: SyncChange) {
        trackedChanges[change.id] = change
        updateEntityVersion(change.entityId)
        recordChangeHistory(change)
        updateStatistics(for: change)
        
        print("[ChangeTracker] Tracking change: \(change.type.rawValue) for \(change.entityType):\(change.entityId)")
    }
    
    /// Update the status of a tracked change
    public func updateChangeStatus(_ changeId: UUID, status: SyncChange.Status) {
        guard let change = trackedChanges[changeId] else { return }
        
        let oldStatus = change.status
        change.status = status
        
        updateStatisticsForStatusChange(from: oldStatus, to: status)
        recordStatusChange(changeId: changeId, from: oldStatus, to: status)
        
        print("[ChangeTracker] Updated change \(changeId) status: \(oldStatus.rawValue) -> \(status.rawValue)")
    }
    
    /// Get all tracked changes
    public func getAllTrackedChanges() -> [SyncChange] {
        return Array(trackedChanges.values)
    }
    
    /// Get changes by status
    public func getChanges(with status: SyncChange.Status) -> [SyncChange] {
        return trackedChanges.values.filter { $0.status == status }
    }
    
    /// Get changes for a specific entity
    public func getChanges(for entityId: UUID) -> [SyncChange] {
        return trackedChanges.values.filter { $0.entityId == entityId }
    }
    
    /// Get pending changes sorted by priority
    public func getPendingChangesSorted() -> [SyncChange] {
        return getChanges(with: .pending).sortedByPriority()
    }
    
    /// Get changes that can be retried
    public func getRetriableChanges(maxRetries: Int = 3) -> [SyncChange] {
        return trackedChanges.values.filter { $0.canRetry(maxRetries: maxRetries) }
    }
    
    /// Get stale changes
    public func getStaleChanges(threshold: TimeInterval = 3600) -> [SyncChange] {
        return trackedChanges.values.filter { $0.isStale(threshold: threshold) }
    }
    
    /// Remove a tracked change
    public func removeChange(_ changeId: UUID) {
        if let change = trackedChanges.removeValue(forKey: changeId) {
            updateStatisticsForRemoval(change)
            print("[ChangeTracker] Removed tracked change: \(changeId)")
        }
    }
    
    /// Clean up processed changes
    public func cleanupProcessedChanges() {
        let processedChanges = trackedChanges.filter { _, change in
            change.status == .synced || change.status == .failed
        }
        
        for (changeId, change) in processedChanges {
            trackedChanges.removeValue(forKey: changeId)
            updateStatisticsForRemoval(change)
        }
        
        print("[ChangeTracker] Cleaned up \(processedChanges.count) processed changes")
    }
    
    /// Clean up stale changes
    public func cleanupStaleChanges(threshold: TimeInterval = 86400) { // 24 hours
        let staleChanges = trackedChanges.filter { _, change in
            change.isStale(threshold: threshold)
        }
        
        for (changeId, change) in staleChanges {
            trackedChanges.removeValue(forKey: changeId)
            updateStatisticsForRemoval(change)
        }
        
        print("[ChangeTracker] Cleaned up \(staleChanges.count) stale changes")
    }
    
    /// Get current version for an entity
    public func getCurrentVersion(for entityId: UUID) -> Int {
        return entityVersions[entityId] ?? 0
    }
    
    /// Check if entity has pending changes
    public func hasPendingChanges(for entityId: UUID) -> Bool {
        return trackedChanges.values.contains { change in
            change.entityId == entityId && change.status == .pending
        }
    }
    
    /// Get tracking statistics
    public func getStatistics() -> TrackingStatistics {
        updateCurrentStatistics()
        return statistics
    }
    
    /// Get change history
    public func getChangeHistory(limit: Int = 100) -> [ChangeHistoryEntry] {
        return Array(changeHistory.suffix(limit))
    }
    
    /// Create a delta summary for sync
    public func createDeltaSummary() -> DeltaSummary {
        let pendingChanges = getPendingChangesSorted()
        let deltas = pendingChanges.compactMap { $0.createDelta() }
        
        return DeltaSummary(
            totalChanges: pendingChanges.count,
            deltas: deltas,
            createdAt: Date()
        )
    }
    
    // MARK: - Private Implementation
    
    private func updateEntityVersion(_ entityId: UUID) {
        entityVersions[entityId] = (entityVersions[entityId] ?? 0) + 1
    }
    
    private func recordChangeHistory(_ change: SyncChange) {
        let entry = ChangeHistoryEntry(
            timestamp: Date(),
            changeId: change.id,
            entityId: change.entityId,
            changeType: change.type,
            status: change.status
        )
        
        changeHistory.append(entry)
        
        // Maintain history size
        if changeHistory.count > maxHistorySize {
            changeHistory.removeFirst()
        }
    }
    
    private func recordStatusChange(changeId: UUID, from oldStatus: SyncChange.Status, to newStatus: SyncChange.Status) {
        let entry = ChangeHistoryEntry(
            timestamp: Date(),
            changeId: changeId,
            entityId: trackedChanges[changeId]?.entityId ?? UUID(),
            changeType: trackedChanges[changeId]?.type ?? .update,
            status: newStatus
        )
        
        changeHistory.append(entry)
        
        if changeHistory.count > maxHistorySize {
            changeHistory.removeFirst()
        }
    }
    
    private func updateStatistics(for change: SyncChange) {
        statistics.totalChangesTracked += 1
        statistics.pendingChanges += 1
        
        statistics.changeDistribution[change.type, default: 0] += 1
        statistics.priorityDistribution[change.priority, default: 0] += 1
    }
    
    private func updateStatisticsForStatusChange(from oldStatus: SyncChange.Status, to newStatus: SyncChange.Status) {
        // Decrement old status count
        switch oldStatus {
        case .pending:
            statistics.pendingChanges = max(0, statistics.pendingChanges - 1)
        case .synced:
            statistics.syncedChanges = max(0, statistics.syncedChanges - 1)
        case .failed:
            statistics.failedChanges = max(0, statistics.failedChanges - 1)
        case .conflict:
            statistics.conflictedChanges = max(0, statistics.conflictedChanges - 1)
        default:
            break
        }
        
        // Increment new status count
        switch newStatus {
        case .pending:
            statistics.pendingChanges += 1
        case .synced:
            statistics.syncedChanges += 1
        case .failed:
            statistics.failedChanges += 1
        case .conflict:
            statistics.conflictedChanges += 1
        default:
            break
        }
    }
    
    private func updateStatisticsForRemoval(_ change: SyncChange) {
        switch change.status {
        case .pending:
            statistics.pendingChanges = max(0, statistics.pendingChanges - 1)
        case .synced:
            statistics.syncedChanges = max(0, statistics.syncedChanges - 1)
        case .failed:
            statistics.failedChanges = max(0, statistics.failedChanges - 1)
        case .conflict:
            statistics.conflictedChanges = max(0, statistics.conflictedChanges - 1)
        default:
            break
        }
    }
    
    private func updateCurrentStatistics() {
        let allChanges = Array(trackedChanges.values)
        
        if !allChanges.isEmpty {
            let totalAge = allChanges.reduce(0.0) { $0 + $1.ageInSeconds }
            statistics.averageChangeAge = totalAge / Double(allChanges.count)
        } else {
            statistics.averageChangeAge = 0.0
        }
    }
}

// MARK: - Supporting Types

/// Summary of delta changes for synchronization
public struct DeltaSummary {
    public let totalChanges: Int
    public let deltas: [SyncDelta]
    public let createdAt: Date
    
    /// Get compressed representation
    public func compressed() -> Data? {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(deltas)
            return data.compressed()
        } catch {
            print("[DeltaSummary] Failed to compress: \(error)")
            return nil
        }
    }
    
    /// Get size in bytes
    public var sizeInBytes: Int {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(deltas)
            return data.count
        } catch {
            return 0
        }
    }
}

// MARK: - Extensions

extension Data {
    func compressed() -> Data? {
        return try? (self as NSData).compressed(using: .lzfse) as Data
    }
    
    func decompressed() -> Data? {
        return try? (self as NSData).decompressed(using: .lzfse) as Data
    }
}
