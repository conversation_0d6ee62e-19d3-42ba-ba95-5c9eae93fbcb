import Foundation
import Combine
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Manager for optimistic updates that provide instant UI feedback
@MainActor
public final class OptimisticUpdateManager: ObservableObject, @unchecked Sendable {
    
    // MARK: - Singleton
    
    public static let shared = OptimisticUpdateManager()
    
    // MARK: - Properties
    
    private let dataManager = ReactiveDataManager.shared
    private var pendingOperations: [UUID: PendingOperation] = [:]
    private var cancellables = Set<AnyCancellable>()
    
    /// Published state for pending operations
    @Published public private(set) var hasPendingOperations = false
    @Published public private(set) var pendingOperationCount = 0
    
    // MARK: - Pending Operation Types
    
    private enum PendingOperation {
        case createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?, tempId: UUID)
        case updateAffirmation(any AffirmationProtocol, originalState: any AffirmationProtocol)
        case deleteAffirmation(UUID, originalAffirmation: any AffirmationProtocol)
        case recordRepetition(UUID, originalCount: Int)
        case startCycle(UUID, originalState: any AffirmationProtocol)
        
        @MainActor
        func getId() -> UUID {
            switch self {
            case .createAffirmation(_, _, _, let tempId):
                return tempId
            case .updateAffirmation(let affirmation, _):
                return affirmation.id
            case .deleteAffirmation(let id, _):
                return id
            case .recordRepetition(let id, _):
                return id
            case .startCycle(let id, _):
                return id
            }
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        setupOperationTracking()
        print("[OptimisticUpdateManager] Initialized with optimistic update support")
    }
    
    // MARK: - Optimistic Operations
    
    /// Create affirmation with optimistic update
    public func createAffirmationOptimistically(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil
    ) async -> (any AffirmationProtocol)? {
        let tempId = UUID()
        
        // Create temporary affirmation for immediate UI update
        let tempAffirmation = createTemporaryAffirmation(
            id: tempId,
            text: text,
            category: category,
            recordingURL: recordingURL
        )
        
        // Add to pending operations
        let operation = PendingOperation.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL,
            tempId: tempId
        )
        await addPendingOperation(operation)

        // Optimistically update UI
        await dataManager.addTemporaryAffirmation(tempAffirmation)
        
        print("[OptimisticUpdateManager] Optimistically created affirmation: \(tempId)")
        
        // Perform actual operation in background
        Task {
            let result = await dataManager.createAffirmation(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
            
            await handleOperationResult(
                operationId: tempId,
                success: result != nil,
                actualAffirmation: result
            )
        }
        
        return tempAffirmation
    }
    
    /// Update affirmation with optimistic update
    public func updateAffirmationOptimistically(_ affirmation: any AffirmationProtocol) async -> Bool {
        // Store original state for rollback
        guard let originalAffirmation = dataManager.affirmations.first(where: { $0.id == affirmation.id }) else {
            return false
        }
        
        // Add to pending operations
        let operation = PendingOperation.updateAffirmation(affirmation, originalState: originalAffirmation)
        await addPendingOperation(operation)

        // Optimistically update UI
        await dataManager.updateAffirmationOptimistically(affirmation)
        
        print("[OptimisticUpdateManager] Optimistically updated affirmation: \(affirmation.id)")
        
        // Perform actual operation in background
        Task {
            let success = await dataManager.updateAffirmation(affirmation)
            
            await handleOperationResult(
                operationId: affirmation.id,
                success: success,
                rollbackAffirmation: success ? nil : originalAffirmation
            )
        }
        
        return true
    }
    
    /// Delete affirmation with optimistic update
    public func deleteAffirmationOptimistically(id: UUID) async -> Bool {
        // Store original affirmation for rollback
        guard let originalAffirmation = dataManager.affirmations.first(where: { $0.id == id }) else {
            return false
        }
        
        // Add to pending operations
        let operation = PendingOperation.deleteAffirmation(id, originalAffirmation: originalAffirmation)
        await addPendingOperation(operation)

        // Optimistically update UI
        await dataManager.removeAffirmationOptimistically(id: id)
        
        print("[OptimisticUpdateManager] Optimistically deleted affirmation: \(id)")
        
        // Perform actual operation in background
        Task {
            let success = await dataManager.deleteAffirmation(id: id)
            
            await handleOperationResult(
                operationId: id,
                success: success,
                rollbackAffirmation: success ? nil : originalAffirmation
            )
        }
        
        return true
    }
    
    /// Record repetition with optimistic update
    public func recordRepetitionOptimistically(for affirmation: any AffirmationProtocol) async -> Bool {
        let originalCount = affirmation.currentRepetitions
        
        // Add to pending operations
        let operation = PendingOperation.recordRepetition(affirmation.id, originalCount: originalCount)
        await addPendingOperation(operation)

        // Optimistically update UI (increment count)
        await dataManager.incrementRepetitionCountOptimistically(for: affirmation.id)
        
        print("[OptimisticUpdateManager] Optimistically recorded repetition for: \(affirmation.id)")
        
        // Perform actual operation in background
        Task {
            let success = await dataManager.recordRepetition(for: affirmation)
            
            await handleOperationResult(
                operationId: affirmation.id,
                success: success,
                rollbackCount: success ? nil : originalCount
            )
        }
        
        return true
    }
    
    /// Start cycle with optimistic update
    public func startCycleOptimistically(for affirmation: any AffirmationProtocol) async -> Bool {
        // Store original state for rollback
        let originalState = affirmation
        
        // Add to pending operations
        let operation = PendingOperation.startCycle(affirmation.id, originalState: originalState)
        await addPendingOperation(operation)

        // Optimistically update UI
        await dataManager.startCycleOptimistically(for: affirmation.id)
        
        print("[OptimisticUpdateManager] Optimistically started cycle for: \(affirmation.id)")
        
        // Perform actual operation in background
        Task {
            let success = await dataManager.startCycle(for: affirmation)
            
            await handleOperationResult(
                operationId: affirmation.id,
                success: success,
                rollbackAffirmation: success ? nil : originalState
            )
        }
        
        return true
    }
    
    // MARK: - Operation Management
    
    private func addPendingOperation(_ operation: PendingOperation) async {
        let operationId = await operation.getId()
        pendingOperations[operationId] = operation
        updatePendingState()
    }
    
    private func removePendingOperation(id: UUID) {
        pendingOperations.removeValue(forKey: id)
        updatePendingState()
    }
    
    private func updatePendingState() {
        pendingOperationCount = pendingOperations.count
        hasPendingOperations = !pendingOperations.isEmpty
    }
    
    // MARK: - Result Handling
    
    private func handleOperationResult(
        operationId: UUID,
        success: Bool,
        actualAffirmation: (any AffirmationProtocol)? = nil,
        rollbackAffirmation: (any AffirmationProtocol)? = nil,
        rollbackCount: Int? = nil
    ) async {
        defer {
            removePendingOperation(id: operationId)
        }
        
        if success {
            print("[OptimisticUpdateManager] Operation succeeded: \(operationId)")
            
            // Replace temporary with actual if needed
            if let actualAffirmation = actualAffirmation {
                await dataManager.replaceTemporaryAffirmation(
                    tempId: operationId,
                    with: actualAffirmation
                )
            }
        } else {
            print("[OptimisticUpdateManager] Operation failed, rolling back: \(operationId)")
            
            // Rollback optimistic changes
            if let rollbackAffirmation = rollbackAffirmation {
                await dataManager.rollbackAffirmationChange(rollbackAffirmation)
            }
            
            if let rollbackCount = rollbackCount {
                await dataManager.rollbackRepetitionCount(for: operationId, to: rollbackCount)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTemporaryAffirmation(
        id: UUID,
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?
    ) -> any AffirmationProtocol {
        // Create a temporary affirmation implementation
        return TemporaryAffirmation(
            id: id,
            text: text,
            category: category,
            recordingURL: recordingURL
        )
    }
    
    private func setupOperationTracking() {
        // Set up any additional operation tracking here
        print("[OptimisticUpdateManager] Operation tracking configured")
    }
    
    // MARK: - Public State Access
    
    /// Get pending operation count
    public func getPendingOperationCount() -> Int {
        return pendingOperationCount
    }
    
    /// Check if there are pending operations
    public func checkHasPendingOperations() -> Bool {
        return hasPendingOperations
    }
    
    /// Get pending operations for debugging
    public func getPendingOperations() -> [UUID] {
        return Array(pendingOperations.keys)
    }
}

// MARK: - Temporary Affirmation Implementation

/// Temporary affirmation for optimistic updates
private struct TemporaryAffirmation: AffirmationProtocol {
    let id: UUID
    let text: String
    let category: AffirmationCategory
    let isFavorite: Bool = false
    let hasRecording: Bool
    let todayProgress: Double = 0.0
    let cycleProgress: Double = 0.0
    let recordingURL: URL?
    let completedCycles: Int = 0
    let currentRepetitions: Int = 0
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double = 0.0
    let moodRating: Int? = nil
    let notes: String? = nil
    let playCount: Int = 0
    let hasActiveCycle: Bool = false
    let currentCycleDay: Int = 0
    let cycleStartDate: Date? = nil
    let dailyProgress: [Date: Int] = [:]
    let isCurrentCycleComplete: Bool = false
    let hasTodayQuotaMet: Bool = false
    let canPerformRepetition: Bool = true
    let createdAt: Date = Date()
    let updatedAt: Date = Date()
    var longestStreak: Int = 0
    
    init(id: UUID, text: String, category: AffirmationCategory, recordingURL: URL?) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.hasRecording = recordingURL != nil
    }
    
    func recordRepetition() throws {
        // Temporary implementation
    }
    
    func updateEnergyLevel(_ level: Double) {
        // Temporary implementation
    }
    
    func recordMood(_ rating: Int, notes: String?) {
        // Temporary implementation
    }
    
    static func == (lhs: TemporaryAffirmation, rhs: TemporaryAffirmation) -> Bool {
        lhs.id == rhs.id
    }
}
