import Foundation
import Combine

/// Analytics and monitoring for view state management
@MainActor
public final class StateAnalytics: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public struct Configuration {
        let trackingEnabled: Bool
        let detailedLogging: Bool
        let performanceMonitoring: Bool
        let maxEventHistory: Int
        
        public static let `default` = Configuration(
            trackingEnabled: true,
            detailedLogging: false,
            performanceMonitoring: true,
            maxEventHistory: 1000
        )
    }
    
    // MARK: - Properties
    
    private let config: Configuration
    private let enabled: Bool
    private var analytics = Analytics()
    private var eventHistory: [AnalyticsEvent] = []
    private var performanceMetrics: [PerformanceMetric] = []
    
    // MARK: - Analytics Data
    
    public struct Analytics {
        var totalStateChanges: Int = 0
        var viewActivations: [String: Int] = [:]
        var stateChangesByType: [String: Int] = [:]
        var averageStateChangeFrequency: Double = 0.0
        var peakStateChangeRate: Double = 0.0
        var mostActiveViews: [String] = []
        var stateChangePatterns: [StateChangePattern] = []
        var sessionMetrics: SessionMetrics = SessionMetrics()
        var performanceSummary: PerformanceSummary = PerformanceSummary()
        
        struct SessionMetrics {
            var sessionStartTime: Date = Date()
            var totalSessionTime: TimeInterval = 0.0
            var stateChangesPerSession: Double = 0.0
            var viewSwitchesPerSession: Double = 0.0
            var averageViewDuration: TimeInterval = 0.0
        }
        
        struct PerformanceSummary {
            var averageStateUpdateTime: TimeInterval = 0.0
            var slowestStateUpdate: TimeInterval = 0.0
            var fastestStateUpdate: TimeInterval = 0.0
            var memoryUsageTrend: [Double] = []
            var stateComplexityTrend: [Int] = []
        }
    }
    
    public struct StateChangePattern {
        let id = UUID()
        let sequence: [String]
        let frequency: Int
        let averageTimeBetweenChanges: TimeInterval
        let lastOccurrence: Date
    }
    
    public struct AnalyticsEvent {
        let id = UUID()
        let timestamp: Date
        let type: EventType
        let viewId: String?
        let metadata: [String: Any]
        
        public enum EventType {
            case stateChange
            case viewActivation
            case viewDeactivation
            case performanceMetric
            case userInteraction
        }
    }
    
    public struct PerformanceMetric {
        let timestamp: Date
        let operation: String
        let duration: TimeInterval
        let memoryUsage: Double
        let stateComplexity: Int
    }
    
    // MARK: - Initialization
    
    public init(enabled: Bool = true, config: Configuration = .default) {
        self.enabled = enabled
        self.config = config
        
        if enabled {
            setupPerformanceMonitoring()
        }
        
        print("[StateAnalytics] Initialized with tracking: \(enabled)")
    }
    
    // MARK: - Public API
    
    /// Record a state change
    public func recordStateChange(viewId: String? = nil, changeType: String = "unknown") {
        guard enabled else { return }
        
        analytics.totalStateChanges += 1
        analytics.stateChangesByType[changeType, default: 0] += 1
        
        let event = AnalyticsEvent(
            timestamp: Date(),
            type: .stateChange,
            viewId: viewId,
            metadata: ["changeType": changeType]
        )
        
        addEvent(event)
        updateStateChangeFrequency()
        detectStateChangePatterns()
        
        if config.detailedLogging {
            print("[StateAnalytics] State change recorded: \(changeType) for view: \(viewId ?? "global")")
        }
    }
    
    /// Record view activation
    public func recordViewActivation(viewId: String) {
        guard enabled else { return }
        
        analytics.viewActivations[viewId, default: 0] += 1
        
        let event = AnalyticsEvent(
            timestamp: Date(),
            type: .viewActivation,
            viewId: viewId,
            metadata: [:]
        )
        
        addEvent(event)
        updateMostActiveViews()
        updateSessionMetrics()
        
        if config.detailedLogging {
            print("[StateAnalytics] View activation recorded: \(viewId)")
        }
    }
    
    /// Record view deactivation
    public func recordViewDeactivation(viewId: String, duration: TimeInterval) {
        guard enabled else { return }
        
        let event = AnalyticsEvent(
            timestamp: Date(),
            type: .viewDeactivation,
            viewId: viewId,
            metadata: ["duration": duration]
        )
        
        addEvent(event)
        updateAverageViewDuration(duration)
        
        if config.detailedLogging {
            print("[StateAnalytics] View deactivation recorded: \(viewId), duration: \(String(format: "%.2f", duration))s")
        }
    }
    
    /// Record performance metric
    public func recordPerformanceMetric(operation: String, duration: TimeInterval, memoryUsage: Double = 0.0, stateComplexity: Int = 0) {
        guard enabled && config.performanceMonitoring else { return }
        
        let metric = PerformanceMetric(
            timestamp: Date(),
            operation: operation,
            duration: duration,
            memoryUsage: memoryUsage,
            stateComplexity: stateComplexity
        )
        
        addPerformanceMetric(metric)
        updatePerformanceSummary(metric)
        
        if config.detailedLogging {
            print("[StateAnalytics] Performance metric recorded: \(operation), duration: \(String(format: "%.3f", duration))s")
        }
    }
    
    /// Get current analytics
    public func getAnalytics() -> Analytics {
        updateAnalyticsSummary()
        return analytics
    }
    
    /// Get event history
    public func getEventHistory(limit: Int = 100) -> [AnalyticsEvent] {
        return Array(eventHistory.suffix(limit))
    }
    
    /// Get performance metrics
    public func getPerformanceMetrics(limit: Int = 100) -> [PerformanceMetric] {
        return Array(performanceMetrics.suffix(limit))
    }
    
    /// Generate analytics report
    public func generateReport() -> String {
        let analytics = getAnalytics()
        
        return """
        [StateAnalytics] Analytics Report:
        
        === State Changes ===
        Total State Changes: \(analytics.totalStateChanges)
        Average Frequency: \(String(format: "%.2f", analytics.averageStateChangeFrequency)) changes/min
        Peak Rate: \(String(format: "%.2f", analytics.peakStateChangeRate)) changes/min
        
        === View Activity ===
        Most Active Views: \(analytics.mostActiveViews.prefix(5).joined(separator: ", "))
        Total View Activations: \(analytics.viewActivations.values.reduce(0, +))
        
        === Session Metrics ===
        Session Duration: \(String(format: "%.1f", analytics.sessionMetrics.totalSessionTime / 60)) minutes
        State Changes per Session: \(String(format: "%.1f", analytics.sessionMetrics.stateChangesPerSession))
        Average View Duration: \(String(format: "%.2f", analytics.sessionMetrics.averageViewDuration))s
        
        === Performance ===
        Average State Update Time: \(String(format: "%.3f", analytics.performanceSummary.averageStateUpdateTime))s
        Slowest Update: \(String(format: "%.3f", analytics.performanceSummary.slowestStateUpdate))s
        Fastest Update: \(String(format: "%.3f", analytics.performanceSummary.fastestStateUpdate))s
        
        === Patterns ===
        Detected Patterns: \(analytics.stateChangePatterns.count)
        """
    }
    
    /// Reset analytics
    public func resetAnalytics() {
        analytics = Analytics()
        eventHistory.removeAll()
        performanceMetrics.removeAll()
        
        print("[StateAnalytics] Analytics reset")
    }
    
    // MARK: - Private Implementation
    
    private func setupPerformanceMonitoring() {
        guard config.performanceMonitoring else { return }
        
        // Set up periodic performance monitoring
        Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.collectPerformanceSnapshot()
            }
        }
    }
    
    private func addEvent(_ event: AnalyticsEvent) {
        eventHistory.append(event)
        
        // Maintain history size
        if eventHistory.count > config.maxEventHistory {
            eventHistory.removeFirst()
        }
    }
    
    private func addPerformanceMetric(_ metric: PerformanceMetric) {
        performanceMetrics.append(metric)
        
        // Maintain metrics size
        if performanceMetrics.count > config.maxEventHistory {
            performanceMetrics.removeFirst()
        }
    }
    
    private func updateStateChangeFrequency() {
        let recentEvents = eventHistory.filter { event in
            event.type == .stateChange && 
            Date().timeIntervalSince(event.timestamp) <= 60.0 // Last minute
        }
        
        analytics.averageStateChangeFrequency = Double(recentEvents.count)
        
        // Update peak rate
        if analytics.averageStateChangeFrequency > analytics.peakStateChangeRate {
            analytics.peakStateChangeRate = analytics.averageStateChangeFrequency
        }
    }
    
    private func updateMostActiveViews() {
        let sortedViews = analytics.viewActivations.sorted { $0.value > $1.value }
        analytics.mostActiveViews = sortedViews.prefix(10).map { $0.key }
    }
    
    private func updateSessionMetrics() {
        let sessionDuration = Date().timeIntervalSince(analytics.sessionMetrics.sessionStartTime)
        analytics.sessionMetrics.totalSessionTime = sessionDuration
        
        if sessionDuration > 0 {
            analytics.sessionMetrics.stateChangesPerSession = Double(analytics.totalStateChanges) / (sessionDuration / 3600.0) // per hour
            
            let viewSwitches = analytics.viewActivations.values.reduce(0, +)
            analytics.sessionMetrics.viewSwitchesPerSession = Double(viewSwitches) / (sessionDuration / 3600.0) // per hour
        }
    }
    
    private func updateAverageViewDuration(_ duration: TimeInterval) {
        let deactivationEvents = eventHistory.filter { $0.type == .viewDeactivation }
        let totalDuration = deactivationEvents.compactMap { $0.metadata["duration"] as? TimeInterval }.reduce(0, +)
        
        if !deactivationEvents.isEmpty {
            analytics.sessionMetrics.averageViewDuration = totalDuration / Double(deactivationEvents.count)
        }
    }
    
    private func updatePerformanceSummary(_ metric: PerformanceMetric) {
        let recentMetrics = performanceMetrics.filter { 
            Date().timeIntervalSince($0.timestamp) <= 300.0 // Last 5 minutes
        }
        
        if !recentMetrics.isEmpty {
            let totalDuration = recentMetrics.map { $0.duration }.reduce(0, +)
            analytics.performanceSummary.averageStateUpdateTime = totalDuration / Double(recentMetrics.count)
            
            analytics.performanceSummary.slowestStateUpdate = recentMetrics.map { $0.duration }.max() ?? 0.0
            analytics.performanceSummary.fastestStateUpdate = recentMetrics.map { $0.duration }.min() ?? 0.0
            
            // Update trends
            let avgMemory = recentMetrics.map { $0.memoryUsage }.reduce(0, +) / Double(recentMetrics.count)
            analytics.performanceSummary.memoryUsageTrend.append(avgMemory)
            
            let avgComplexity = recentMetrics.map { $0.stateComplexity }.reduce(0, +) / recentMetrics.count
            analytics.performanceSummary.stateComplexityTrend.append(avgComplexity)
            
            // Maintain trend size
            if analytics.performanceSummary.memoryUsageTrend.count > 100 {
                analytics.performanceSummary.memoryUsageTrend.removeFirst()
            }
            if analytics.performanceSummary.stateComplexityTrend.count > 100 {
                analytics.performanceSummary.stateComplexityTrend.removeFirst()
            }
        }
    }
    
    private func detectStateChangePatterns() {
        // Analyze recent state changes for patterns
        let recentStateChanges = eventHistory.filter { event in
            event.type == .stateChange && 
            Date().timeIntervalSince(event.timestamp) <= 300.0 // Last 5 minutes
        }
        
        if recentStateChanges.count >= 3 {
            // Look for sequences of 3 or more state changes
            let sequences = extractSequences(from: recentStateChanges, length: 3)
            updatePatterns(sequences)
        }
    }
    
    private func extractSequences(from events: [AnalyticsEvent], length: Int) -> [[String]] {
        var sequences: [[String]] = []
        
        for i in 0...(events.count - length) {
            let sequence = Array(events[i..<(i + length)]).compactMap { event in
                event.metadata["changeType"] as? String
            }
            
            if sequence.count == length {
                sequences.append(sequence)
            }
        }
        
        return sequences
    }
    
    private func updatePatterns(_ sequences: [[String]]) {
        var patternCounts: [String: Int] = [:]
        
        for sequence in sequences {
            let key = sequence.joined(separator: "->")
            patternCounts[key, default: 0] += 1
        }
        
        // Update existing patterns or create new ones
        for (sequenceKey, count) in patternCounts {
            let sequence = sequenceKey.components(separatedBy: "->")
            
            if let existingIndex = analytics.stateChangePatterns.firstIndex(where: { $0.sequence == sequence }) {
                analytics.stateChangePatterns[existingIndex] = StateChangePattern(
                    sequence: sequence,
                    frequency: analytics.stateChangePatterns[existingIndex].frequency + count,
                    averageTimeBetweenChanges: calculateAverageTimeBetween(sequence),
                    lastOccurrence: Date()
                )
            } else {
                analytics.stateChangePatterns.append(StateChangePattern(
                    sequence: sequence,
                    frequency: count,
                    averageTimeBetweenChanges: calculateAverageTimeBetween(sequence),
                    lastOccurrence: Date()
                ))
            }
        }
    }
    
    private func calculateAverageTimeBetween(_ sequence: [String]) -> TimeInterval {
        // Simplified calculation - in a real implementation, this would analyze actual timing
        return 1.0 // 1 second average
    }
    
    private func collectPerformanceSnapshot() {
        let memoryUsage = getCurrentMemoryUsage()
        let stateComplexity = getCurrentStateComplexity()
        
        recordPerformanceMetric(
            operation: "performance_snapshot",
            duration: 0.0,
            memoryUsage: memoryUsage,
            stateComplexity: stateComplexity
        )
    }
    
    private func getCurrentMemoryUsage() -> Double {
        // Simplified memory usage calculation
        return Double.random(in: 50...200) // MB
    }
    
    private func getCurrentStateComplexity() -> Int {
        // Simplified state complexity calculation
        return analytics.totalStateChanges % 100
    }
    
    private func updateAnalyticsSummary() {
        // Update any derived analytics values
        updateSessionMetrics()
    }
}
