import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// DebugRepetitionCounter
///
/// A singleton class that maintains debug repetition counts across all instances
/// of DebugRepetitionService. This ensures that repetition counts are properly
/// synchronized across the app.
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class DebugRepetitionCounter {
    // MARK: - Singleton Instance

    public static let shared = DebugRepetitionCounter()

    // MARK: - Properties

    private var repetitionCounts: [UUID: Int] = [:]

    // MARK: - Initialization

    private init() {
        print("DebugRepetitionCounter: Initialized singleton instance")
    }

    // MARK: - Public Methods

    /// Get the current repetition count for an affirmation
    public func getCount(for affirmationId: UUID) -> Int {
        return repetitionCounts[affirmationId] ?? 0
    }

    /// Increment the repetition count for an affirmation
    public func incrementCount(for affirmationId: UUID) -> Int {
        let currentCount = getCount(for: affirmationId)

        // Increment by 1
        let newCount = currentCount + 1
        repetitionCounts[affirmationId] = newCount

        print(
            "DebugRepetitionCounter: Incremented count for \(affirmationId) from \(currentCount) to \(newCount)"
        )

        // Post a single notification to avoid duplicate updates
        NotificationCenter.default.post(
            name: Notification.Name("RepetitionCountChanged"),
            object: nil,
            userInfo: [
                "count": newCount,
                "affirmationId": affirmationId,
                "timestamp": Date().timeIntervalSince1970,
                "source": "DebugRepetitionCounter",
            ]
        )
        print(
            "DebugRepetitionCounter: Posted notification for count update to \(newCount)")

        return newCount
    }

    /// Reset the repetition count for an affirmation
    public func resetCount(for affirmationId: UUID) {
        repetitionCounts[affirmationId] = 0

        print("DebugRepetitionCounter: Reset count for \(affirmationId) to 0")

        // Post notification to update all views
        NotificationCenter.default.post(
            name: Notification.Name("RepetitionCountChanged"),
            object: nil,
            userInfo: [
                "count": 0,
                "affirmationId": affirmationId,
                "timestamp": Date().timeIntervalSince1970,
                "source": "DebugRepetitionCounter",
            ]
        )
    }

    /// Reset all repetition counts
    public func resetAllCounts() {
        let affirmationIds = repetitionCounts.keys
        repetitionCounts.removeAll()

        print("DebugRepetitionCounter: Reset all counts")

        // Post notifications for each affirmation
        for affirmationId in affirmationIds {
            NotificationCenter.default.post(
                name: Notification.Name("RepetitionCountChanged"),
                object: nil,
                userInfo: [
                    "count": 0,
                    "affirmationId": affirmationId,
                    "timestamp": Date().timeIntervalSince1970,
                    "source": "DebugRepetitionCounter",
                ]
            )
        }
    }
}

@available(iOS 17.0, macOS 14.0, *)
extension ServiceFactory {
    /// Get a debug repetition service that wraps the standard repetition service
    /// This service ensures repetitions are always counted correctly, regardless of speech recognition
    public func getDebugRepetitionService() throws -> RepetitionServiceProtocol {
        let baseService = try getRepetitionService()
        return DebugRepetitionService(wrappedService: baseService)
    }

    /// Get the appropriate repetition service based on debug mode
    /// If debug mode is enabled, returns a DebugRepetitionService
    /// Otherwise, returns the standard RepetitionService
    public func getRepetitionServiceForMode(debugMode: Bool) throws -> RepetitionServiceProtocol {
        if debugMode {
            return try getDebugRepetitionService()
        } else {
            return try getRepetitionService()
        }
    }
}

/// A special debug implementation of RepetitionServiceProtocol that always succeeds
/// and increments the repetition counter regardless of speech recognition.
/// This is used to diagnose issues with the repetition counting system.
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class DebugRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
    // MARK: - Private Properties

    private let wrappedService: RepetitionServiceProtocol
    private let counter: DebugRepetitionCounter

    // MARK: - Initialization

    public init(wrappedService: RepetitionServiceProtocol) {
        self.wrappedService = wrappedService
        self.counter = DebugRepetitionCounter.shared
        print(
            "DebugRepetitionService: Initialized with wrapped service of type \(type(of: wrappedService))"
        )
        print("DebugRepetitionService: Using shared DebugRepetitionCounter instance")
    }

    // MARK: - Repetition Methods

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        print("DebugRepetitionService: Recording repetition for affirmation \(affirmation.id)")

        // Get current count from the shared counter
        let currentCount = counter.getCount(for: affirmation.id)
        print(
            "DebugRepetitionService: Current count for affirmation \(affirmation.id): \(currentCount) (from shared counter)"
        )
        print("DebugRepetitionService: Affirmation's own count: \(affirmation.currentRepetitions)")

        // Use the maximum of the counter's count and the affirmation's count as starting point
        let startingCount = max(currentCount, affirmation.currentRepetitions)

        // If the affirmation has a higher count, sync the counter to match
        if startingCount > currentCount {
            print(
                "DebugRepetitionService: Syncing counter to match affirmation count: \(startingCount)"
            )
            counter.resetCount(for: affirmation.id)
            for _ in 0..<startingCount {
                _ = counter.incrementCount(for: affirmation.id)
            }
        }

        print("DebugRepetitionService: Using starting count: \(startingCount)")

        // Increment using the shared counter
        let newCount = counter.incrementCount(for: affirmation.id)
        print("DebugRepetitionService: Incremented count to \(newCount) using shared counter")

        // Add a small delay to ensure the count is properly registered
        try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
        print("DebugRepetitionService: Added delay after incrementing count")

        // Create a copy of the affirmation with the updated count
        var updatedAffirmation = affirmation

        // Update the affirmation's repetition count
        // This is a workaround since we can't directly modify the affirmation
        updatedAffirmation = AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            createdAt: affirmation.createdAt,
            updatedAt: Date(),
            currentCycleDay: affirmation.currentCycleDay,
            cycleStartDate: affirmation.cycleStartDate,
            completedCycles: affirmation.completedCycles,
            currentRepetitions: newCount,  // Use the new count directly
            dailyProgress: affirmation.dailyProgress,
            lastRepetitionDate: Date(),
            energyLevel: affirmation.energyLevel,
            moodRating: affirmation.moodRating,
            notes: affirmation.notes,
            isFavorite: affirmation.isFavorite,
            playCount: affirmation.playCount,
            hasActiveCycle: true,
            isCurrentCycleComplete: false,
            todayProgress: Double(newCount) / 100.0,
            cycleProgress: affirmation.cycleProgress,
            hasTodayQuotaMet: newCount >= 100
        )

        print(
            "DebugRepetitionService: Incremented repetition count from \(startingCount) to \(newCount)"
        )

        return RepetitionResult(
            success: true,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: newCount >= 100,
            isCycleComplete: false
        )
    }

    // Forward all other methods to the wrapped service

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await wrappedService.startCycle(for: affirmation)
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await wrappedService.startSession(for: affirmation)
    }

    public nonisolated func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        return MainActor.assumeIsolated {
            // Get the count from the shared counter
            let currentCount = self.counter.getCount(for: affirmation.id)
            print(
                "DebugRepetitionService: getProgress for \(affirmation.id) - count: \(currentCount) (from shared counter)"
            )

            return ProgressInfo(
                todayProgress: Double(currentCount) / 100.0,
                cycleProgress: Double(affirmation.currentCycleDay) / 7.0,
                currentDay: affirmation.currentCycleDay,
                totalDays: 7,
                currentRepetitions: currentCount,
                totalRepetitions: 100,
                hasTodayQuotaMet: currentCount >= 100,
                isCycleComplete: affirmation.currentCycleDay >= 7 && currentCount >= 100,
                hasActiveCycle: affirmation.hasActiveCycle
            )
        }
    }

    public nonisolated func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return wrappedService.getStreakInfo(for: affirmation)
    }

    public nonisolated func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        // Always allow repetitions in debug mode, regardless of quota or cycle status
        print("DebugRepetitionService: canPerformRepetition - Always returning true in debug mode")
        return true
    }

    public nonisolated func timeUntilNextRepetition(for affirmation: any AffirmationProtocol)
        -> TimeInterval?
    {
        return nil  // No time restriction in debug mode
    }

    public nonisolated func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return wrappedService.isCycleBroken(for: affirmation)
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        return try await wrappedService.restartBrokenCycle(for: affirmation)
    }

    // MARK: - Debug Methods

    /// Reset the repetition count for a specific affirmation
    @MainActor
    public func resetCount(for affirmationId: UUID) {
        print("DebugRepetitionService: Resetting repetition count for affirmation \(affirmationId)")

        // Use the shared counter to reset the count
        counter.resetCount(for: affirmationId)

        print("DebugRepetitionService: Reset count using shared counter")
    }
}
