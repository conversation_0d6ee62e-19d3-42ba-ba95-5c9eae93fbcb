import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Resolves synchronization conflicts during background sync
@MainActor
public final class SyncConflictResolver: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public enum ConflictResolutionStrategy {
        case lastWriteWins
        case firstWriteWins
        case merge
        case serverWins
        case clientWins
        case userPrompt
        case fieldLevelMerge
        case timestampBased
    }
    
    // MARK: - Properties
    
    private let strategy: ConflictResolutionStrategy
    private var conflictHistory: [ResolvedConflict] = []
    private var resolutionStatistics = ResolutionStatistics()
    
    // MARK: - Statistics
    
    public struct ResolutionStatistics {
        var totalConflicts: Int = 0
        var resolvedConflicts: Int = 0
        var unresolvedConflicts: Int = 0
        var averageResolutionTime: TimeInterval = 0.0
        var strategyUsage: [ConflictResolutionStrategy: Int] = [:]
        var conflictTypeDistribution: [SyncConflict.ConflictType: Int] = [:]
        
        var resolutionRate: Double {
            guard totalConflicts > 0 else { return 0.0 }
            return Double(resolvedConflicts) / Double(totalConflicts)
        }
    }
    
    // MARK: - Resolved Conflict Record

    public struct ResolvedConflict {
        let conflictId: UUID
        let entityId: UUID
        let entityType: String
        let conflictType: SyncConflict.ConflictType
        let strategy: ConflictResolutionStrategy
        let resolutionTime: TimeInterval
        let resolvedAt: Date
        let successful: Bool
    }
    
    // MARK: - Initialization
    
    public init(strategy: ConflictResolutionStrategy) {
        self.strategy = strategy
        print("[SyncConflictResolver] Initialized with strategy: \(strategy)")
    }
    
    // MARK: - Public API
    
    /// Resolve a synchronization conflict
    public func resolveConflict(_ conflict: SyncConflict) async -> Bool {
        let startTime = Date()
        resolutionStatistics.totalConflicts += 1
        resolutionStatistics.conflictTypeDistribution[conflict.conflictType, default: 0] += 1
        
        print("[SyncConflictResolver] Resolving \(conflict.conflictType) conflict for \(conflict.entityType):\(conflict.entityId)")
        
        let resolution = await performConflictResolution(conflict)
        let resolutionTime = Date().timeIntervalSince(startTime)
        
        let success = resolution != nil
        if success {
            resolutionStatistics.resolvedConflicts += 1
        } else {
            resolutionStatistics.unresolvedConflicts += 1
        }
        
        updateResolutionStatistics(resolutionTime: resolutionTime)
        recordConflictResolution(conflict: conflict, resolutionTime: resolutionTime, success: success)
        
        return success
    }
    
    /// Resolve multiple conflicts in batch
    public func resolveConflicts(_ conflicts: [SyncConflict]) async -> [SyncConflict] {
        var resolvedConflicts: [SyncConflict] = []
        
        for conflict in conflicts {
            if await resolveConflict(conflict) {
                var resolvedConflict = conflict
                resolvedConflict.resolved = true
                resolvedConflicts.append(resolvedConflict)
            }
        }
        
        print("[SyncConflictResolver] Resolved \(resolvedConflicts.count) of \(conflicts.count) conflicts")
        return resolvedConflicts
    }
    
    /// Get resolution statistics
    public func getStatistics() -> ResolutionStatistics {
        return resolutionStatistics
    }
    
    /// Get conflict resolution history
    public func getResolutionHistory(limit: Int = 100) -> [ResolvedConflict] {
        return Array(conflictHistory.suffix(limit))
    }
    
    /// Check if strategy can handle conflict type
    public func canResolve(conflictType: SyncConflict.ConflictType) -> Bool {
        switch (strategy, conflictType) {
        case (.userPrompt, _):
            return false // Cannot auto-resolve user prompt conflicts
        case (.merge, .deletionConflict):
            return false // Cannot merge deletion conflicts
        case (.fieldLevelMerge, .deletionConflict):
            return false // Cannot field-merge deletion conflicts
        default:
            return true
        }
    }
    
    // MARK: - Private Implementation
    
    private func performConflictResolution(_ conflict: SyncConflict) async -> Any? {
        guard canResolve(conflictType: conflict.conflictType) else {
            print("[SyncConflictResolver] Cannot resolve \(conflict.conflictType) with strategy \(strategy)")
            return nil
        }
        
        switch strategy {
        case .lastWriteWins:
            return await resolveLastWriteWins(conflict)
        case .firstWriteWins:
            return await resolveFirstWriteWins(conflict)
        case .merge:
            return await resolveMerge(conflict)
        case .serverWins:
            return await resolveServerWins(conflict)
        case .clientWins:
            return await resolveClientWins(conflict)
        case .fieldLevelMerge:
            return await resolveFieldLevelMerge(conflict)
        case .timestampBased:
            return await resolveTimestampBased(conflict)
        case .userPrompt:
            return await resolveUserPrompt(conflict)
        }
    }
    
    private func resolveLastWriteWins(_ conflict: SyncConflict) async -> Any? {
        // Compare timestamps and choose the most recent version
        if let localAffirmation = conflict.localVersion as? any AffirmationProtocol,
           let remoteAffirmation = conflict.remoteVersion as? any AffirmationProtocol {
            
            let resolution = localAffirmation.updatedAt > remoteAffirmation.updatedAt ? localAffirmation : remoteAffirmation
            print("[SyncConflictResolver] Last write wins: chose \(resolution.updatedAt > localAffirmation.updatedAt ? "remote" : "local") version")
            return resolution
        }
        
        return conflict.localVersion // Default to local if comparison fails
    }
    
    private func resolveFirstWriteWins(_ conflict: SyncConflict) async -> Any? {
        // Choose the version with the earlier timestamp
        if let localAffirmation = conflict.localVersion as? any AffirmationProtocol,
           let remoteAffirmation = conflict.remoteVersion as? any AffirmationProtocol {
            
            let resolution = localAffirmation.updatedAt < remoteAffirmation.updatedAt ? localAffirmation : remoteAffirmation
            print("[SyncConflictResolver] First write wins: chose \(resolution.updatedAt < localAffirmation.updatedAt ? "remote" : "local") version")
            return resolution
        }
        
        return conflict.localVersion
    }
    
    private func resolveMerge(_ conflict: SyncConflict) async -> Any? {
        // Perform intelligent merging of both versions
        if let localAffirmation = conflict.localVersion as? any AffirmationProtocol,
           let remoteAffirmation = conflict.remoteVersion as? any AffirmationProtocol {
            
            return await performIntelligentMerge(local: localAffirmation, remote: remoteAffirmation)
        }
        
        return conflict.localVersion
    }
    
    private func resolveServerWins(_ conflict: SyncConflict) async -> Any? {
        // Always choose the server (remote) version
        print("[SyncConflictResolver] Server wins: chose remote version")
        return conflict.remoteVersion
    }
    
    private func resolveClientWins(_ conflict: SyncConflict) async -> Any? {
        // Always choose the client (local) version
        print("[SyncConflictResolver] Client wins: chose local version")
        return conflict.localVersion
    }
    
    private func resolveFieldLevelMerge(_ conflict: SyncConflict) async -> Any? {
        // Perform field-by-field merging
        if let localAffirmation = conflict.localVersion as? any AffirmationProtocol,
           let remoteAffirmation = conflict.remoteVersion as? any AffirmationProtocol {
            
            return await performFieldLevelMerge(local: localAffirmation, remote: remoteAffirmation)
        }
        
        return conflict.localVersion
    }
    
    private func resolveTimestampBased(_ conflict: SyncConflict) async -> Any? {
        // Use timestamp-based resolution with additional logic
        if let localAffirmation = conflict.localVersion as? any AffirmationProtocol,
           let remoteAffirmation = conflict.remoteVersion as? any AffirmationProtocol {
            
            // Consider both update time and creation time
            let localScore = localAffirmation.updatedAt.timeIntervalSince1970 + localAffirmation.createdAt.timeIntervalSince1970
            let remoteScore = remoteAffirmation.updatedAt.timeIntervalSince1970 + remoteAffirmation.createdAt.timeIntervalSince1970
            
            let resolution = localScore > remoteScore ? localAffirmation : remoteAffirmation
            print("[SyncConflictResolver] Timestamp-based: chose \(localScore > remoteScore ? "local" : "remote") version")
            return resolution
        }
        
        return conflict.localVersion
    }
    
    private func resolveUserPrompt(_ conflict: SyncConflict) async -> Any? {
        // In a real implementation, this would prompt the user
        // For now, fall back to last write wins
        print("[SyncConflictResolver] User prompt required - falling back to last write wins")
        return await resolveLastWriteWins(conflict)
    }
    
    private func performIntelligentMerge(local: any AffirmationProtocol, remote: any AffirmationProtocol) async -> any AffirmationProtocol {
        // Create an intelligent merge of both versions
        return MergedSyncAffirmation(
            id: local.id,
            text: selectBestText(local.text, remote.text),
            category: local.category, // Prefer local category changes
            isFavorite: local.isFavorite || remote.isFavorite, // Union of favorites
            recordingURL: local.recordingURL ?? remote.recordingURL,
            currentRepetitions: max(local.currentRepetitions, remote.currentRepetitions),
            completedCycles: max(local.completedCycles, remote.completedCycles),
            energyLevel: (local.energyLevel + remote.energyLevel) / 2.0,
            notes: mergeNotes(local.notes, remote.notes),
            updatedAt: max(local.updatedAt, remote.updatedAt)
        )
    }
    
    private func performFieldLevelMerge(local: any AffirmationProtocol, remote: any AffirmationProtocol) async -> any AffirmationProtocol {
        // Perform field-by-field intelligent merging
        return MergedSyncAffirmation(
            id: local.id,
            text: selectBestText(local.text, remote.text),
            category: local.category, // Assume local category is intentional
            isFavorite: local.isFavorite, // Prefer local favorite status
            recordingURL: local.recordingURL ?? remote.recordingURL,
            currentRepetitions: max(local.currentRepetitions, remote.currentRepetitions),
            completedCycles: max(local.completedCycles, remote.completedCycles),
            energyLevel: local.energyLevel > 0 ? local.energyLevel : remote.energyLevel,
            notes: mergeNotes(local.notes, remote.notes),
            updatedAt: Date() // Set to current time for merged result
        )
    }
    
    private func selectBestText(_ localText: String, _ remoteText: String) -> String {
        // Prefer longer, more descriptive text
        return localText.count > remoteText.count ? localText : remoteText
    }
    
    private func mergeNotes(_ localNotes: String?, _ remoteNotes: String?) -> String? {
        switch (localNotes, remoteNotes) {
        case (let local?, let remote?):
            return "\(remote)\n---\n\(local)"
        case (let local?, nil):
            return local
        case (nil, let remote?):
            return remote
        case (nil, nil):
            return nil
        }
    }
    
    private func updateResolutionStatistics(resolutionTime: TimeInterval) {
        resolutionStatistics.strategyUsage[strategy, default: 0] += 1
        
        let totalResolved = resolutionStatistics.resolvedConflicts + resolutionStatistics.unresolvedConflicts
        let currentAverage = resolutionStatistics.averageResolutionTime
        
        resolutionStatistics.averageResolutionTime = (currentAverage * Double(totalResolved - 1) + resolutionTime) / Double(totalResolved)
    }
    
    private func recordConflictResolution(conflict: SyncConflict, resolutionTime: TimeInterval, success: Bool) {
        let record = ResolvedConflict(
            conflictId: conflict.id,
            entityId: conflict.entityId,
            entityType: conflict.entityType,
            conflictType: conflict.conflictType,
            strategy: strategy,
            resolutionTime: resolutionTime,
            resolvedAt: Date(),
            successful: success
        )
        
        conflictHistory.append(record)
        
        // Maintain history size
        if conflictHistory.count > 1000 {
            conflictHistory.removeFirst()
        }
    }
}

// MARK: - Merged Sync Affirmation

/// Merged affirmation result from sync conflict resolution
public struct MergedSyncAffirmation: AffirmationProtocol {
    public let id: UUID
    public let text: String
    public let category: AffirmationCategory
    public let isFavorite: Bool
    public let hasRecording: Bool
    public let todayProgress: Double = 0.0
    public let cycleProgress: Double = 0.0
    public let recordingURL: URL?
    public let completedCycles: Int
    public let currentRepetitions: Int
    public let lastRepetitionDate: Date? = nil
    public let energyLevel: Double
    public let moodRating: Int? = nil
    public let notes: String?
    public let playCount: Int = 0
    public let hasActiveCycle: Bool = false
    public let currentCycleDay: Int = 0
    public let cycleStartDate: Date? = nil
    public let dailyProgress: [Date: Int] = [:]
    public let isCurrentCycleComplete: Bool = false
    public let hasTodayQuotaMet: Bool = false
    public let canPerformRepetition: Bool = true
    public let createdAt: Date = Date()
    public let updatedAt: Date
    public var longestStreak: Int = 0
    
    public init(
        id: UUID,
        text: String,
        category: AffirmationCategory,
        isFavorite: Bool,
        recordingURL: URL?,
        currentRepetitions: Int,
        completedCycles: Int,
        energyLevel: Double,
        notes: String?,
        updatedAt: Date
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.isFavorite = isFavorite
        self.recordingURL = recordingURL
        self.hasRecording = recordingURL != nil
        self.currentRepetitions = currentRepetitions
        self.completedCycles = completedCycles
        self.energyLevel = energyLevel
        self.notes = notes
        self.updatedAt = updatedAt
    }
    
    public func recordRepetition() throws {
        // No-op for merged affirmation
    }

    public func updateEnergyLevel(_ level: Double) {
        // No-op for merged affirmation
    }

    public func recordMood(_ rating: Int, notes: String?) {
        // No-op for merged affirmation
    }
}
