import Foundation
import Combine
import NeuroLoopInterfaces

/// Advanced cache performance monitoring and optimization service
@MainActor
public final class CachePerformanceMonitor: ObservableObject {
    
    // MARK: - Singleton
    
    public static let shared = CachePerformanceMonitor()
    
    // MARK: - Properties
    
    private let cacheManager: MultiLevelCacheManager
    private let invalidationService: SmartCacheInvalidationService
    private var cancellables = Set<AnyCancellable>()
    
    @Published public private(set) var performanceMetrics = CachePerformanceMetrics()
    @Published public private(set) var recommendations: [CacheRecommendation] = []
    @Published public private(set) var isMonitoring = false
    
    // MARK: - Performance Metrics
    
    public struct CachePerformanceMetrics {
        var overallHitRatio: Double = 0.0
        var memoryHitRatio: Double = 0.0
        var diskHitRatio: Double = 0.0
        var averageResponseTime: TimeInterval = 0.0
        var cacheEfficiency: Double = 0.0
        var memoryUsage: Int = 0
        var diskUsage: Int = 0
        var totalRequests: Int = 0
        var lastUpdated: Date = Date()
        
        // Performance indicators
        var isPerformingWell: Bool {
            overallHitRatio >= 0.8 && averageResponseTime <= 0.1
        }
        
        var needsOptimization: Bool {
            overallHitRatio < 0.6 || averageResponseTime > 0.2
        }
    }
    
    // MARK: - Cache Recommendation
    
    public struct CacheRecommendation {
        let type: RecommendationType
        let priority: Priority
        let description: String
        let estimatedImpact: String
        let actionRequired: String
        
        enum RecommendationType {
            case increaseMemoryCache
            case optimizeDiskCache
            case adjustTTL
            case improveWarmingStrategy
            case reduceInvalidation
            case compressData
            case cleanupExpired
        }
        
        enum Priority {
            case low, medium, high, critical
        }
    }
    
    // MARK: - Monitoring Configuration
    
    private struct MonitoringConfig {
        let samplingInterval: TimeInterval = 60 // 1 minute
        let analysisInterval: TimeInterval = 300 // 5 minutes
        let recommendationInterval: TimeInterval = 900 // 15 minutes
        let performanceThresholds = PerformanceThresholds()
    }
    
    private struct PerformanceThresholds {
        let minHitRatio: Double = 0.7
        let maxResponseTime: TimeInterval = 0.15
        let maxMemoryUsage: Int = 100 * 1024 * 1024 // 100MB
        let maxDiskUsage: Int = 500 * 1024 * 1024 // 500MB
    }
    
    private let config = MonitoringConfig()
    
    // MARK: - Initialization
    
    private init() {
        self.cacheManager = MultiLevelCacheManager.shared
        self.invalidationService = SmartCacheInvalidationService.shared
        
        setupMonitoring()
        print("[CachePerformanceMonitor] Initialized with performance monitoring")
    }
    
    // MARK: - Public API
    
    /// Start performance monitoring
    public func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        // Start periodic sampling
        Timer.scheduledTimer(withTimeInterval: config.samplingInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.collectMetrics()
            }
        }
        
        // Start periodic analysis
        Timer.scheduledTimer(withTimeInterval: config.analysisInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.analyzePerformance()
            }
        }
        
        // Start periodic recommendations
        Timer.scheduledTimer(withTimeInterval: config.recommendationInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.generateRecommendations()
            }
        }
        
        print("[CachePerformanceMonitor] Started performance monitoring")
    }
    
    /// Stop performance monitoring
    public func stopMonitoring() {
        isMonitoring = false
        print("[CachePerformanceMonitor] Stopped performance monitoring")
    }
    
    /// Get detailed performance report
    public func getDetailedReport() async -> CachePerformanceReport {
        await collectMetrics()
        await generateRecommendations()
        
        return CachePerformanceReport(
            metrics: performanceMetrics,
            recommendations: recommendations,
            timestamp: Date()
        )
    }
    
    /// Apply automatic optimizations
    public func applyAutomaticOptimizations() async {
        print("[CachePerformanceMonitor] Applying automatic optimizations")
        
        for recommendation in recommendations where recommendation.priority == .high || recommendation.priority == .critical {
            await applyRecommendation(recommendation)
        }
    }
    
    // MARK: - Private Methods
    
    private func collectMetrics() async {
        let startTime = Date()
        
        // Collect cache statistics
        let cacheStats = cacheManager.cacheStats
        let invalidationStats = invalidationService.invalidationStats
        
        // Update performance metrics
        performanceMetrics.overallHitRatio = cacheStats.hitRatio
        performanceMetrics.memoryHitRatio = cacheStats.memoryHitRatio
        performanceMetrics.totalRequests = cacheStats.totalRequests
        performanceMetrics.lastUpdated = Date()
        
        // Calculate response time (simplified)
        performanceMetrics.averageResponseTime = Date().timeIntervalSince(startTime)
        
        // Calculate efficiency
        performanceMetrics.cacheEfficiency = calculateEfficiency()
        
        print("[CachePerformanceMonitor] Collected metrics - Hit Ratio: \(String(format: "%.2f", performanceMetrics.overallHitRatio))")
    }
    
    private func analyzePerformance() async {
        let metrics = performanceMetrics
        
        // Check for performance issues
        if metrics.overallHitRatio < config.performanceThresholds.minHitRatio {
            print("[CachePerformanceMonitor] ⚠️ Low hit ratio detected: \(String(format: "%.2f", metrics.overallHitRatio))")
        }
        
        if metrics.averageResponseTime > config.performanceThresholds.maxResponseTime {
            print("[CachePerformanceMonitor] ⚠️ High response time detected: \(String(format: "%.3f", metrics.averageResponseTime))s")
        }
        
        // Analyze trends
        analyzeTrends()
    }
    
    private func generateRecommendations() async {
        var newRecommendations: [CacheRecommendation] = []
        let metrics = performanceMetrics
        
        // Low hit ratio recommendations
        if metrics.overallHitRatio < 0.6 {
            newRecommendations.append(CacheRecommendation(
                type: .increaseMemoryCache,
                priority: .high,
                description: "Hit ratio is below 60%, consider increasing memory cache size",
                estimatedImpact: "Could improve hit ratio by 15-25%",
                actionRequired: "Increase memory cache capacity"
            ))
        }
        
        // High response time recommendations
        if metrics.averageResponseTime > 0.2 {
            newRecommendations.append(CacheRecommendation(
                type: .optimizeDiskCache,
                priority: .medium,
                description: "Response time is high, disk cache may need optimization",
                estimatedImpact: "Could reduce response time by 30-50%",
                actionRequired: "Optimize disk cache structure"
            ))
        }
        
        // Memory usage recommendations
        if metrics.memoryUsage > config.performanceThresholds.maxMemoryUsage {
            newRecommendations.append(CacheRecommendation(
                type: .compressData,
                priority: .medium,
                description: "Memory usage is high, enable compression",
                estimatedImpact: "Could reduce memory usage by 40-60%",
                actionRequired: "Enable data compression"
            ))
        }
        
        // Cache warming recommendations
        if metrics.memoryHitRatio < 0.4 {
            newRecommendations.append(CacheRecommendation(
                type: .improveWarmingStrategy,
                priority: .medium,
                description: "Low memory hit ratio suggests poor cache warming",
                estimatedImpact: "Could improve memory hit ratio by 20-30%",
                actionRequired: "Improve cache warming strategy"
            ))
        }
        
        recommendations = newRecommendations
        
        if !recommendations.isEmpty {
            print("[CachePerformanceMonitor] Generated \(recommendations.count) recommendations")
        }
    }
    
    private func applyRecommendation(_ recommendation: CacheRecommendation) async {
        switch recommendation.type {
        case .cleanupExpired:
            await invalidationService.performTimeBasedInvalidation()
        case .compressData:
            // This would enable compression in cache manager
            print("[CachePerformanceMonitor] Applied compression optimization")
        case .improveWarmingStrategy:
            // This would trigger intelligent cache warming
            await CacheWarmingService.shared.warmCacheIntelligently()
        default:
            print("[CachePerformanceMonitor] Recommendation \(recommendation.type) requires manual intervention")
        }
    }
    
    private func calculateEfficiency() -> Double {
        let hitRatio = performanceMetrics.overallHitRatio
        let responseTime = performanceMetrics.averageResponseTime
        
        // Simple efficiency calculation
        let timeScore = max(0, 1.0 - (responseTime / 0.5)) // Normalize to 0.5s max
        return (hitRatio + timeScore) / 2.0
    }
    
    private func analyzeTrends() {
        // This would analyze historical data for trends
        // Simplified implementation
        print("[CachePerformanceMonitor] Analyzing performance trends")
    }
    
    private func setupMonitoring() {
        // Listen for cache events
        NotificationCenter.default.publisher(for: Notification.Name("CacheHit"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.collectMetrics()
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: Notification.Name("CacheMiss"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.collectMetrics()
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - Performance Report

public struct CachePerformanceReport {
    public let metrics: CachePerformanceMonitor.CachePerformanceMetrics
    public let recommendations: [CachePerformanceMonitor.CacheRecommendation]
    public let timestamp: Date
    
    public var summary: String {
        let hitRatio = String(format: "%.1f%%", metrics.overallHitRatio * 100)
        let responseTime = String(format: "%.3fs", metrics.averageResponseTime)
        let efficiency = String(format: "%.1f%%", metrics.cacheEfficiency * 100)
        
        return """
        Cache Performance Report
        ========================
        Hit Ratio: \(hitRatio)
        Response Time: \(responseTime)
        Efficiency: \(efficiency)
        Recommendations: \(recommendations.count)
        Status: \(metrics.isPerformingWell ? "✅ Good" : "⚠️ Needs Attention")
        """
    }
}
