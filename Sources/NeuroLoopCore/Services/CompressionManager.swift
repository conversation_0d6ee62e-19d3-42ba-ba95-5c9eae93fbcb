import Foundation
import Compression

/// Manages data compression for efficient synchronization
@MainActor
public final class CompressionManager: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public struct Configuration {
        let algorithm: Algorithm
        let compressionLevel: Int
        let minSizeThreshold: Int
        let maxCompressionRatio: Double
        
        public static let `default` = Configuration(
            algorithm: .lzfse,
            compressionLevel: 5,
            minSizeThreshold: 1024, // 1KB
            maxCompressionRatio: 0.9 // Don't compress if result is > 90% of original
        )
    }
    
    public enum Algorithm {
        case lzfse
        case lz4
        case lzma
        case zlib
        
        var compressionAlgorithm: compression_algorithm {
            switch self {
            case .lzfse:
                return COMPRESSION_LZFSE
            case .lz4:
                return COMPRESSION_LZ4
            case .lzma:
                return COMPRESSION_LZMA
            case .zlib:
                return COMPRESSION_ZLIB
            }
        }
    }
    
    // MARK: - Properties
    
    private let config: Configuration
    private let enabled: Bool
    private var compressionStatistics = CompressionStatistics()
    
    // MARK: - Statistics
    
    public struct CompressionStatistics {
        var totalCompressions: Int = 0
        var successfulCompressions: Int = 0
        var failedCompressions: Int = 0
        var totalOriginalSize: Int = 0
        var totalCompressedSize: Int = 0
        var averageCompressionRatio: Double = 0.0
        var averageCompressionTime: TimeInterval = 0.0
        var bytesSaved: Int = 0
        
        var compressionRate: Double {
            guard totalCompressions > 0 else { return 0.0 }
            return Double(successfulCompressions) / Double(totalCompressions)
        }
        
        var overallCompressionRatio: Double {
            guard totalOriginalSize > 0 else { return 0.0 }
            return Double(totalCompressedSize) / Double(totalOriginalSize)
        }
        
        var spaceSavings: Double {
            guard totalOriginalSize > 0 else { return 0.0 }
            return 1.0 - overallCompressionRatio
        }
    }
    
    // MARK: - Initialization
    
    public init(enabled: Bool = true, config: Configuration = .default) {
        self.enabled = enabled
        self.config = config
        print("[CompressionManager] Initialized with \(config.algorithm) compression: \(enabled ? "enabled" : "disabled")")
    }
    
    // MARK: - Public API
    
    /// Compress sync changes
    public func compressChanges(_ changes: [SyncChange]) async {
        guard enabled else { return }
        
        let startTime = Date()
        var compressedCount = 0
        
        for change in changes {
            if await compressChange(change) {
                compressedCount += 1
            }
        }
        
        let compressionTime = Date().timeIntervalSince(startTime)
        updateCompressionStatistics(compressionTime: compressionTime)
        
        print("[CompressionManager] Compressed \(compressedCount) of \(changes.count) changes in \(String(format: "%.3f", compressionTime))s")
    }
    
    /// Compress data
    public func compressData(_ data: Data) async -> CompressedData? {
        guard enabled else { return CompressedData(originalData: data, compressed: false) }
        guard data.count >= config.minSizeThreshold else {
            return CompressedData(originalData: data, compressed: false)
        }
        
        let startTime = Date()
        compressionStatistics.totalCompressions += 1
        
        do {
            let compressedData = try await performCompression(data)
            let compressionRatio = Double(compressedData.count) / Double(data.count)
            
            // Only use compressed data if it's significantly smaller
            if compressionRatio <= config.maxCompressionRatio {
                let compressionTime = Date().timeIntervalSince(startTime)
                recordSuccessfulCompression(
                    originalSize: data.count,
                    compressedSize: compressedData.count,
                    compressionTime: compressionTime
                )
                
                return CompressedData(
                    originalData: data,
                    compressedData: compressedData,
                    compressed: true,
                    algorithm: config.algorithm,
                    compressionRatio: compressionRatio
                )
            } else {
                compressionStatistics.failedCompressions += 1
                return CompressedData(originalData: data, compressed: false)
            }
            
        } catch {
            compressionStatistics.failedCompressions += 1
            print("[CompressionManager] Compression failed: \(error)")
            return CompressedData(originalData: data, compressed: false)
        }
    }
    
    /// Decompress data
    public func decompressData(_ compressedData: CompressedData) async -> Data? {
        guard compressedData.compressed else {
            return compressedData.originalData
        }
        
        guard let data = compressedData.compressedData else {
            return compressedData.originalData
        }
        
        do {
            return try await performDecompression(data, algorithm: compressedData.algorithm)
        } catch {
            print("[CompressionManager] Decompression failed: \(error)")
            return compressedData.originalData
        }
    }
    
    /// Get compression statistics
    public func getStatistics() -> CompressionStatistics {
        return compressionStatistics
    }
    
    /// Reset statistics
    public func resetStatistics() {
        compressionStatistics = CompressionStatistics()
        print("[CompressionManager] Statistics reset")
    }
    
    /// Check if compression is beneficial for data size
    public func shouldCompress(dataSize: Int) -> Bool {
        return enabled && dataSize >= config.minSizeThreshold
    }
    
    // MARK: - Private Implementation
    
    private func compressChange(_ change: SyncChange) async -> Bool {
        guard let data = change.data else { return false }
        
        do {
            let encoder = JSONEncoder()
            let jsonData = try encoder.encode(AnyEncodable(data))
            
            if let compressedData = await compressData(jsonData) {
                return compressedData.compressed
            }
        } catch {
            print("[CompressionManager] Failed to encode change data: \(error)")
        }
        
        return false
    }
    
    private func performCompression(_ data: Data) async throws -> Data {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .utility).async {
                do {
                    let compressedData = try data.compressed(using: self.config.algorithm.compressionAlgorithm)
                    continuation.resume(returning: compressedData)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func performDecompression(_ data: Data, algorithm: Algorithm) async throws -> Data {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .utility).async {
                do {
                    let decompressedData = try data.decompressed(using: algorithm.compressionAlgorithm)
                    continuation.resume(returning: decompressedData)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func recordSuccessfulCompression(originalSize: Int, compressedSize: Int, compressionTime: TimeInterval) {
        compressionStatistics.successfulCompressions += 1
        compressionStatistics.totalOriginalSize += originalSize
        compressionStatistics.totalCompressedSize += compressedSize
        compressionStatistics.bytesSaved += (originalSize - compressedSize)
        
        let compressionRatio = Double(compressedSize) / Double(originalSize)
        updateAverageCompressionRatio(compressionRatio)
        updateAverageCompressionTime(compressionTime)
    }
    
    private func updateCompressionStatistics(compressionTime: TimeInterval) {
        updateAverageCompressionTime(compressionTime)
    }
    
    private func updateAverageCompressionRatio(_ ratio: Double) {
        let totalSuccessful = compressionStatistics.successfulCompressions
        let currentAverage = compressionStatistics.averageCompressionRatio
        
        compressionStatistics.averageCompressionRatio = (currentAverage * Double(totalSuccessful - 1) + ratio) / Double(totalSuccessful)
    }
    
    private func updateAverageCompressionTime(_ time: TimeInterval) {
        let totalCompressions = compressionStatistics.totalCompressions
        let currentAverage = compressionStatistics.averageCompressionTime
        
        compressionStatistics.averageCompressionTime = (currentAverage * Double(totalCompressions - 1) + time) / Double(totalCompressions)
    }
}

// MARK: - Supporting Types

/// Container for compressed data with metadata
public struct CompressedData {
    public let originalData: Data
    public let compressedData: Data?
    public let compressed: Bool
    public let algorithm: CompressionManager.Algorithm
    public let compressionRatio: Double
    public let timestamp: Date
    
    init(
        originalData: Data,
        compressedData: Data? = nil,
        compressed: Bool,
        algorithm: CompressionManager.Algorithm = .lzfse,
        compressionRatio: Double = 1.0
    ) {
        self.originalData = originalData
        self.compressedData = compressedData
        self.compressed = compressed
        self.algorithm = algorithm
        self.compressionRatio = compressionRatio
        self.timestamp = Date()
    }
    
    /// Get the data to use (compressed or original)
    public var dataToUse: Data {
        return compressed ? (compressedData ?? originalData) : originalData
    }
    
    /// Get size savings in bytes
    public var bytesSaved: Int {
        guard compressed, let compressedData = compressedData else { return 0 }
        return originalData.count - compressedData.count
    }
    
    /// Get space savings percentage
    public var spaceSavings: Double {
        return 1.0 - compressionRatio
    }
}

/// Wrapper for making any value encodable
private struct AnyEncodable: Encodable {
    private let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case let string as String:
            try container.encode(string)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let bool as Bool:
            try container.encode(bool)
        case let date as Date:
            try container.encode(date)
        case let data as Data:
            try container.encode(data)
        default:
            try container.encode("Unsupported type")
        }
    }
}

// MARK: - Data Extensions

extension Data {
    func compressed(using algorithm: compression_algorithm) throws -> Data {
        return try self.withUnsafeBytes { bytes in
            let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: count)
            defer { buffer.deallocate() }
            
            let compressedSize = compression_encode_buffer(
                buffer, count,
                bytes.bindMemory(to: UInt8.self).baseAddress!, count,
                nil, algorithm
            )
            
            guard compressedSize > 0 else {
                throw CompressionError.compressionFailed
            }
            
            return Data(bytes: buffer, count: compressedSize)
        }
    }
    
    func decompressed(using algorithm: compression_algorithm) throws -> Data {
        return try self.withUnsafeBytes { bytes in
            let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: count * 4) // Assume max 4x expansion
            defer { buffer.deallocate() }
            
            let decompressedSize = compression_decode_buffer(
                buffer, count * 4,
                bytes.bindMemory(to: UInt8.self).baseAddress!, count,
                nil, algorithm
            )
            
            guard decompressedSize > 0 else {
                throw CompressionError.decompressionFailed
            }
            
            return Data(bytes: buffer, count: decompressedSize)
        }
    }
}

// MARK: - Compression Error

enum CompressionError: Error {
    case compressionFailed
    case decompressionFailed
    case unsupportedAlgorithm
    case invalidData
}
