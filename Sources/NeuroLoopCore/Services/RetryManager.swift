import Foundation

/// Advanced retry manager with exponential backoff and circuit breaker pattern
@MainActor
public final class RetryManager: @unchecked Sendable {
    
    // MARK: - Configuration
    
    public struct Configuration {
        let maxRetryAttempts: Int
        let baseRetryDelay: TimeInterval
        let maxRetryDelay: TimeInterval
        let backoffMultiplier: Double
        let jitterEnabled: Bool
        
        public init(
            maxRetryAttempts: Int = 3,
            baseRetryDelay: TimeInterval = 1.0,
            maxRetryDelay: TimeInterval = 30.0,
            backoffMultiplier: Double = 2.0,
            jitterEnabled: Bool = true
        ) {
            self.maxRetryAttempts = maxRetryAttempts
            self.baseRetryDelay = baseRetryDelay
            self.maxRetryDelay = maxRetryDelay
            self.backoffMultiplier = backoffMultiplier
            self.jitterEnabled = jitterEnabled
        }
    }
    
    // MARK: - Circuit Breaker
    
    private enum CircuitState {
        case closed
        case open
        case halfOpen
    }
    
    private struct CircuitBreaker {
        var state: CircuitState = .closed
        var failureCount: Int = 0
        var lastFailureTime: Date?
        var successCount: Int = 0
        
        let failureThreshold: Int = 5
        let recoveryTimeout: TimeInterval = 60.0
        let halfOpenSuccessThreshold: Int = 3
        
        mutating func recordSuccess() {
            switch state {
            case .closed:
                failureCount = 0
            case .halfOpen:
                successCount += 1
                if successCount >= halfOpenSuccessThreshold {
                    state = .closed
                    failureCount = 0
                    successCount = 0
                }
            case .open:
                break
            }
        }
        
        mutating func recordFailure() {
            failureCount += 1
            lastFailureTime = Date()
            
            switch state {
            case .closed:
                if failureCount >= failureThreshold {
                    state = .open
                }
            case .halfOpen:
                state = .open
                successCount = 0
            case .open:
                break
            }
        }
        
        mutating func canExecute() -> Bool {
            switch state {
            case .closed:
                return true
            case .open:
                guard let lastFailure = lastFailureTime,
                      Date().timeIntervalSince(lastFailure) >= recoveryTimeout else {
                    return false
                }
                state = .halfOpen
                successCount = 0
                return true
            case .halfOpen:
                return true
            }
        }
    }
    
    // MARK: - Properties
    
    private let config: Configuration
    private var circuitBreaker = CircuitBreaker()
    private var retryStatistics = RetryStatistics()
    
    // MARK: - Statistics
    
    public struct RetryStatistics {
        var totalAttempts: Int = 0
        var successfulRetries: Int = 0
        var failedRetries: Int = 0
        var circuitBreakerTrips: Int = 0
        var averageRetryDelay: TimeInterval = 0.0
        
        var retrySuccessRate: Double {
            guard totalAttempts > 0 else { return 0.0 }
            return Double(successfulRetries) / Double(totalAttempts)
        }
    }
    
    // MARK: - Initialization
    
    public init(config: EnhancedOptimisticUpdateManager.Configuration) {
        self.config = Configuration(
            maxRetryAttempts: config.maxRetryAttempts,
            baseRetryDelay: config.baseRetryDelay
        )
    }
    
    // MARK: - Retry Execution
    
    /// Execute operation with retry logic and circuit breaker
    public func executeWithRetry<T>(
        operation: @escaping () async -> T?,
        maxAttempts: Int? = nil,
        baseDelay: TimeInterval? = nil,
        onResult: @escaping (Bool, T?) async -> Void
    ) async -> T? {
        let attempts = maxAttempts ?? config.maxRetryAttempts
        let delay = baseDelay ?? config.baseRetryDelay
        
        guard circuitBreaker.canExecute() else {
            print("[RetryManager] Circuit breaker is open, operation blocked")
            retryStatistics.circuitBreakerTrips += 1
            await onResult(false, nil)
            return nil
        }
        
        for attempt in 1...attempts {
            retryStatistics.totalAttempts += 1
            
            let result = await operation()
            
            if result != nil {
                circuitBreaker.recordSuccess()
                retryStatistics.successfulRetries += 1
                await onResult(true, result)
                return result
            } else {
                circuitBreaker.recordFailure()
                retryStatistics.failedRetries += 1
                
                if attempt < attempts {
                    let retryDelay = calculateRetryDelay(attempt: attempt, baseDelay: delay)
                    print("[RetryManager] Attempt \(attempt) failed, retrying in \(retryDelay)s")
                    
                    try? await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                } else {
                    print("[RetryManager] All \(attempts) attempts failed")
                    await onResult(false, nil)
                }
            }
        }
        
        return nil
    }
    
    // MARK: - Delay Calculation
    
    private func calculateRetryDelay(attempt: Int, baseDelay: TimeInterval) -> TimeInterval {
        let exponentialDelay = baseDelay * pow(config.backoffMultiplier, Double(attempt - 1))
        let cappedDelay = min(exponentialDelay, config.maxRetryDelay)
        
        if config.jitterEnabled {
            // Add jitter to prevent thundering herd
            let jitter = Double.random(in: 0.0...0.1) * cappedDelay
            return cappedDelay + jitter
        }
        
        return cappedDelay
    }
    
    // MARK: - Public API
    
    /// Get current retry statistics
    public func getStatistics() -> RetryStatistics {
        return retryStatistics
    }
    
    /// Reset circuit breaker manually
    public func resetCircuitBreaker() {
        circuitBreaker = CircuitBreaker()
        print("[RetryManager] Circuit breaker reset")
    }
    
    /// Check if circuit breaker is open
    public func isCircuitBreakerOpen() -> Bool {
        return circuitBreaker.state == .open
    }
}
