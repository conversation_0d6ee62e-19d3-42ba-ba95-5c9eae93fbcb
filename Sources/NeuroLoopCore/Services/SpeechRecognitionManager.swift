import AVFoundation
import Speech
import Foundation

/// Optimized speech recognition manager with pre-initialization and background setup
@MainActor
public final class SpeechRecognitionManager: ObservableObject, @unchecked Sendable {
    // MARK: - Shared Instance
    
    public static let shared = SpeechRecognitionManager()
    
    // MARK: - Published Properties
    
    @Published public private(set) var isReady = false
    @Published public private(set) var isInitializing = false
    @Published public private(set) var authorizationStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    @Published public private(set) var isAvailable = false
    
    // MARK: - Private Properties
    
    private var speechRecognizer: SFSpeechRecognizer?
    private var audioEngine: AVAudioEngine?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    
    private var preInitializationTask: Task<Void, Never>?
    
    // MARK: - Initialization
    
    private init() {
        print("[SpeechRecognitionManager] Initializing speech recognition manager")
        setupInitialState()
    }
    
    private func setupInitialState() {
        authorizationStatus = SFSpeechRecognizer.authorizationStatus()
        
        // Check if speech recognition is available
        let preferredLocale = Locale.current
        let localeIdentifier = preferredLocale.language.languageCode?.identifier ?? "en"
        let recognizerLocale = Locale(identifier: "\(localeIdentifier)-US")
        
        speechRecognizer = SFSpeechRecognizer(locale: recognizerLocale)
        isAvailable = speechRecognizer?.isAvailable ?? false
        
        print("[SpeechRecognitionManager] Initial setup - Available: \(isAvailable), Auth: \(authorizationStatus)")
    }
    
    // MARK: - Pre-initialization
    
    /// Pre-warm speech recognition components in background
    public func preInitialize() async {
        guard preInitializationTask == nil else {
            await preInitializationTask?.value
            return
        }
        
        preInitializationTask = Task { @MainActor in
            await performPreInitialization()
        }
        
        await preInitializationTask?.value
    }
    
    private func performPreInitialization() async {
        guard !isReady else { return }
        
        isInitializing = true
        
        print("[SpeechRecognitionManager] Starting pre-initialization...")

        // 1. Setup audio session in background
        await setupAudioSession()

        // 2. Pre-warm audio engine
        await setupAudioEngine()

        // 3. Request permissions if needed
        await requestPermissionsIfNeeded()

        // 4. Validate speech recognizer
        await validateSpeechRecognizer()

        isReady = true
        print("[SpeechRecognitionManager] Pre-initialization completed successfully")
        
        isInitializing = false
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() async {
        await withCheckedContinuation { continuation in
            Task {
                do {
                    let audioSession = AVAudioSession.sharedInstance()
                    
                    // Configure audio session for recording
                    try audioSession.setCategory(
                        .playAndRecord,
                        mode: .measurement,
                        options: [.duckOthers, .defaultToSpeaker]
                    )
                    
                    try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                    
                    print("[SpeechRecognitionManager] Audio session configured successfully")
                    continuation.resume()
                    
                } catch {
                    print("[SpeechRecognitionManager] Audio session setup failed: \(error)")
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Audio Engine Setup
    
    private func setupAudioEngine() async {
        await withCheckedContinuation { continuation in
            Task {
                do {
                    let engine = AVAudioEngine()
                    let inputNode = engine.inputNode
                    let recordingFormat = inputNode.outputFormat(forBus: 0)
                    
                    // Validate recording format
                    guard recordingFormat.sampleRate > 0 else {
                        print("[SpeechRecognitionManager] Invalid recording format")
                        continuation.resume()
                        return
                    }
                    
                    // Pre-prepare the audio engine (don't start yet)
                    engine.prepare()
                    
                    audioEngine = engine
                    
                    print("[SpeechRecognitionManager] Audio engine prepared successfully")
                    print("[SpeechRecognitionManager] Recording format: \(recordingFormat)")
                    
                    continuation.resume()
                    
                } catch {
                    print("[SpeechRecognitionManager] Audio engine setup failed: \(error)")
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Permission Management
    
    private func requestPermissionsIfNeeded() async {
        // Request speech recognition permission if not determined
        if authorizationStatus == .notDetermined {
            print("[SpeechRecognitionManager] Requesting speech recognition permission...")
            
            let granted = await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { status in
                    continuation.resume(returning: status == .authorized)
                }
            }
            
            await MainActor.run {
                authorizationStatus = SFSpeechRecognizer.authorizationStatus()
            }
            
            print("[SpeechRecognitionManager] Speech permission result: \(granted)")
        }
        
        // Request microphone permission
        let microphonePermission = await AVAudioApplication.requestRecordPermission()
        print("[SpeechRecognitionManager] Microphone permission: \(microphonePermission)")
    }
    
    private func validateSpeechRecognizer() async {
        guard let recognizer = speechRecognizer else {
            print("[SpeechRecognitionManager] Speech recognizer not available")
            return
        }
        
        isAvailable = recognizer.isAvailable
        
        if !isAvailable {
            print("[SpeechRecognitionManager] Speech recognizer not available for current locale")
        } else {
            print("[SpeechRecognitionManager] Speech recognizer validated successfully")
        }
    }
    
    // MARK: - Quick Start Recognition
    
    /// Start speech recognition with minimal setup time (uses pre-initialized components)
    public func startQuickRecognition(
        contextualStrings: [String] = [],
        partialResultsHandler: @escaping (String) -> Void,
        finalResultHandler: @escaping (String, Bool) -> Void,
        errorHandler: @escaping (Error) -> Void
    ) async {
        guard isReady else {
            print("[SpeechRecognitionManager] Not ready - call preInitialize() first")
            errorHandler(SpeechRecognitionError.notReady)
            return
        }
        
        guard authorizationStatus == .authorized else {
            print("[SpeechRecognitionManager] Speech recognition not authorized")
            errorHandler(SpeechRecognitionError.notAuthorized)
            return
        }
        
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("[SpeechRecognitionManager] Speech recognizer not available")
            errorHandler(SpeechRecognitionError.recognizerUnavailable)
            return
        }
        
        guard let audioEngine = audioEngine else {
            print("[SpeechRecognitionManager] Audio engine not initialized")
            errorHandler(SpeechRecognitionError.audioEngineNotReady)
            return
        }
        
        do {
            // Stop any existing recognition
            await stopRecognition()
            
            // Create recognition request
            let request = SFSpeechAudioBufferRecognitionRequest()
            request.shouldReportPartialResults = true
            request.contextualStrings = contextualStrings
            
            recognitionRequest = request
            
            // Setup audio tap
            let inputNode = audioEngine.inputNode
            let recordingFormat = inputNode.outputFormat(forBus: 0)
            
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                request.append(buffer)
            }
            
            // Start audio engine
            try audioEngine.start()
            
            // Start recognition task
            recognitionTask = speechRecognizer.recognitionTask(with: request) { result, error in
                Task { @MainActor in
                    if let result = result {
                        let recognizedText = result.bestTranscription.formattedString
                        partialResultsHandler(recognizedText)
                        
                        if result.isFinal {
                            finalResultHandler(recognizedText, true)
                        }
                    }
                    
                    if let error = error {
                        errorHandler(error)
                    }
                }
            }
            
            print("[SpeechRecognitionManager] Quick recognition started successfully")
            
        } catch {
            print("[SpeechRecognitionManager] Failed to start quick recognition: \(error)")
            errorHandler(error)
        }
    }
    
    /// Stop speech recognition
    public func stopRecognition() async {
        recognitionTask?.cancel()
        recognitionTask = nil
        
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        
        print("[SpeechRecognitionManager] Recognition stopped")
    }
    
    // MARK: - Cleanup
    
    public func cleanup() async {
        await stopRecognition()
        
        audioEngine = nil
        speechRecognizer = nil
        
        isReady = false
        
        print("[SpeechRecognitionManager] Cleanup completed")
    }
}

// MARK: - Error Types

public enum SpeechRecognitionError: Error, LocalizedError {
    case notReady
    case notAuthorized
    case recognizerUnavailable
    case audioEngineNotReady
    case setupFailed(Error)
    
    public var errorDescription: String? {
        switch self {
        case .notReady:
            return "Speech recognition not ready. Call preInitialize() first."
        case .notAuthorized:
            return "Speech recognition not authorized."
        case .recognizerUnavailable:
            return "Speech recognizer not available."
        case .audioEngineNotReady:
            return "Audio engine not ready."
        case .setupFailed(let error):
            return "Speech recognition setup failed: \(error.localizedDescription)"
        }
    }
}
