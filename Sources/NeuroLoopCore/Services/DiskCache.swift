import Foundation

/// Disk-based cache implementation for persistent storage
actor DiskCache {
    
    // MARK: - Properties
    
    private let capacity: Int
    private let cacheDirectory: URL
    private let metadataFile: URL
    private var metadata: [String: CacheMetadata] = [:]
    
    // MARK: - Cache Metadata
    
    private struct CacheMetadata: Codable {
        let key: String
        let fileName: String
        let size: Int
        let createdAt: Date
        let expirationDate: Date?
        let accessCount: Int
        
        var isExpired: Bool {
            guard let expiration = expirationDate else { return false }
            return Date() > expiration
        }
    }
    
    // MARK: - Initialization
    
    init(capacity: Int) {
        self.capacity = capacity
        
        // Create cache directory
        let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        self.cacheDirectory = cacheDir.appendingPathComponent("NeuroLoopCache")
        self.metadataFile = cacheDirectory.appendingPathComponent("metadata.json")
        
        // Create directory if needed
        try? FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        
        // Load existing metadata
        loadMetadata()
        
        print("[DiskCache] Initialized with capacity: \(capacity) at \(cacheDirectory.path)")
    }
    
    // MARK: - Cache Operations
    
    /// Store data to disk
    func store(_ data: Data, forKey key: String, expirationDate: Date?) async {
        let fileName = generateFileName(for: key)
        let fileURL = cacheDirectory.appendingPathComponent(fileName)
        
        do {
            // Write data to file
            try data.write(to: fileURL)
            
            // Update metadata
            let meta = CacheMetadata(
                key: key,
                fileName: fileName,
                size: data.count,
                createdAt: Date(),
                expirationDate: expirationDate,
                accessCount: 1
            )
            
            metadata[key] = meta
            
            // Enforce capacity limits
            await enforceCapacity()
            
            // Save metadata
            saveMetadata()
            
            print("[DiskCache] Stored \(key) (\(data.count) bytes)")
            
        } catch {
            print("[DiskCache] Error storing \(key): \(error)")
        }
    }
    
    /// Retrieve data from disk
    func retrieve(forKey key: String) async -> Data? {
        guard let meta = metadata[key] else {
            return nil
        }
        
        // Check if expired
        if meta.isExpired {
            await remove(forKey: key)
            return nil
        }
        
        let fileURL = cacheDirectory.appendingPathComponent(meta.fileName)
        
        do {
            let data = try Data(contentsOf: fileURL)
            
            // Update access count
            let updatedMeta = CacheMetadata(
                key: meta.key,
                fileName: meta.fileName,
                size: meta.size,
                createdAt: meta.createdAt,
                expirationDate: meta.expirationDate,
                accessCount: meta.accessCount + 1
            )
            
            metadata[key] = updatedMeta
            saveMetadata()
            
            print("[DiskCache] Retrieved \(key) (\(data.count) bytes)")
            return data
            
        } catch {
            print("[DiskCache] Error retrieving \(key): \(error)")
            // Remove corrupted entry
            await remove(forKey: key)
            return nil
        }
    }
    
    /// Remove data from disk
    func remove(forKey key: String) async {
        guard let meta = metadata[key] else { return }
        
        let fileURL = cacheDirectory.appendingPathComponent(meta.fileName)
        
        do {
            try FileManager.default.removeItem(at: fileURL)
            metadata.removeValue(forKey: key)
            saveMetadata()
            print("[DiskCache] Removed \(key)")
        } catch {
            print("[DiskCache] Error removing \(key): \(error)")
        }
    }
    
    /// Clear all cached data
    func clearAll() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
            
            metadata.removeAll()
            saveMetadata()
            
            print("[DiskCache] Cleared all cached data")
            
        } catch {
            print("[DiskCache] Error clearing cache: \(error)")
        }
    }
    
    /// Clean up expired entries
    func cleanupExpired() async {
        let expiredKeys = metadata.compactMap { key, meta in
            meta.isExpired ? key : nil
        }
        
        for key in expiredKeys {
            await remove(forKey: key)
        }
        
        if !expiredKeys.isEmpty {
            print("[DiskCache] Cleaned up \(expiredKeys.count) expired entries")
        }
    }
    
    /// Get cache statistics
    func getStatistics() async -> DiskCacheStatistics {
        let totalSize = metadata.values.reduce(0) { $0 + $1.size }
        let totalFiles = metadata.count
        let expiredCount = metadata.values.filter { $0.isExpired }.count
        
        return DiskCacheStatistics(
            totalFiles: totalFiles,
            totalSize: totalSize,
            expiredFiles: expiredCount,
            capacity: capacity
        )
    }
    
    // MARK: - Private Helpers
    
    private func generateFileName(for key: String) -> String {
        // Create a safe filename from the key
        let hash = key.data(using: .utf8)?.base64EncodedString() ?? UUID().uuidString
        return hash.replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "+", with: "-")
            .prefix(50) + ".cache"
    }
    
    private func enforceCapacity() async {
        guard metadata.count > capacity else { return }
        
        // Sort by access count and creation date (LRU)
        let sortedEntries = metadata.sorted { entry1, entry2 in
            if entry1.value.accessCount != entry2.value.accessCount {
                return entry1.value.accessCount < entry2.value.accessCount
            }
            return entry1.value.createdAt < entry2.value.createdAt
        }
        
        // Remove least recently used entries
        let entriesToRemove = sortedEntries.prefix(metadata.count - capacity)
        
        for (key, _) in entriesToRemove {
            await remove(forKey: key)
        }
        
        print("[DiskCache] Enforced capacity, removed \(entriesToRemove.count) entries")
    }
    
    private func loadMetadata() {
        do {
            let data = try Data(contentsOf: metadataFile)
            metadata = try JSONDecoder().decode([String: CacheMetadata].self, from: data)
            print("[DiskCache] Loaded metadata for \(metadata.count) entries")
        } catch {
            print("[DiskCache] No existing metadata found or error loading: \(error)")
            metadata = [:]
        }
    }
    
    private func saveMetadata() {
        do {
            let data = try JSONEncoder().encode(metadata)
            try data.write(to: metadataFile)
        } catch {
            print("[DiskCache] Error saving metadata: \(error)")
        }
    }
}

// MARK: - Statistics

public struct DiskCacheStatistics {
    public let totalFiles: Int
    public let totalSize: Int
    public let expiredFiles: Int
    public let capacity: Int
    
    public var utilizationPercentage: Double {
        guard capacity > 0 else { return 0.0 }
        return Double(totalFiles) / Double(capacity) * 100.0
    }
    
    public var averageFileSize: Double {
        guard totalFiles > 0 else { return 0.0 }
        return Double(totalSize) / Double(totalFiles)
    }
}
