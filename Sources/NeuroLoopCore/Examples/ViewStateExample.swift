import Foundation
import SwiftUI
import NeuroLoopInterfaces

/// Example demonstrating advanced view state management integration
@available(iOS 17.0, macOS 14.0, *)
public struct ViewStateExample: View {
    
    // MARK: - View State Management
    
    @ViewState(viewId: "example_view", key: "counter", default: 0)
    private var counter: Int
    
    @ViewState(viewId: "example_view", key: "text", default: "")
    private var text: String
    
    @ViewState(viewId: "example_view", key: "isEnabled", default: true)
    private var isEnabled: Bool
    
    // MARK: - Traditional State (for comparison)
    
    @State private var traditionalCounter = 0
    @State private var showingStateReport = false
    
    // MARK: - Body
    
    public var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    
                    // MARK: - Managed State Section
                    
                    GroupBox("Managed State (Persistent & Undoable)") {
                        VStack(spacing: 15) {
                            
                            HStack {
                                Text("Counter:")
                                Spacer()
                                Text("\(counter)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                            }
                            
                            HStack {
                                Button("Decrement") {
                                    counter -= 1
                                }
                                .disabled(!isEnabled)
                                
                                Spacer()
                                
                                Button("Increment") {
                                    counter += 1
                                }
                                .disabled(!isEnabled)
                            }
                            
                            TextField("Enter text", text: Binding(
                                get: { text },
                                set: { text = $0 }
                            ))
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .disabled(!isEnabled)
                            
                            Toggle("Controls Enabled", isOn: Binding(
                                get: { isEnabled },
                                set: { isEnabled = $0 }
                            ))
                        }
                        .padding()
                    }
                    
                    // MARK: - Traditional State Section
                    
                    GroupBox("Traditional State (Non-persistent)") {
                        VStack(spacing: 15) {
                            
                            HStack {
                                Text("Counter:")
                                Spacer()
                                Text("\(traditionalCounter)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                            }
                            
                            HStack {
                                Button("Decrement") {
                                    traditionalCounter -= 1
                                }
                                
                                Spacer()
                                
                                Button("Increment") {
                                    traditionalCounter += 1
                                }
                            }
                        }
                        .padding()
                    }
                    
                    // MARK: - State Management Controls
                    
                    GroupBox("State Management") {
                        VStack(spacing: 10) {
                            
                            HStack {
                                Button("Create Snapshot") {
                                    $counter.createSnapshot(description: "Manual snapshot at counter: \(counter)")
                                }
                                
                                Spacer()
                                
                                Button("Clear State") {
                                    $counter.clearState()
                                }
                            }
                            
                            HStack {
                                Button("Undo") {
                                    _ = ViewStateManager.shared.undo()
                                }
                                .disabled(!ViewStateManager.shared.canUndo)
                                
                                Spacer()
                                
                                Button("Redo") {
                                    _ = ViewStateManager.shared.redo()
                                }
                                .disabled(!ViewStateManager.shared.canRedo)
                            }
                            
                            Button("Show State Report") {
                                showingStateReport = true
                            }
                        }
                        .padding()
                    }
                    
                    // MARK: - State Information
                    
                    GroupBox("State Information") {
                        VStack(alignment: .leading, spacing: 8) {
                            
                            HStack {
                                Text("View Active:")
                                Spacer()
                                Text($counter.isActive ? "Yes" : "No")
                                    .foregroundColor($counter.isActive ? .green : .red)
                            }
                            
                            HStack {
                                Text("State Dirty:")
                                Spacer()
                                Text($counter.isDirty ? "Yes" : "No")
                                    .foregroundColor($counter.isDirty ? .orange : .green)
                            }
                            
                            HStack {
                                Text("Last Updated:")
                                Spacer()
                                Text($counter.lastUpdated, style: .time)
                                    .font(.caption)
                            }
                            
                            HStack {
                                Text("Can Undo:")
                                Spacer()
                                Text(ViewStateManager.shared.canUndo ? "Yes" : "No")
                                    .foregroundColor(ViewStateManager.shared.canUndo ? .green : .red)
                            }
                            
                            HStack {
                                Text("Can Redo:")
                                Spacer()
                                Text(ViewStateManager.shared.canRedo ? "Yes" : "No")
                                    .foregroundColor(ViewStateManager.shared.canRedo ? .green : .red)
                            }
                        }
                        .padding()
                    }
                }
                .padding()
            }
            .navigationTitle("View State Example")
            .managedViewState(
                viewId: "example_view",
                persistKeys: ["counter", "text", "isEnabled"]
            )
        }
        .sheet(isPresented: $showingStateReport) {
            StateReportView()
        }
    }
}

// MARK: - State Report View

@available(iOS 17.0, macOS 14.0, *)
private struct StateReportView: View {
    
    @Environment(\.dismiss) private var dismiss
    @State private var reportText = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    
                    Text("View State Management Report")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(reportText)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("State Report")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        } // NavigationView closing brace
        .onAppear {
            generateReport()
        }
    } // body closing brace
    
    @MainActor
    private func generateReport() {
        let stateManager = ViewStateManager.shared
        let analytics = stateManager.getStateAnalytics()

        reportText = """
        === View State Management Report ===
        
        App State:
        - Selected Tab: \(stateManager.appState.selectedTab)
        - Current Affirmation: \(stateManager.appState.currentAffirmationId?.uuidString ?? "None")
        - Session Active: \(stateManager.appState.sessionState.isActive)
        - Last Active: \(stateManager.appState.lastActiveDate)
        
        View States:
        - Total Views: \(stateManager.viewStates.count)
        - Active Views: \(stateManager.viewStates.values.filter { $0.isActive }.count)
        - Dirty Views: \(stateManager.viewStates.values.filter { $0.isDirty }.count)
        
        History:
        - Total Snapshots: \(stateManager.stateHistory.count)
        - Can Undo: \(stateManager.canUndo)
        - Can Redo: \(stateManager.canRedo)
        
        Metrics:
        - Total State Changes: \(stateManager.stateMetrics.totalStateChanges)
        - Undo Operations: \(stateManager.stateMetrics.undoOperations)
        - Redo Operations: \(stateManager.stateMetrics.redoOperations)
        - Persistence Operations: \(stateManager.stateMetrics.persistenceOperations)
        - Average State Size: \(String(format: "%.1f", stateManager.stateMetrics.averageStateSize)) bytes
        - Compression Ratio: \(String(format: "%.2f", stateManager.stateMetrics.compressionRatio * 100))%
        
        Analytics:
        - Total State Changes: \(analytics.totalStateChanges)
        - Most Active Views: \(analytics.mostActiveViews.prefix(3).joined(separator: ", "))
        - Session Duration: \(String(format: "%.1f", analytics.sessionMetrics.totalSessionTime / 60)) minutes
        - Average View Duration: \(String(format: "%.2f", analytics.sessionMetrics.averageViewDuration))s
        - State Changes per Session: \(String(format: "%.1f", analytics.sessionMetrics.stateChangesPerSession))
        
        Performance:
        - Average Update Time: \(String(format: "%.3f", analytics.performanceSummary.averageStateUpdateTime))s
        - Slowest Update: \(String(format: "%.3f", analytics.performanceSummary.slowestStateUpdate))s
        - Fastest Update: \(String(format: "%.3f", analytics.performanceSummary.fastestStateUpdate))s
        
        Recent State History:
        \(stateManager.stateHistory.suffix(5).map { "- \($0.timestamp): \($0.description)" }.joined(separator: "\n"))
        """
    }
}

// MARK: - Preview

@available(iOS 17.0, macOS 14.0, *)
struct ViewStateExample_Previews: PreviewProvider {
    static var previews: some View {
        ViewStateExample()
    }
}
