import Foundation

/// Errors that can occur in the affirmation service
public enum AffirmationServiceError: LocalizedError {
    case networkError
    case invalidData
    case notFound
    case unauthorized
    case unknown

    public var errorDescription: String? {
        switch self {
        case .networkError:
            return "Network connection error"
        case .invalidData:
            return "Invalid data received"
        case .notFound:
            return "Affirmation not found"
        case .unauthorized:
            return "Unauthorized access"
        case .unknown:
            return "Unknown error occurred"
        }
    }
}

/// Errors that can occur in the repetition service
public enum RepetitionServiceError: LocalizedError {
    case tooManyRepetitions
    case invalidRepetition
    case unknown

    public var errorDescription: String? {
        switch self {
        case .tooManyRepetitions:
            return "Maximum daily repetitions reached"
        case .invalidRepetition:
            return "Invalid repetition attempt"
        case .unknown:
            return "Unknown error occurred"
        }
    }
}
