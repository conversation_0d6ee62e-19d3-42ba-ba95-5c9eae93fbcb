import Foundation
import NeuroLoopTypes

public struct AffirmationStub: AffirmationProtocol, Identifiable, Equatable {
    public let id: UUID
    public var text: String
    public var category: AffirmationCategory
    public var recordingURL: URL?
    public var createdAt: Date
    public var updatedAt: Date
    public var currentCycleDay: Int
    public var cycleStartDate: Date?
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var dailyProgress: [Date: Int]
    public var lastRepetitionDate: Date?
    public var energyLevel: Double
    public var moodRating: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var isCurrentCycleComplete: Bool
    public var todayProgress: Double
    public var cycleProgress: Double
    public var hasTodayQuotaMet: Bool
    public var canPerformRepetition: Bool { true }
    public var hasRecording: Bool { recordingURL != nil }
    public var longestStreak: Int

    public func recordRepetition() throws {}
    public func updateEnergyLevel(_ level: Double) {}
    public func recordMood(_ rating: Int, notes: String?) {}

    public init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        currentCycleDay: Int = 1,
        cycleStartDate: Date? = nil,
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        dailyProgress: [Date: Int] = [:],
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.0,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        isCurrentCycleComplete: Bool = false,
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0,
        hasTodayQuotaMet: Bool = false,
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.dailyProgress = dailyProgress
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.isCurrentCycleComplete = isCurrentCycleComplete
        self.todayProgress = todayProgress
        self.cycleProgress = cycleProgress
        self.hasTodayQuotaMet = hasTodayQuotaMet
        self.longestStreak = longestStreak
    }

    public static nonisolated func == (lhs: AffirmationStub, rhs: AffirmationStub) -> Bool {
        lhs.id == rhs.id
    }
}
