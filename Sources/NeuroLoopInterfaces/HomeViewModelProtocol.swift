import Foundation
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public protocol HomeViewModelProtocol: ObservableObject {
    var currentAffirmation: (any AffirmationProtocol)? { get }
    var affirmations: [any AffirmationProtocol] { get }
    var isLoading: Bool { get }
    var error: Error? { get }

    func loadData() async
    func refreshData() async
    func recordRepetition(for affirmation: any AffirmationProtocol) async
}
