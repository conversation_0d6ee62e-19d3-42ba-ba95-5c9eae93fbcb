import Foundation

public protocol StreakServiceProtocol {
    func validateStreaks() async throws -> StreakValidationResult
    func getStreakStatistics() async throws -> StreakStatistics
    func getStreakCalendarData(for affirmation: any AffirmationProtocol, numberOfDays: Int) async
        -> [StreakCalendarDay]
    func isStreakAtRisk(for affirmation: any AffirmationProtocol) async -> Bool
    func updateStreak(for affirmation: any AffirmationProtocol) async throws
    func getStreakInfo(for affirmation: any AffirmationProtocol) async -> StreakInfo
    func getStreakCalendar(for affirmation: any AffirmationProtocol) async -> [StreakCalendarDay]
    func validateStreak(for affirmation: any AffirmationProtocol) async -> Bool
    func resetStreak(for affirmation: any AffirmationProtocol) async throws
}

public struct StreakValidationResult: Sendable {
    public let success: Bool
    public let error: Error?
    public let validatedAffirmations: Int
    public let brokenStreaks: Int
    public init(
        success: Bool,
        error: Error? = nil,
        validatedAffirmations: Int,
        brokenStreaks: Int
    ) {
        self.success = success
        self.error = error
        self.validatedAffirmations = validatedAffirmations
        self.brokenStreaks = brokenStreaks
    }
}

public struct StreakStatistics: Sendable {
    public var totalAffirmations: Int = 0
    public var activeStreaks: Int = 0
    public var completedCycles: Int = 0
    public var longestCurrentStreak: Int = 0
    public var longestStreakAffirmationId: UUID?
    public var averageStreakLength: Double = 0.0
    public init() {}
}
