import Foundation
import NeuroLoopTypes

/// Protocol for affirmation objects
@MainActor
public protocol AffirmationProtocol: Identifiable, Equatable, Sendable {
    /// Unique identifier
    var id: UUID { get }

    /// The affirmation text
    var text: String { get }

    /// The category of the affirmation
    var category: AffirmationCategory { get }

    /// Whether the affirmation is marked as favorite
    var isFavorite: Bool { get }

    /// Whether the affirmation has a recording
    var hasRecording: Bool { get }

    /// Progress for today's repetitions (0.0 to 1.0)
    var todayProgress: Double { get }

    /// Progress for the current cycle (0.0 to 1.0)
    var cycleProgress: Double { get }

    /// URL to the audio recording
    var recordingURL: URL? { get }

    /// Number of completed cycles
    var completedCycles: Int { get }

    /// Number of repetitions performed today
    var currentRepetitions: Int { get }

    /// Date when the last repetition was performed
    var lastRepetitionDate: Date? { get }

    /// Energy level associated with the affirmation
    var energyLevel: Double { get }

    /// Mood rating associated with the affirmation
    var moodRating: Int? { get }

    /// Additional notes
    var notes: String? { get }

    /// Number of times the affirmation has been played
    var playCount: Int { get }

    /// Whether there's an active cycle
    var hasActiveCycle: Bool { get }

    /// Current day in the affirmation cycle
    var currentCycleDay: Int { get }

    /// Date when the current cycle started
    var cycleStartDate: Date? { get }

    /// Progress for each day
    var dailyProgress: [Date: Int] { get }

    /// Whether the current cycle is complete
    var isCurrentCycleComplete: Bool { get }

    /// Whether today's repetition quota has been met
    var hasTodayQuotaMet: Bool { get }

    /// Whether a repetition can be performed now
    var canPerformRepetition: Bool { get }

    /// Date when the affirmation was created
    var createdAt: Date { get }

    /// Date when the affirmation was last updated
    var updatedAt: Date { get }

    /// Longest streak achieved for this affirmation
    var longestStreak: Int { get set }

    /// Record a repetition of the affirmation
    func recordRepetition() throws

    /// Update the energy level for the affirmation
    func updateEnergyLevel(_ level: Double)

    /// Record mood rating and optional notes
    func recordMood(_ rating: Int, notes: String?)
}
