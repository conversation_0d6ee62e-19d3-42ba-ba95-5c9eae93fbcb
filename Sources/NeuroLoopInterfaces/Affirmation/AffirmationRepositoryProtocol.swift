import Foundation
import NeuroLoopTypes

/// Protocol for affirmation repository operations
@MainActor
public protocol AffirmationRepositoryProtocol: Sendable {
    /// Fetch all affirmations
    func fetchAffirmations() async throws -> [any AffirmationProtocol]
    
    /// Fetch affirmations by category
    func fetchAffirmations(category: AffirmationCategory) async throws -> [any AffirmationProtocol]
    
    /// Fetch favorite affirmations
    func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol]
    
    /// Fetch a specific affirmation by ID
    func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)?
    
    /// Save an affirmation
    func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws

    /// Update an affirmation
    func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws
    
    /// Delete an affirmation
    func deleteAffirmation(id: UUID) async throws
    
    /// Update affirmation progress
    func updateProgress(for affirmationId: UUID, repetitions: Int, date: Date) async throws
    
    /// Get affirmation statistics
    func getStatistics(for affirmationId: UUID) async throws -> AffirmationStatistics
    
    /// Search affirmations by text
    func searchAffirmations(query: String) async throws -> [any AffirmationProtocol]
    
    /// Get affirmations sorted by option
    func getAffirmations(sortedBy option: AffirmationSortOption) async throws -> [any AffirmationProtocol]
    
    /// Update affirmation favorite status
    func updateFavoriteStatus(for affirmationId: UUID, isFavorite: Bool) async throws
    
    /// Get daily progress for an affirmation
    func getDailyProgress(for affirmationId: UUID, date: Date) async throws -> Int
    
    /// Update energy level for an affirmation
    func updateEnergyLevel(for affirmationId: UUID, level: Double) async throws
    
    /// Record mood for an affirmation
    func recordMood(for affirmationId: UUID, rating: Int, notes: String?) async throws
    
    /// Get affirmations with active cycles
    func getAffirmationsWithActiveCycles() async throws -> [any AffirmationProtocol]
    
    /// Complete a cycle for an affirmation
    func completeCycle(for affirmationId: UUID) async throws
    
    /// Start a new cycle for an affirmation
    func startNewCycle(for affirmationId: UUID) async throws
    
    /// Get streak information for an affirmation
    func getStreakInfo(for affirmationId: UUID) async throws -> (currentStreak: Int, longestStreak: Int)
    
    /// Update longest streak for an affirmation
    func updateLongestStreak(for affirmationId: UUID, streak: Int) async throws

    /// Create a new affirmation
    func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> any AffirmationProtocol

    /// Fetch the current active affirmation
    func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)?

    /// Start a cycle for an affirmation
    func startCycle(for affirmation: any AffirmationProtocol) async throws

    /// Record a repetition for an affirmation
    func recordRepetition(for affirmation: any AffirmationProtocol) async throws
}
