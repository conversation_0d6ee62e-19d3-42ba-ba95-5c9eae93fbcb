import Foundation
import NeuroLoopTypes

/// Protocol defining the contract for affirmation repositories
public protocol AffirmationRepositoryProtocol: Sendable {
    /// Fetch all affirmations
    func fetchAffirmations() async throws -> [any AffirmationProtocol]

    /// Fetch an affirmation by ID
    func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)?

    /// Create a new affirmation
    func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?
    ) async throws -> any AffirmationProtocol

    /// Update an existing affirmation
    func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws

    /// Delete an affirmation
    func deleteAffirmation(id: UUID) async throws

    /// Fetch affirmations by category
    func fetchAffirmations(category: AffirmationCategory) async throws -> [any AffirmationProtocol]

    /// Fetch favorite affirmations
    func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol]

    /// Fetch current active affirmation
    func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)?

    /// Record a repetition for an affirmation
    func recordRepetition(for affirmation: any AffirmationProtocol) async throws

    /// Start a new cycle for an affirmation
    func startCycle(for affirmation: any AffirmationProtocol) async throws
}
