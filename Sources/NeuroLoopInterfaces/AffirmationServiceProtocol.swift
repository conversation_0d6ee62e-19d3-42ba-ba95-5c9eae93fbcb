import Foundation
import NeuroLoopTypes

public protocol AffirmationServiceProtocol: Sendable {
    func fetchAffirmations() async throws -> [any AffirmationProtocol]
    func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)?
    func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws
    func deleteAffirmation(id: UUID) async throws
    func fetchAffirmations(category: AffirmationCategory) async throws -> [any AffirmationProtocol]
    func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol]
    func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)?
    func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    func startCycle(for affirmation: any AffirmationProtocol) async throws
    func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    func getStatistics() async throws -> AffirmationStatistics
}
