import NeuroLoopTypes
import SwiftUI

@MainActor
public class NavigationCoordinator: ObservableObject {
    @Published public var homePath: [NavigationRoute] = []
    @Published public var journalPath: [NavigationRoute] = []
    @Published public var settingsPath: [NavigationRoute] = []

    public init() {}

    public func navigate(to route: NavigationRoute, in tab: Tab) {
        switch tab {
        case .home:
            homePath.append(route)
        case .journal:
            journalPath.append(route)
        case .settings:
            settingsPath.append(route)
        }
    }

    public func popToRoot(in tab: Tab) {
        switch tab {
        case .home:
            homePath.removeAll()
        case .journal:
            journalPath.removeAll()
        case .settings:
            settingsPath.removeAll()
        }
    }

    public func popLast(in tab: Tab) {
        switch tab {
        case .home:
            _ = homePath.popLast()
        case .journal:
            _ = journalPath.popLast()
        case .settings:
            _ = settingsPath.popLast()
        }
    }
}

public enum Tab {
    case home
    case journal
    case settings
}

public enum NavigationRoute: <PERSON>hable {
    case affirmationDetail(id: UUID)
    case premium
    case theme
    case settings
    case profile
    case help
    case about
}
