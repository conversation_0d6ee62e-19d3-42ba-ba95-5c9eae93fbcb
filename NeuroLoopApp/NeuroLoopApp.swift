import SwiftUI
import NeuroLoop<PERSON>ore
import NeuroLoopUI
import NeuroLoopTypes
import NeuroLoopShared
import NeuroLoopInterfaces
import Foundation

@MainActor
class StartupPerformanceLog {
    static let shared = StartupPerformanceLog()
    private var events: [(String, TimeInterval)] = []
    private var startTime: Date?
    private init() {}
    func mark(_ label: String) {
        let now = Date()
        if startTime == nil { startTime = now }
        let elapsed = now.timeIntervalSince(startTime ?? now)
        events.append((label, elapsed))
        print("[Startup] \(label): \(elapsed)s")
    }
    func printSummary() {
        print("--- Startup Performance Summary ---")
        for (label, elapsed) in events {
            print("[Startup] \(label): \(elapsed)s")
        }
        if let total = events.last?.1 {
            print("[Startup] Total to interactive: \(total)s")
        }
        print("-----------------------------------")
    }
}

@MainActor
class StartupCoordinator {
    static let shared = StartupCoordinator()
    private init() {}

    func start() {
        StartupPerformanceLog.shared.mark("StartupCoordinator.start")
        // Initialize critical services synchronously
        do {
            _ = try ServiceFactory.shared.getServices()
            StartupPerformanceLog.shared.mark("Critical services initialized")
        } catch {
            print("[Startup] Error initializing critical services: \(error)")
        }
    }
}

@main
struct NeuroLoopApp: App {
    // Use production mode with all Phase 2 features
    private let useProductionMode = true

    @StateObject private var navigationCoordinator = NeuroLoopShared.NavigationCoordinator()
    @StateObject private var themeManager = NeuroLoopCore.ThemeManager.shared
    @StateObject private var hapticManager = NeuroLoopTypes.HapticManager.shared

    init() {
        StartupPerformanceLog.shared.mark("App init")
        StartupCoordinator.shared.start()
    }

    var body: some Scene {
        WindowGroup {
            if #available(iOS 17.0, macOS 14.0, *) {
                if useProductionMode {
                    // PRODUCTION MODE with Phase 2 Advanced Data Architecture
                    RootView()
                        .environmentObject(navigationCoordinator)
                        .environmentObject(themeManager)
                        .environmentObject(hapticManager)
                        .onAppear {
                            StartupPerformanceLog.shared.mark("RootView appeared")
                            print("🚀 NeuroLoop Production Mode: Phase 2 Advanced Data Architecture Active")
                        }
                } else {
                    // PREVIEW MODE
                    MainTabView.preview()
                        .environmentObject(navigationCoordinator)
                        .environmentObject(themeManager)
                        .environmentObject(hapticManager)
                        .onAppear {
                            StartupPerformanceLog.shared.mark("MainTabView appeared")
                        }
                }
            } else {
                Text("This app requires iOS 17.0 or later")
                    .foregroundColor(.red)
                    .padding()
            }
        }
    }
}
