<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>Neuro Loop 100</string>
	<key>CFBundleExecutable</key>
	<string>NeuroLoopApp</string>
	<key>CFBundleIdentifier</key>
	<string>com.neuroloop.NeuroLoopApp</string>
	<key>CFBundleName</key>
	<string>Neuro Loop 100</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>2</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>NeuroLoop needs access to your microphone to record your spoken affirmations.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Neur<PERSON><PERSON><PERSON> needs access to speech recognition to verify your spoken affirmations.</string>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string></string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string></string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
