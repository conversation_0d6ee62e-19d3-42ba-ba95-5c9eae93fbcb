import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * DiagnosticAppEntry
 *
 * Entry point for the diagnostic app. This file provides instructions on how to use
 * the diagnostic tools to fix the repetition counter issue.
 */

@available(iOS 17.0, macOS 14.0, *)
@main
struct DiagnosticAppEntry: App {
    @StateObject private var themeManager = ThemeManager.shared

    var body: some Scene {
        WindowGroup {
            DiagnosticApp()
                .environmentObject(themeManager)
                .onAppear {
                    print("DiagnosticAppEntry: App started")
                    printInstructions()
                }
        }
    }

    private func printInstructions() {
        print("""

        ===== NEUROLOOP DIAGNOSTIC APP =====

        This app helps diagnose and fix issues with the repetition counter in the NeuroLoop app.

        INSTRUCTIONS:

        1. Use the "Diagnostics" tab to test the microphone and repetition service.
        2. Use the "Affirmation" tab to test the SpeakAffirmation implementation.

        FEATURES IMPLEMENTED:

        1. Fixed double repetition counting issue with session-based tracking.
        2. Modern glassmorphic notification system with haptic feedback.
        3. Contextual icons and swipe-to-dismiss gestures.
        4. Dynamic milestone celebrations and varied messaging.
        5. Consolidated single source of truth architecture.

        =====================================

        """)
    }
}
